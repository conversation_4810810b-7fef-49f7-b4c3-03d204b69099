{"name": "aktivate-fr-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:host": "vite --host", "type-check": "tsc --project tsconfig.app.json --watch", "dev:ts": "concurrently \"npm run dev\" \"npm run type-check\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "[ -n \"$CI\" ] || [ -n \"$DOCKER\" ] || husky", "test": "jest"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.0.0", "@mui/icons-material": "^6.4.4", "@mui/material": "^6.4.4", "@mui/x-data-grid": "^7.27.3", "@mui/x-date-pickers": "^8.1.0", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.0.0", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-hard-break": "^2.12.0", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "axios": "^1.7.9", "classnames": "^2.5.1", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "jest-environment-jsdom": "^29.7.0", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-confirm": "^0.3.0", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.54.2", "react-image-crop": "^11.0.10", "react-qr-code": "^2.0.15", "react-router": "^7.1.5", "react-router-dom": "^7.1.5", "react-share": "^5.2.2", "react-toastify": "^11.0.3", "simplebar-react": "^3.3.2", "tailwindcss": "^4.0.6", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/js": "^9.19.0", "@feature-sliced/steiger-plugin": "^0.5.5", "@tanstack/eslint-plugin-query": "^5.66.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-helmet": "^6.1.11", "@types/react-image-crop": "^8.1.6", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.1.2", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "msw": "^2.7.3", "prettier": "^3.5.0", "steiger": "^0.5.6", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}, "msw": {"workerDirectory": ["public"]}}