name: PR Checks

on:
  pull_request:
    branches:
      - main
      - dev

jobs:
  build-and-lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"
          cache: "npm"

      - name: Cache node modules
        uses: actions/cache@v3
        id: npm-cache
        with:
          path: |
            **/node_modules
            ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install Dependencies
        if: steps.npm-cache.outputs.cache-hit != 'true'
        run: npm ci

      - name: Ts compile
        run: npx tsc --project tsconfig.app.json

      - name: Run Staiger
        run: npx steiger ./src --fix --fail-on-warnings

      - name: Build Project
        run: npm run build
