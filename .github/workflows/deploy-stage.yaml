name: Deploy Dev Branch

on:
  push:
    branches:
      - dev

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Authenticate with G<PERSON>
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_STAGING_FR }}

      - name: Set up gcloud CLI
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: SSH into Compute Engine and Run Command
        run: |
          gcloud compute ssh 281542149312-compute@aktivate-raise-staging \
            --zone=us-central1-f \
            --command="
                sudo chown -R \$(whoami):\$(whoami) /home/<USER>/FE &&
                cd FE &&
                git fetch origin &&
                git checkout dev &&
                git pull origin dev &&
                npm ci &&
                npm run build &&
                docker run --rm -v fe_web-dist:/dist alpine sh -c \"rm -rf /dist/*\" &&
                docker run --rm -v /home/<USER>/FE/dist:/src -v fe_web-dist:/dist alpine sh -c \"cp -r /src/* /dist/\""

