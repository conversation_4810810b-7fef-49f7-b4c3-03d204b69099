import { useQuery } from "@tanstack/react-query"
import { messageTemplateApi } from "../api/messageTemplate.queries"
import {
  CreateMessageTemplateDto,
  UpdateMessageTemplateDto,
} from "../api/dto/messageTemplate.dto.ts"

export const useGetMessageTemplates = () => {
  return useQuery({
    queryKey: ["getMessageTemplates"],
    queryFn: () => messageTemplateApi.getAll(),
  })
}

export const useGetMessageTemplateById = (id: number) => {
  return useQuery({
    queryKey: ["getMessageTemplate", id],
    queryFn: () => messageTemplateApi.getById(id),
  })
}

export const useCreateMessageTemplate = (body: CreateMessageTemplateDto) => {
  return useQuery({
    queryKey: ["createMessageTemplate"],
    queryFn: () => messageTemplateApi.create(body),
  })
}

export const useUpdateMessageTemplate = (id: number, body: UpdateMessageTemplateDto) => {
  return useQuery({
    queryKey: ["updateMessageTemplate", id],
    queryFn: () => messageTemplateApi.update(id, body),
  })
}

export const useDeleteMessageTemplate = (id: number) => {
  return useQuery({
    queryKey: ["deleteMessageTemplate", id],
    queryFn: () => messageTemplateApi.delete(id),
  })
}