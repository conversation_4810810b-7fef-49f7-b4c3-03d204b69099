export interface MessageTemplateDto {
  id: number
  template_type_id: number
  channel: string
  template_name: string
  subject: string
  body: string
}

export interface CreateMessageTemplateDto {
  template_type_id: number
  channel: string
  template_name: string
  subject: string
  body: string
}

export interface UpdateMessageTemplateDto {
  template_type_id: number
  channel: string
  template_name: string
  subject: string
  body: string
}