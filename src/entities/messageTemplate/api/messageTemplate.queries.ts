import { API } from "@/shared/api"
import {
  CreateMessageTemplateDto,
  MessageTemplateDto,
  UpdateMessageTemplateDto,
} from "./dto/messageTemplate.dto"

export const messageTemplateApi = {
  getAll: async (): Promise<MessageTemplateDto[]> => {
    const response = await API.get(`/message-templates`)
    return response.data
  },

  getById: async (id: number): Promise<MessageTemplateDto> => {
    const response = await API.get(`/message-templates/${id}`)
    return response.data
  },

  create: async (
    body: CreateMessageTemplateDto
  ): Promise<MessageTemplateDto> => {
    const response = await API.post(`/message-templates`, body)
    return response.data
  },

  update: async (
    id: number,
    body: Partial<UpdateMessageTemplateDto>
  ): Promise<MessageTemplateDto> => {
    const response = await API.put(`/message-templates/${id}`, body)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(`/message-templates/${id}`)
    return response.data
  },
}
