import { API } from "@/shared/api"
import { RelationshipDto } from "./dto/relationship.dto"

export const relationshipApi = {
  getAll: async (): Promise<RelationshipDto[]> => {
    const { data } = await API.get<RelationshipDto[]>("/relationships")
    return data
  },

  getById: async (id: number): Promise<RelationshipDto> => {
    const { data } = await API.get<RelationshipDto>(`/relationships/${id}`)
    return data
  },

  create: async (
    relationship: Omit<RelationshipDto, "relationships_id">
  ): Promise<RelationshipDto> => {
    const { data } = await API.post<RelationshipDto>(
      "/relationships",
      relationship
    )
    return data
  },

  update: async (
    id: number,
    relationship: Partial<Omit<RelationshipDto, "relationships_id">>
  ): Promise<RelationshipDto> => {
    const { data } = await API.put<RelationshipDto>(
      `/relationships/${id}`,
      relationship
    )
    return data
  },

  delete: async (id: number): Promise<void> => {
    await API.delete(`/relationships/${id}`)
  },
}
