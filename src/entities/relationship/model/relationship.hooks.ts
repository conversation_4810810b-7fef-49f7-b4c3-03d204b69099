import {
  useQuery,
  useMutation,
  UseMutationOptions,
  useQueryClient,
} from "@tanstack/react-query"
import { relationshipApi } from "../api/relationship.queries"
import { RelationshipDto } from "../api/dto/relationship.dto"
import { ApiError } from "@/shared/model"

export const useGetRelationships = () => {
  return useQuery({
    queryKey: ["useGetRelationships"],
    queryFn: () => relationshipApi.getAll(),
  })
}

export const useGetRelationshipById = (id: number) => {
  return useQuery({
    queryKey: ["useGetRelationshipById", id],
    queryFn: () => relationshipApi.getById(id),
    enabled: !!id,
  })
}

export const useCreateRelationship = (
  options?: UseMutationOptions<
    RelationshipDto,
    ApiError,
    Omit<RelationshipDto, "relationships_id">
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (relationship: Omit<RelationshipDto, "relationships_id">) =>
      relationshipApi.create(relationship),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["useGetRelationships"] })
    },
    ...options,
  })
}

export const useUpdateRelationship = (
  options?: UseMutationOptions<
    RelationshipDto,
    ApiError,
    {
      id: number
      relationship: Partial<Omit<RelationshipDto, "relationships_id">>
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, relationship }) =>
      relationshipApi.update(id, relationship),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["useGetRelationships"] })
      queryClient.invalidateQueries({
        queryKey: ["useGetRelationshipById", variables.id],
      })
    },
    ...options,
  })
}

export const useDeleteRelationship = (
  options?: UseMutationOptions<void, ApiError, number>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => relationshipApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["useGetRelationships"] })
    },
    ...options,
  })
}
