import { PaginationParams } from "@/shared/model"

export interface TippingPercentageDto {
  id: number
  campaign_id: number
  option_name: string
  value: string
  created_at: string
  updated_at: string
}

export interface DonationDenominationDto {
  id: number
  campaign_id: number
  amount: string
  created_at: string
  updated_at: string
}
export interface DonationDto {
  payment_status: string
  base_donation_amount: string
  donor_first_name: string
  donor_last_name: string
}

export interface CampaignDto {
  campaign_id: number
  name: string
  pin: string
  level: string
  gender: string
  sport: string
  school_org_name: string
  mascot: string
  fundraising_goal: string
  cause: string
  roster_size: number
  auto_increase_goal: boolean
  colors: {
    primary: string
    secondary: string
  }
  status: string
  campaign_owner_id: number | null
  tipping_enabled: boolean
  created_at: string
  updated_at: string
  guid: string
  team_display_name: string
  tippingPercentages: TippingPercentageDto[]
  donationDenominations: DonationDenominationDto[]
  city: string
  state: string
  zip: string
  campaign_start_date: string
  campaign_end_date: string
  our_message_text: string
  donations: DonationDto[]
  donations_raised: number
  campaign_owner: string
  donation_page_title: string
  show_leaderboard: boolean
}

export interface CampaignUserImageDto {
  id: number
  image_url: string
  image_type: string
}

export interface DonationUserDto extends CampaignDto {
  campaign_user_info: {
    donation_message: string
    user_name: string
    images: CampaignUserImageDto[]
  }
}

export interface ActiveCampaignDto {
  campaign_id: number
  name: string
}

export interface CreateCampaignDto {
  name: string
  level_id: number
  gender_id: number
  sport_id: number
  school_org_name: string
  mascot?: string
  fundraising_goal: number
  cause_id?: number
  roster_size?: number
  auto_increase_goal: boolean
  primary_color: string
  secondary_color: string
  status_id?: number
  campaign_owner_id?: number
  donation_denominations?: number[]
  city: string
  state: string
  zip: string
  campaign_start_date: string
  campaign_end_date: string
  donation_page_title: string
  show_leaderboard: boolean
}

export interface UpdateCampaignDto {
  name: string
  pin: string
  level_id: number
  gender_id: number
  sport_id: number
  school_org_name: string
  mascot?: string | null
  fundraising_goal: number
  cause_id: number | null
  roster_size: number
  auto_increase_goal: boolean
  primary_color: string
  secondary_color: string
  tipping_enabled?: boolean
  status_id?: number
  campaign_owner_id?: number
  donation_denominations?: number[]
  donation_page_title: string
  city: string
  state: string
  zip: string
  campaign_start_date: string
  campaign_end_date: string
  team_display_name: string
  our_message_text: string
  show_leaderboard: boolean
}

export interface CampaignSummaryDto {
  total_raised: number
  total_fees: number
  total_profit: number
  total_tips: number
  check_request_status: string
}

export interface QuickStatsDto {
  today: {
    donations: number
    tips: number
    total_donations: number
  }
  yesterday: {
    donations: number
    tips: number
    total_donations: number
  }
  last_7_days: {
    donations: number
    tips: number
    total_donations: number
  }
  year_to_date: {
    donations: number
    tips: number
    total_donations: number
  }
}

export interface DonationsReportDto {
  data: {
    campaign_id: number
    campaign_display_name: string
    base_donation_amount: string
    tip_amount: string
    total_donation_amount: string
    donor_full_name: string
    donor_email: string
    donor_phone_number: string
    created_at: string
  }[]
  current_page: number
  per_page: number
  total: number
  total_pages: number
}

export interface CheckRequestDto {
  data: {
    id: number
    campaign_id: number
    school_org_name: string
    campaign_display_name: string
    request_date: string
    recipient_name: string
    check_address: string
    memo: string
    notes: string
    org_contact_email: string
    org_contact_phone: string
    request_status: string
    check_amount: string
  }[]
  current_page: number
  per_page: number
  total: number
}

export interface CheckRequestFilterDto {
  statuses: string[]
}

export interface MessageLogDto {
  data: {
    campaign_user: string
    donor_name: string
    channel: string
    template: string
    send_date: string
    scheduled_at: string
    sent: boolean
  }[]
}

export interface MessageLogFilterDto {
  statuses: string[]
}

export interface TotalCheckRequestAmountDto {
  requested: number
  processed: number
  cleared: number
}

export interface CampaignFilterDto {
  name?: string | null
  owner_id?: number | null
  status_id?: number | null
}

export interface CampaignQueryParams extends PaginationParams, CampaignFilterDto {}