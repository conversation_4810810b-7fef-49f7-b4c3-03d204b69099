import { MessageLogFilterParams } from "@/entities/campaignMessageLog/@x/campaign"
import { API, apiUrls } from "@/shared/api"
import { PaginatedResponse, PaginationParams } from "@/shared/model"
import {
  CheckRequestFilterParams,
  DonationReportFilterParams,
} from "../model/types"
import {
  ActiveCampaignDto,
  CampaignDto,
  CampaignQueryParams,
  CampaignSummaryDto,
  CheckRequestDto,
  CheckRequestFilterDto,
  CreateCampaignDto,
  DonationsReportDto,
  DonationUserDto,
  MessageLogDto,
  MessageLogFilterDto,
  QuickStatsDto,
  TotalCheckRequestAmountDto,
  UpdateCampaignDto,
} from "./dto/campaign.dto"

export const campaignApi = {
  getAll: async (
    params: CampaignQueryParams
  ): Promise<PaginatedResponse<CampaignDto>> => {
    const queryParams = Object.fromEntries(
      Object.entries(params).filter(
        ([, value]) => !!value
      )
    )

    const response = await API.get("/campaigns", {
      params: queryParams,
    })

    return response.data
  },

  getById: async (id: number): Promise<CampaignDto> => {
    const response = await API.get(`/campaigns/${id}`)
    return response.data
  },

  getByGuid: async (guid: string): Promise<CampaignDto> => {
    const response = await API.get(apiUrls.campaign.byGuid(guid))
    return response.data
  },

  getAllActive: async (): Promise<ActiveCampaignDto[]> => {
    const response = await API.get(`/active-campaigns`)
    return response.data
  },

  getCampaignByToken: async (token: string): Promise<CampaignDto> => {
    const response = await API.get(
      `/campaign-invites/get-campaign-by-token?token=${token}`
    )
    return response.data
  },

  getByPin: async (pin: string): Promise<CampaignDto> => {
    const response = await API.get(`/campaign/get-campaign-by-pin?pin=${pin}`)
    return response.data
  },

  add: async (body: CreateCampaignDto): Promise<CreateCampaignDto> => {
    const response = await API.post(`/campaigns`, body)
    return response.data
  },

  update: async (
    id: number,
    body: Partial<UpdateCampaignDto>
  ): Promise<CampaignDto> => {
    const response = await API.put(`/campaigns/${id}`, body)
    return response.data
  },

  bulkCreate: async (data: FormData): Promise<unknown> => {
    const response = await API.post("/campaigns/upload", data, {
      headers: { "Content-Type": "multipart/form-data" },
    })
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(`/campaigns/${id}`)
    return response.data
  },

  getByUserId: async (
    guid: string,
    userId: number
  ): Promise<DonationUserDto> => {
    const response = await API.get(
      apiUrls.campaign.donationUser.getDonationUser(guid),
      {
        params: {
          campaign_user_id: userId,
        },
      }
    )
    return response.data
  },
  getByInviteId: async (
    guid: string,
    inviteId: number
  ): Promise<DonationUserDto> => {
    const response = await API.get(
      apiUrls.campaign.donationUser.getDonationUser(guid),
      {
        params: {
          donation_invite_id: inviteId,
        },
      }
    )
    return response.data
  },
  getCampaignSummary: async (
    campaignId: number
  ): Promise<CampaignSummaryDto> => {
    const response = await API.get(
      apiUrls.campaign.dashboard.summary(campaignId)
    )
    return response.data
  },
  getQuickStats: async (): Promise<QuickStatsDto> => {
    const response = await API.get(apiUrls.campaign.dashboard.quickStats())
    return response.data
  },
  getCampaignDonationsReport: async (
    params: PaginationParams & DonationReportFilterParams
  ): Promise<DonationsReportDto> => {
    const response = await API.get(
      apiUrls.campaign.dashboard.donationsReport(),
      {
        params: {
          page: params.page,
          per_page: params.per_page,
          start_date: params.start_date,
          end_date: params.end_date,
        },
      }
    )
    return response.data
  },
  getCheckRequests: async (
    params: PaginationParams & CheckRequestFilterParams
  ): Promise<CheckRequestDto> => {
    const response = await API.get(apiUrls.campaign.dashboard.checkRequests(), {
      params: {
        page: params.page,
        per_page: params.per_page,
        start_date: params.start_date,
        end_date: params.end_date,
        check_request_status: params.check_request_status,
      },
    })
    return response.data
  },
  getCheckRequestFilters: async (): Promise<CheckRequestFilterDto> => {
    const response = await API.get(
      apiUrls.campaign.dashboard.checkRequestFilters()
    )
    return response.data
  },
  getMessageLogs: async (
    params: PaginationParams & MessageLogFilterParams
  ): Promise<PaginatedResponse<MessageLogDto>> => {
    const response = await API.get(apiUrls.campaign.dashboard.messageLogs(), {
      params: {
        page: params.page,
        per_page: params.per_page,
        start_date: params.start_date,
        end_date: params.end_date,
        message_status: params.message_status,
      },
    })
    return response.data
  },
  getMessageLogFilters: async (): Promise<MessageLogFilterDto> => {
    const response = await API.get(
      apiUrls.campaign.dashboard.messageLogFilters()
    )
    return response.data
  },
  getCheckRequestTotalAmount: async (
    start_date: string,
    end_date: string
  ): Promise<TotalCheckRequestAmountDto> => {
    const response = await API.get(
      apiUrls.campaign.dashboard.checkRequestTotalAmount(),
      {
        params: {
          start_date,
          end_date,
        },
      }
    )
    return response.data
  },
}
