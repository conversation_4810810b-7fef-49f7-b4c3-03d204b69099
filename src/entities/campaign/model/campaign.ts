export interface Campaign {
  campaignId: number
  name: string
  pin: string
  level: string
  gender: string
  sport: string
  colors: {
    primary: string
    secondary: string
  }
  status: string
  campaignOwnerId: number | null
  createdAt: string
  updatedAt: string
  schoolOrgName: string
  mascot: string
  fundraisingGoal: number
  cause: string
  rosterSize: number
  autoIncreaseGoal: boolean
  guid: string
  city: string
  state: string
  zip: string
  campaignStartDate: string
  campaignEndDate: string,
  teamDisplayName: string,
  ourMessageText: string
}
