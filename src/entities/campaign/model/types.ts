export type DonationReportFilterParams = {
  start_date: string
  end_date: string
}

export type DonationReportFilter = {
  date_from: Date
  date_to: Date
}

export type CheckRequestFilter = {
  start_date: Date
  end_date: Date
  check_request_status: {
    label: string
    value: string
  }
}

export type CheckRequestFilterParams = {
  start_date?: string
  end_date?: string
  check_request_status: string
}
