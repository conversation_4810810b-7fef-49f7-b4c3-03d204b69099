import { MessageLogFilterParams } from "@/entities/campaignMessageLog/@x/campaign"
import { ApiError, PaginationParams, queryKeys } from "@/shared/model"
import {
  QueryOptions,
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { campaignApi } from "../api/campaign.queries"
import {
  CampaignDto,
  CreateCampaignDto,
  UpdateCampaignDto,
} from "../api/dto/campaign.dto"
import {
  CheckRequestFilterParams,
  DonationReportFilterParams,
} from "../model/types"

export const useGetCampaigns = (params: PaginationParams) => {
  return useQuery({
    queryKey: ["getCampaigns", params],
    queryFn: async () => campaignApi.getAll(params),
    refetchOnMount: true,
    staleTime: 0,
  })
}

export const useGetAllActiveCampaigns = () => {
  return useQuery({
    queryKey: ["getAllActiveCampaigns"],
    queryFn: campaignApi.getAllActive,
  })
}

export const useGetCampaignById = (id: number) => {
  return useQuery({
    queryKey: ["getCampaignById", id],
    queryFn: () => campaignApi.getById(id),
  })
}

export const useGetCampaignByToken = (token: string) => {
  return useQuery({
    queryKey: ["getCampaignByToken", token],
    queryFn: () => campaignApi.getCampaignByToken(token),
  })
}

export const useGetCampaignByPin = (
  pin: string,
  options?: QueryOptions<CampaignDto, ApiError>
) => {
  return useQuery({
    queryKey: ["getCampaignByPin", pin],
    queryFn: () => campaignApi.getByPin(pin),
    ...options,
  })
}

export const useCreateCampaign = (
  options?: UseMutationOptions<CreateCampaignDto, Error, CreateCampaignDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (campaign: CreateCampaignDto) => {
      return campaignApi.add(campaign)
    },
    onSuccess: (...args) => {
      queryClient.invalidateQueries({ queryKey: ["getCampaigns"] })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
    ...options,
  })
}

export interface UpdateCampaignParams {
  id: number
  campaign: Partial<UpdateCampaignDto>
}

export const useUpdateCampaign = (
  options?: UseMutationOptions<CampaignDto, Error, UpdateCampaignParams>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: async ({ id, campaign }: UpdateCampaignParams) => {
      return campaignApi.update(id, campaign)
    },
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ["getCampaigns"],
      })
      queryClient.invalidateQueries({
        queryKey: ["getCampaignById"],
      })

      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}

export const useDeleteCampaign = (
  options?: UseMutationOptions<void, Error, number>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (id: number) => {
      return campaignApi.delete(id)
    },
    onSuccess: (...args) => {
      queryClient.invalidateQueries({ queryKey: ["getCampaigns"] })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
    ...options,
  })
}

export const useGetCampaignByGuid = (guid: string, enabled: boolean) => {
  return useQuery({
    queryKey: [queryKeys.campaign.byGuid, guid],
    queryFn: () => campaignApi.getByGuid(guid),
    staleTime: 0,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled,
  })
}

export const useGetDonationUser = (guid: string, userId: number) => {
  return useQuery({
    queryKey: [queryKeys.campaign.donationUser.get, guid, userId],
    queryFn: () => campaignApi.getByUserId(guid, userId),
    enabled: !!userId,
  })
}

export const useGetDonationUserByInviteId = (
  guid: string,
  inviteId: number
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.donationUser.get, guid, inviteId],
    queryFn: () => campaignApi.getByInviteId(guid, inviteId),
    enabled: !!inviteId,
  })
}

export const useGetCampaignSummary = (campaignId: number) => {
  return useQuery({
    queryKey: [queryKeys.campaign.dashboard.summary, campaignId],
    queryFn: () => campaignApi.getCampaignSummary(campaignId),
  })
}

export const useGetQuickStats = () => {
  return useQuery({
    queryKey: [queryKeys.campaign.dashboard.quickStats],
    queryFn: () => campaignApi.getQuickStats(),
    staleTime: 0,
    refetchOnMount: true,
  })
}

export const useGetCampaignDonationsReport = (
  params: PaginationParams & DonationReportFilterParams
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.dashboard.donationsReport, params],
    queryFn: () => campaignApi.getCampaignDonationsReport(params),
  })
}

export const useGetCheckRequests = (
  params: PaginationParams & CheckRequestFilterParams
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.dashboard.checkRequests, params],
    queryFn: () => campaignApi.getCheckRequests(params),
  })
}

export const useGetCheckRequestFilters = () => {
  return useQuery({
    queryKey: [queryKeys.campaign.dashboard.checkRequestFilters],
    queryFn: () => campaignApi.getCheckRequestFilters(),
  })
}

export const useGetMessageLogs = (
  params: PaginationParams & MessageLogFilterParams
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.dashboard.messageLogs, params],
    queryFn: () => campaignApi.getMessageLogs(params),
  })
}

export const useGetMessageLogFilters = () => {
  return useQuery({
    queryKey: [queryKeys.campaign.dashboard.messageLogFilters],
    queryFn: () => campaignApi.getMessageLogFilters(),
  })
}

export const useGetCheckRequestTotalAmount = (
  start_date: string,
  end_date: string
) => {
  return useQuery({
    queryKey: [
      queryKeys.campaign.dashboard.checkRequestTotalAmount,
      start_date,
      end_date,
    ],
    queryFn: () => campaignApi.getCheckRequestTotalAmount(start_date, end_date),
  })
}
