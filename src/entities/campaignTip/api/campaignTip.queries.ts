import { API, apiUrls } from "@/shared/api"
import { CampaignTipDto, UpdateCampaignTipDto } from "./dto/campaignTip.dto"

export const CampaignTipApi = {
  getAll: async (id: number): Promise<CampaignTipDto[]> => {
    const response = await API.get(apiUrls.campaign.tip.list(id))
    return response.data
  },

  update: async (
    id: number,
    body: UpdateCampaignTipDto
  ): Promise<CampaignTipDto> => {
    const response = await API.put(apiUrls.campaign.tip.updateAmounts(id), body)
    return response.data
  },
}
