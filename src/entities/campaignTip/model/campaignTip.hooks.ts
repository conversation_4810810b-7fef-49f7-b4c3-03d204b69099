import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { ApiError, queryKeys } from "@/shared/model"
import { CampaignTipApi } from "../api/campaignTip.queries"
import {
  CampaignTipDto,
  UpdateCampaignTipDto,
} from "../api/dto/campaignTip.dto.ts"

const key = queryKeys.campaign.tips.get

export const useGetCampaignTips = (campaignId: number) => {
  return useQuery({
    queryKey: [key, campaignId],
    queryFn: () => CampaignTipApi.getAll(campaignId),
  })
}

export const useUpdateCampaignTips = (
  options?: UseMutationOptions<
    CampaignTipDto,
    ApiError,
    { campaignId: number; data: UpdateCampaignTipDto }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, data }) =>
      CampaignTipApi.update(campaignId, data),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [key],
      })

      queryClient.invalidateQueries({
        queryKey: ["getCampaigns"],
      })
      queryClient.invalidateQueries({
        queryKey: ["getCampaignById"],
      })

      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}
