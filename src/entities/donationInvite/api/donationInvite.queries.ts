import { API } from "@/shared/api"
import { PaginatedResponse, PaginationParams } from "@/shared/model"
import {
  CreateDonationInviteDto,
  DonationInviteDto,
} from "./dto/donationInvite.dto"

export const donationInviteApi = {
  getAll: async (): Promise<DonationInviteDto[]> => {
    const response = await API.get(`/campaign-donation-invites`)
    return response.data
  },

  getAllByCampaignId: async (
    campaignId: number,
    params: PaginationParams
  ): Promise<PaginatedResponse<DonationInviteDto>> => {
    const response = await API.get(
      `/campaign/${campaignId}/campaign-donation-invites`,
      { params }
    )
    return response.data
  },

  getById: async (
    campaignId: number,
    id: number
  ): Promise<DonationInviteDto> => {
    const response = await API.get(
      `/campaign/${campaignId}/campaign-donation-invites/${id}`
    )
    return response.data
  },

  add: async (
    campaignId: number,
    body: CreateDonationInviteDto
  ): Promise<DonationInviteDto> => {
    const response = await API.post(
      `/campaign/${campaignId}/campaign-donation-invites`,
      body
    )
    return response.data
  },

  update: async (
    campaignId: number,
    id: number,
    body: Partial<DonationInviteDto>
  ): Promise<DonationInviteDto> => {
    const response = await API.put(
      `/campaign/${campaignId}/campaign-donation-invites/${id}`,
      body
    )
    return response.data
  },

  delete: async (campaignId: number, id: number): Promise<void> => {
    const response = await API.delete(
      `/campaign/${campaignId}/campaign-donation-invites/${id}`
    )
    return response.data
  },
}
