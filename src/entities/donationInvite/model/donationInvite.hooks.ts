import { ApiError, PaginationParams } from "@/shared/model"
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { donationInviteApi } from "../api/donationInvite.queries"
import {
  CreateDonationInviteDto,
  DonationInviteDto,
} from "../api/dto/donationInvite.dto"

export const useGetDonationInvites = () => {
  return useQuery({
    queryKey: ["donationInvites"],
    queryFn: () => donationInviteApi.getAll(),
    refetchOnMount: true,
    staleTime: 0,
  })
}

export const useGetDonationInviteById = (campaignId: number, id: number) => {
  return useQuery({
    queryKey: ["donationInvites", "byId", id],
    queryFn: () => donationInviteApi.getById(campaignId, id),
  })
}

export const useGetDonationInvitesByCampaignId = (
  campaignId: number,
  params: PaginationParams
) => {
  return useQuery({
    queryKey: ["donationInvites", "byCampaignAndUser", params],
    queryFn: () => donationInviteApi.getAllByCampaignId(campaignId, params),
    refetchOnMount: true,
    staleTime: 0,
  })
}

export const useAddDonationInvite = (
  campaignId: number,
  options?: UseMutationOptions<
    DonationInviteDto,
    ApiError,
    CreateDonationInviteDto
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (body: CreateDonationInviteDto) =>
      donationInviteApi.add(campaignId, body),
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ["donationInvites"],
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}

export const useUpdateDonationInvite = (
  campaignId: number,
  options?: UseMutationOptions<
    DonationInviteDto,
    ApiError,
    { id: number; data: Partial<DonationInviteDto> }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ id, data }) =>
      donationInviteApi.update(campaignId, id, data),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: ["donationInvites"],
      })
      queryClient.invalidateQueries({
        queryKey: ["donationInvites", "byId", variables.id],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useDeleteDonationInvite = (
  campaignId: number,
  options?: UseMutationOptions<void, ApiError, number>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (id: number) => donationInviteApi.delete(campaignId, id),
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ["donationInvites"],
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
