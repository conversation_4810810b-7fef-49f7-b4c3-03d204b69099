import { API } from "@/shared/api"
import { ImageTypeDto } from "./dto/imageType.dto"

export const imageTypeApi = {
  getAll: async (): Promise<ImageTypeDto[]> => {
    const response = await API.get(`/image-types`)
    return response.data
  },

  getById: async (id: number): Promise<ImageTypeDto> => {
    const response = await API.get(`/image-types/${id}`)
    return response.data
  },

  add: async ({ name }: { name: string }): Promise<ImageTypeDto> => {
    const response = await API.post(`/image-types`, {
      name,
    })
    return response.data
  },

  update: async (
    id: number,
    { name }: { name: string }
  ): Promise<ImageTypeDto> => {
    const response = await API.put(`/image-types/${id}`, {
      name,
    })
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(`/image-types/${id}`)
    return response.data
  },
}
