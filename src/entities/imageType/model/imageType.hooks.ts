import { useQuery } from "@tanstack/react-query"
import { imageTypeApi } from "../api/imageType.queries"

export const useGetImageTypes = () => {
  return useQuery({
    queryKey: ["getImageTypes"],
    queryFn: () => imageTypeApi.getAll(),
  })
}

export const useGetImageTypeById = (id: number) => {
  return useQuery({
    queryKey: ["getImageType", id],
    queryFn: () => imageTypeApi.getById(id),
  })
}

export const useCreateImageType = (body: { name: string }) => {
  return useQuery({
    queryKey: ["createImageType"],
    queryFn: () => imageTypeApi.add(body),
  })
}

export const useUpdateImageType = (id: number, body: { name: string }) => {
  return useQuery({
    queryKey: ["updateImageType", id],
    queryFn: () => imageTypeApi.update(id, body),
  })
}

export const useDeleteImageType = (id: number) => {
  return useQuery({
    queryKey: ["deleteImageType", id],
    queryFn: () => imageTypeApi.delete(id),
  })
}