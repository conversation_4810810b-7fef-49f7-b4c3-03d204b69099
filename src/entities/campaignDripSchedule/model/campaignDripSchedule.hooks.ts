import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { ApiError, PaginationParams, queryKeys } from "@/shared/model"
import { campaignDripSchedulesApi } from "../api/campaignDripSchedule.queries"
import { CampaignDripSchedulesDto } from "../api/dto/campaignDripSchedule.dto"

export const useGetCampaignDripSchedules = (
  campaignId: number,
  params: PaginationParams
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.dripSchedules.get, campaignId, params],
    queryFn: () => campaignDripSchedulesApi.getAll(campaignId, params),
  })
}

export const useGetCampaignDelayStartDripSchedules = (campaignId: number) => {
  return useQuery({
    queryKey: [queryKeys.campaign.settings.get, campaignId],
    queryFn: () => campaignDripSchedulesApi.getDelayStart(campaignId),
  })
}

export const useUpdateCampaignDripSchedule = (
  options?: UseMutationOptions<
    CampaignDripSchedulesDto,
    ApiError,
    {
      campaignId: number
      data: {
        id: number
        interval: number
      }
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, data }) =>
      campaignDripSchedulesApi.update(campaignId, data),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.campaign.dripSchedules.get, variables.campaignId],
      })

      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useUpdateCampaignDelayStartDripSchedule = (
  options?: UseMutationOptions<
    unknown,
    ApiError,
    {
      campaignId: number
      data: {
        id: number
        donation_invite_delay_start_at: string
      }
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, data }) =>
      campaignDripSchedulesApi.updateDelayStart(campaignId, data),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.campaign.settings.get, variables.campaignId],
      })

      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}
