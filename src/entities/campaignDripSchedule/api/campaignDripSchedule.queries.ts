import { API, apiUrls } from "@/shared/api"
import { PaginatedResponse, PaginationParams } from "@/shared/model"
import { CampaignDripSchedulesDto } from "./dto/campaignDripSchedule.dto"

export const campaignDripSchedulesApi = {
  getAll: async (
    campaignId: number,
    params: PaginationParams
  ): Promise<PaginatedResponse<CampaignDripSchedulesDto>> => {
    const response = await API.get(
      apiUrls.campaign.dripSchedules.list(campaignId),
      {
        params: {
          page: params.page,
          per_page: params.per_page,
        },
      }
    )
    return {
      ...response.data,
      data: response.data.data,
    }
  },

  getDelayStart: async (
    campaignId: number
  ): Promise<{
    id: number
    donation_invite_delay_start_at: string
  } | null> => {
    const response = await API.get(apiUrls.campaign.settings.list(campaignId))
    const data = response.data
    const item = data?.length ? data[data.length - 1] : null
    return item
      ? {
          id: item.id,
          donation_invite_delay_start_at: item.donation_invite_delay_start_at,
        }
      : null
  },
  updateDelayStart: async (
    campaignId: number,
    data: {
      id: number
      donation_invite_delay_start_at: string
    }
  ): Promise<unknown> => {
    const response = await API.put(
      apiUrls.campaign.settings.details(campaignId, data.id),
      {
        donation_invite_delay_start_at: data.donation_invite_delay_start_at,
      }
    )
    return response.data
  },
  update: async (
    groupId: number,
    data: {
      id: number
      interval: number
    }
  ): Promise<CampaignDripSchedulesDto> => {
    const response = await API.put(
      apiUrls.campaign.dripSchedules.update(groupId),
      data
    )
    return response.data
  },
}
