interface Template {
  id: number
  campaign_id: number
  template_id: number
  subject: string | null
  body: string
  created_at: string
  updated_at: string
  channel: string
  template_name: string
}

export interface CampaignDripSchedulesDto {
  id: number
  template_id: number
  campaign_id: number
  days_after_event: number
  created_at: string
  updated_at: string
  drip_schedule_id: number
  enabled: boolean
  campaign__template_id: number
  message_template: Omit<Template, "channel">
  template: Template
}
