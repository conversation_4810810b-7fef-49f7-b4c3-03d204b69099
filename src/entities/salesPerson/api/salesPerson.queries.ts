import { API, apiUrls } from "@/shared/api"
import { SalesPersonDto } from "./dto/salesPerson.dto"

export const salesPersonApi = {
  getAll: async (): Promise<SalesPersonDto[]> => {
    const response = await API.get(apiUrls.salesPerson.list())
    return response.data
  },

  create: async ({ user_id }: { user_id: number }): Promise<SalesPersonDto> => {
    const response = await API.post(apiUrls.salesPerson.list(), {
      user_id,
    })
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(apiUrls.salesPerson.details(id))
    return response.data
  },
}
