import { queryKeys } from "@/shared/model"
import { useQuery } from "@tanstack/react-query"
import { salesPersonApi } from "../api/salesPerson.queries"

export const useGetSalesPersons = () => {
  return useQuery({
    queryKey: [queryKeys.salesPerson.get],
    queryFn: () => salesPersonApi.getAll(),
  })
}

export const useCreateSalesPerson = (body: { user_id: number }) => {
  return useQuery({
    queryKey: [queryKeys.salesPerson.create],
    queryFn: () => salesPersonApi.create(body),
  })
}

export const useDeleteSalesPerson = (id: number) => {
  return useQuery({
    queryKey: [queryKeys.salesPerson.delete, id],
    queryFn: () => salesPersonApi.delete(id),
  })
}
