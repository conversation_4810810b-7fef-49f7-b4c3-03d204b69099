import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { UseFormReset } from "react-hook-form"
import { ApiError, queryKeys } from "@/shared/model"
import {
  CampaignMessageTemplateDto,
  UpdateCampaignMessageTemplateDto,
} from "../api/dto/campaignMessageTemplate.dto"
import { campaignMessageTemplateApi } from "../api/campaignMessageTemplate.queries"

export const useGetCampaignMessageTemplates = (
  campaignId: number,
  channel: string
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.messageTemplates.get, campaignId, channel],
    queryFn: () =>
      campaignMessageTemplateApi.getAll(campaignId, {
        channel,
      }),
  })
}

export const useGetCampaignMessageTemplateById = (
  campaignId: number,
  id: number
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.messageTemplates.getById, campaignId, id],
    queryFn: () => campaignMessageTemplateApi.getById(campaignId, id),
  })
}

export const useDeleteCampaignMessageTemplate = (
  options?: UseMutationOptions<
    void,
    ApiError,
    {
      campaignId: number
      templateId: number
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, templateId }) =>
      campaignMessageTemplateApi.delete(campaignId, templateId),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.campaign.messageTemplates.get],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useUpdateCampaignMessageTemplate = (
  options?: UseMutationOptions<
    CampaignMessageTemplateDto[],
    ApiError,
    {
      campaignId: number
      templateId: number
      body: UpdateCampaignMessageTemplateDto
      reset?: UseFormReset<UpdateCampaignMessageTemplateDto>
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, templateId, body }) =>
      campaignMessageTemplateApi.update(campaignId, templateId, body),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [
          queryKeys.campaign.messageTemplates.getById,
          variables.campaignId,
          variables.templateId,
        ],
      })
      queryClient.refetchQueries({
        queryKey: [
          queryKeys.campaign.messageTemplates.get,
          variables.campaignId
        ],
      })

      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}
