import { API, apiUrls } from "@/shared/api"
import {
  CampaignMessageTemplateDto,
  UpdateCampaignMessageTemplateDto,
} from "./dto/campaignMessageTemplate.dto"

export const campaignMessageTemplateApi = {
  getAll: async (
    campaignId: number,
    params?: Record<string, any>
  ): Promise<CampaignMessageTemplateDto[]> => {
    const response = await API.get(
      apiUrls.campaign.messageTemplates.list(campaignId),
      { params }
    )
    return response.data
  },

  getById: async (
    campaignId: number,
    templateId: number
  ): Promise<CampaignMessageTemplateDto[]> => {
    const response = await API.get(
      apiUrls.campaign.messageTemplates.details(campaignId, templateId)
    )
    return response.data
  },

  update: async (
    campaignId: number,
    templateId: number,
    body: UpdateCampaignMessageTemplateDto
  ): Promise<CampaignMessageTemplateDto[]> => {
    const response = await API.put(
      apiUrls.campaign.messageTemplates.details(campaignId, templateId),
      body
    )
    return response.data
  },

  delete: async (campaignId: number, templateId: number): Promise<void> => {
    await API.delete(
      apiUrls.campaign.messageTemplates.details(campaignId, templateId)
    )
  },
}
