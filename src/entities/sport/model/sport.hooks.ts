import {
  useQuery,
  useMutation,
  UseMutationOptions,
  useQueryClient,
} from "@tanstack/react-query"
import { sportApi } from "@/entities/sport"
import { ApiError, queryKeys } from "@/shared/model"
import {
  SportDto
} from "../api/dto/sport.dto"

export const useGetSports = () => {
  return useQuery({
    queryKey: [queryKeys.sports.get],
    queryFn: () => sportApi.getAll(),
  })
}

export const useCreateSport = (
  options?: UseMutationOptions<SportDto, ApiError, { name: string; }>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (name) => sportApi.create(name),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.sports.get],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useUpdateSport = (
  options?: UseMutationOptions<
    SportDto,
    ApiError,
    { sportId: number; name: { name: string } }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ sportId, name }) => sportApi.update(sportId, name),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.sports.get],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useDeleteSport = (
  options?: UseMutationOptions<void, ApiError, number>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (sportId) => sportApi.delete(sportId),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.sports.get],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}
