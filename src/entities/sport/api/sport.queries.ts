import { API, apiUrls } from "@/shared/api"
import { SportDto } from "./dto/sport.dto"

export const sportApi = {
  getAll: async (): Promise<SportDto[]> => {
    const response = await API.get(apiUrls.sports.list())
    return response.data
  },

  create: async ({ name }: { name: string }): Promise<SportDto> => {
    const response = await API.post(apiUrls.sports.list(), {
      name,
    })
    return response.data
  },

  update: async (id: number, { name }: { name: string }): Promise<SportDto> => {
    const response = await API.put(apiUrls.sports.details(id), {
      name,
    })
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(apiUrls.sports.details(id))
    return response.data
  },
}
