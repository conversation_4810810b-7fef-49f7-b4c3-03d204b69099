import {
  CampaignMemberDto,
  UpdateCampaignMemberDto,
} from "@/entities/campaignMember"
import { ApiError, PaginationParams, queryKeys } from "@/shared/model"
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { campaignMemberApi } from "../api/campaignMember.queries"

const keyGet = queryKeys.campaign.members.get
const keyGetById = queryKeys.campaign.members.getById

export const useGetCampaignMembers = (
  campaignId: number,
  params: PaginationParams
) => {
  return useQuery({
    queryKey: [keyGet, campaignId, params],
    queryFn: () => campaignMemberApi.getAll(campaignId, params),
  })
}

export const useGetCampaignMemberById = (
  campaignId: number,
  userId: number
) => {
  return useQuery({
    queryKey: [keyGetById, campaignId, userId],
    queryFn: () => campaignMemberApi.getById(campaignId, userId),
  })
}

export const useUpdateCampaignMember = (
  options?: UseMutationOptions<
    CampaignMemberDto,
    ApiError,
    {
      campaignId: number
      userId: number
      data: UpdateCampaignMemberDto
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, userId, data }) =>
      campaignMemberApi.update(campaignId, userId, data),
    onSuccess: (result, variables, ...rest) => {
      queryClient.refetchQueries({
        queryKey: [keyGet, variables.campaignId],
      })
      queryClient.invalidateQueries({
        queryKey: [keyGetById, variables.campaignId, variables.userId],
      })

      if (options?.onSuccess) {
        options.onSuccess(result, variables, ...rest)
      }
    },
  })
}

export const useDeleteCampaignMember = (
  options?: UseMutationOptions<
    void,
    Error,
    { campaignId: number; userId: number }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, userId }) =>
      campaignMemberApi.delete(campaignId, userId),
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [keyGet],
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
