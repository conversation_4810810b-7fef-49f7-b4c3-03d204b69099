export enum CampaignMemberStatus {
  PENDING = "pending",
  SENT = "sent",
  ACCEPTED = "accepted",
  EXPIRED = "expired",
}

export interface CampaignMemberData {
  user_id: number
  campaign_user_id: number
  campaign_id: number
  campaign_role_id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  status: CampaignMemberStatus
  invite_id: number | null
  group?: {
    id: number
    name: string
  } | null
}

export interface CampaignMemberDto {
  data: CampaignMemberData[]
  current_page: number
  total: number
  per_page: number
}

export interface UpdateCampaignMemberDto {
  first_name: string
  last_name: string
  email: string
  phone: string
  campaign_role_id: number
  group_id?: number | null
}
