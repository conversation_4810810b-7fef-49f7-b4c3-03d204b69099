import { API, apiUrls } from "@/shared/api"
import { PaginationParams } from "@/shared/model"
import {
  CampaignMemberData,
  CampaignMemberDto,
  UpdateCampaignMemberDto,
} from "./dto/campaignMember.dto"

export const campaignMemberApi = {
  getAll: async (
    campaignId: number,
    params: PaginationParams
  ): Promise<CampaignMemberDto> => {
    const response = await API.get(apiUrls.campaign.members.list(campaignId), {
      params,
    })

    return response.data
  },

  getById: async (
    campaignId: number,
    userId: number
  ): Promise<CampaignMemberData> => {
    const response = await API.get(
      apiUrls.campaign.members.details(campaignId, userId)
    )

    return response.data
  },

  update: async (
    campaignId: number,
    userId: number,
    member: UpdateCampaignMemberDto
  ): Promise<CampaignMemberDto> => {
    const response = await API.put(
      apiUrls.campaign.members.details(campaignId, userId),
      member
    )
    return response.data
  },

  delete: async (campaignId: number, userId: number): Promise<void> => {
    const response = await API.delete(
      apiUrls.campaign.members.details(campaignId, userId)
    )
    return response.data
  },
}
