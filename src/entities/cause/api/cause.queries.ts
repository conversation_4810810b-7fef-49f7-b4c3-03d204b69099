import { API } from "@/shared/api"
import { CauseDto } from "./dto/cause.dto"

export const causeApi = {
  getAll: async (): Promise<CauseDto[]> => {
    const response = await API.get(`/causes`)
    return response.data
  },

  getById: async (id: number): Promise<CauseDto> => {
    const response = await API.get(`/causes/${id}`)
    return response.data
  },

  add: async ({ name }: { name: string }): Promise<CauseDto> => {
    const response = await API.post(`/causes`, {
      name,
    })
    return response.data
  },

  update: async (id: number, { name }: { name: string }): Promise<CauseDto> => {
    const response = await API.put(`/causes/${id}`, {
      name,
    })
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(`/causes/${id}`)
    return response.data
  },
}
