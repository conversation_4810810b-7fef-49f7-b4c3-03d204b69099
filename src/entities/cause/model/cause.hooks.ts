import { useQuery } from "@tanstack/react-query"
import { causeApi } from "../api/cause.queries"

export const useGetCauses = () => {
  return useQuery({
    queryKey: ["getCauses"],
    queryFn: () => causeApi.getAll(),
  })
}

export const useGetCauseById = (id: number) => {
  return useQuery({
    queryKey: ["getCause", id],
    queryFn: () => causeApi.getById(id),
  })
}

export const useCreateCause = (body: { name: string }) => {
  return useQuery({
    queryKey: ["createCause"],
    queryFn: () => causeApi.add(body),
  })
}

export const useUpdateCause = (id: number, body: { name: string }) => {
  return useQuery({
    queryKey: ["updateCause", id],
    queryFn: () => causeApi.update(id, body),
  })
}

export const useDeleteCause = (id: number) => {
  return useQuery({
    queryKey: ["deleteCause", id],
    queryFn: () => causeApi.delete(id),
  })
}