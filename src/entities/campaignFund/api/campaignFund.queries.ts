import { API, apiUrls } from "@/shared/api"
import { CampaignFundApiDto } from "./dto/campaignFund.dto"

export const campaignFundApi = {
  get: async (campaignId: number): Promise<CampaignFundApiDto> => {
    const response = await API.get(
      apiUrls.campaign.checkRequest.list(campaignId)
    )
    const data = response.data
    const item = data?.length ? data[data.length - 1] : null
    return item
  },

  create: async (campaignId: number): Promise<CampaignFundApiDto> => {
    const response = await API.post(
      apiUrls.campaign.checkRequest.list(campaignId)
    )
    return response.data
  },

  finalize: async (campaignId: number): Promise<CampaignFundApiDto> => {
    const response = await API.post(
      apiUrls.campaign.checkRequest.finalize(campaignId)
    )
    return response.data
  },

  clear: async (campaignId: number): Promise<CampaignFundApiDto> => {
    const response = await API.post(
      apiUrls.campaign.checkRequest.clear(campaignId)
    )
    return response.data
  },

  update: async (
    campaignId: number,
    id: number,
    data: Partial<CampaignFundApiDto>
  ): Promise<CampaignFundApiDto> => {
    const response = await API.put(
      apiUrls.campaign.checkRequest.details(campaignId, id),
      data
    )
    return response.data
  },
}
