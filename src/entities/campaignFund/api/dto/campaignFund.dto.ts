export const CampaignFundStatus = {
  pending: "pending",
  requested: "requested",
  processed: "processed",
  cleared: "cleared",
} as const

type CampaignFundStatusType =
  (typeof CampaignFundStatus)[keyof typeof CampaignFundStatus]

export interface CampaignFundApiDto {
  campaign_id: number
  request_status: CampaignFundStatusType
  total_funds: number
  total_fees: number
  total_raised: number
  recipient_name: string
  mailing_address_1: string
  mailing_address_2?: string | null
  city: string
  state: string
  id: number
  zip: string
  memo: string
  notes?: string | null
  org_contact_email?: string | null
  org_contact_phone?: string | null
  check_requested_at: string | null
  check_processed_at: string | null
  requested_by_user_id: number | null
  notification_enabled: boolean
  created_at: string
  updated_at: string
  check_number?: string | null
}

export type UpdateCampaignFundApiDto = Pick<
  CampaignFundApiDto,
  | "recipient_name"
  | "mailing_address_1"
  | "mailing_address_2"
  | "city"
  | "state"
  | "zip"
  | "memo"
  | "notes"
  | "org_contact_email"
  | "org_contact_phone"
  | "notification_enabled"
  | "check_number"
>
