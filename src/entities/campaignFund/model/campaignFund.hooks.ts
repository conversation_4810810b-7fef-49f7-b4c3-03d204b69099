import { ApiError, queryKeys } from "@/shared/model"
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { campaignFundApi } from "../api/campaignFund.queries"
import {
  CampaignFundApiDto,
  UpdateCampaignFundApiDto,
} from "../api/dto/campaignFund.dto"

const key = queryKeys.campaign.fund.get

export const useGetCampaignFund = (campaignId: number) => {
  return useQuery({
    queryKey: [key, campaignId],
    queryFn: () => campaignFundApi.get(campaignId),
    refetchOnMount: true,
    staleTime: 0,
  })
}

export const useCreateRequestCampaignFund = (
  options?: UseMutationOptions<CampaignFundApiDto, ApiError, number>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (campaignId) => campaignFundApi.create(campaignId),
    onSuccess: (data, campaignId, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [key, campaignId],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, campaignId, ...rest)
      }
    },
  })
}

export const useFinalizeRequestCampaignFund = (
  options?: UseMutationOptions<CampaignFundApiDto, ApiError, number>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (campaignId) => campaignFundApi.finalize(campaignId),
    onSuccess: (data, campaignId, ...rest) => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.campaign.dashboard.checkRequests] })
      queryClient.invalidateQueries({
        queryKey: [key, campaignId],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, campaignId, ...rest)
      }
    },
  })
}

export const useClearRequestCampaignFund = (
  options?: UseMutationOptions<CampaignFundApiDto, ApiError, number>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (campaignId) => campaignFundApi.clear(campaignId),
    onSuccess: (data, campaignId, ...rest) => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.campaign.dashboard.checkRequests] })
      queryClient.invalidateQueries({
        queryKey: [key, campaignId],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, campaignId, ...rest)
      }
    },
  })
}

export const useUpdateCampaignFund = (
  options?: UseMutationOptions<
    CampaignFundApiDto,
    ApiError,
    {
      campaignId: number
      id: number
      data: UpdateCampaignFundApiDto
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, id, data }) =>
      campaignFundApi.update(campaignId, id, data),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [key, variables.campaignId],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}
