import { queryKeys } from "@/shared/model"
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from "@tanstack/react-query"
import { toast } from "react-toastify"
import { campaignDonationMessageApi } from "../api/campaignDonationMessage.queries"
import { CampaignDonationMessageDto } from "../api/dto/campaignDonationMessage.dto"

export const useGetCampaignDonationMessage = (
  campaignId: number,
  campaignUserId: number,
  options?: Omit<
    UseQueryOptions<CampaignDonationMessageDto>,
    "queryKey" | "queryFn"
  >
) => {
  return useQuery({
    queryKey: [
      queryKeys.campaign.donationMessage.getById,
      campaignId,
      campaignUserId,
    ],
    queryFn: () =>
      campaignDonationMessageApi.getById(campaignId, campaignUserId),
    ...options,
  })
}

export const useUpdateCampaignDonationMessage = (
  campaignId: number,
  campaignUserId: number,
  options?: UseMutationOptions<
    CampaignDonationMessageDto,
    Error,
    { message: string }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { message: string }) =>
      campaignDonationMessageApi.update(campaignId, campaignUserId, data),
    ...options,
    onSuccess: () => {
      toast.success("Donation message updated successfully")
      queryClient.invalidateQueries({
        queryKey: [
          queryKeys.campaign.donationMessage.getById,
          campaignId,
          campaignUserId,
        ],
      })
    },
    onError: () => {
      toast.error("Failed to update donation message")
    },
  })
}
