import { API, apiUrls } from "@/shared/api"
import { CampaignDonationMessageDto } from "./dto/campaignDonationMessage.dto"

export const campaignDonationMessageApi = {
  getById: async (
    campaignId: number,
    campaignUserId: number
  ): Promise<CampaignDonationMessageDto> => {
    const response = await API.get(
      apiUrls.campaign.users.donationMessage.details(campaignId, campaignUserId)
    )
    return response.data
  },
  update: async (
    campaignId: number,
    campaignUserId: number,
    data: { message: string }
  ): Promise<CampaignDonationMessageDto> => {
    const response = await API.put(
      apiUrls.campaign.users.donationMessage.details(
        campaignId,
        campaignUserId
      ),
      data
    )
    return response.data
  },
}
