import { API } from "@/shared/api"
import { GenderDto } from "./dto/gender.dto"

export const genderApi = {
  getAll: async (): Promise<GenderDto[]> => {
    const response = await API.get(`/genders`)
    return response.data
  },

  getById: async (id: number): Promise<GenderDto> => {
    const response = await API.get(`/genders/${id}`)
    return response.data
  },

  add: async ({ name }: { name: string }): Promise<GenderDto> => {
    const response = await API.post(`/genders`, {
      name,
    })
    return response.data
  },

  update: async (
    id: number,
    { name }: { name: string }
  ): Promise<GenderDto> => {
    const response = await API.put(`/genders/${id}`, {
      name,
    })
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(`/genders/${id}`)
    return response.data
  },
}
