import { useQuery } from "@tanstack/react-query"
import { genderApi } from "../api/gender.queries"

export const useGetGenders = () => {
  return useQuery({
    queryKey: ["getGenders"],
    queryFn: () => genderApi.getAll(),
  })
}

export const useGetGenderById = (id: number) => {
  return useQuery({
    queryKey: ["getGender", id],
    queryFn: () => genderApi.getById(id),
  })
}

export const useCreateGender = (body: { name: string }) => {
  return useQuery({
    queryKey: ["createGender"],
    queryFn: () => genderApi.add(body),
  })
}

export const useUpdateGender = (id: number, body: { name: string }) => {
  return useQuery({
    queryKey: ["updateGender", id],
    queryFn: () => genderApi.update(id, body),
  })
}

export const useDeleteGender = (id: number) => {
  return useQuery({
    queryKey: ["deleteGender", id],
    queryFn: () => genderApi.delete(id),
  })
}