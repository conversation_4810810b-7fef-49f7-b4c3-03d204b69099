import { API } from "@/shared/api"
import { GetCampaignGroupMemberDTO } from "./dto/campaignGroupMember.dto"

export const campaignGroupMemberApi = {
  getAll: async (groupId: number): Promise<GetCampaignGroupMemberDTO[]> => {
    const response = await API.get(`/groups/${groupId}/members`)
    return response.data
  },

  add: async (groupId: number, memberIds: number[]): Promise<void> => {
    const response = await API.post(`/groups/${groupId}/members/`, {
      campaign_user_ids: memberIds,
    })
    return response.data
  },

  selfAdd: async (campaignId: number, groupId: number, memberIds: number[]): Promise<void> => {
    const response = await API.post(`campaign/${campaignId}/groups/${groupId}/members/`, {
      campaign_user_ids: memberIds,
    })
    return response.data
  },
  

  delete: async (groupId: number, memberId: number): Promise<void> => {
    const response = await API.delete(`/groups/${groupId}/members/${memberId}`)
    return response.data
  },
}
