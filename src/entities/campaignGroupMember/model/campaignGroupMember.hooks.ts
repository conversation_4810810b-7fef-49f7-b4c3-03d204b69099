import { ApiError } from "@/shared/model"
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { campaignGroupMemberApi } from "../api/campaignGroupMember.queries"

export const useGetCampaignGroupMembers = (groupId: number) => {
  return useQuery({
    queryKey: ["useGetCampaignGroupMembers", groupId],
    queryFn: () => campaignGroupMemberApi.getAll(groupId),
  })
}

export const useAddCampaignGroupMember = (
  options?: UseMutationOptions<
    void,
    ApiError,
    { groupId: number; memberIds: number[] }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ groupId, memberIds }) =>
      campaignGroupMemberApi.add(groupId, memberIds),
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroupMembers"],
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}

export const useSelfAddCampaignGroupMember = (
  options?: UseMutationOptions<
    void,
    ApiError,
    { campaignId: number; groupId: number; memberIds: number[] }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, groupId, memberIds }) =>
      campaignGroupMemberApi.selfAdd(campaignId, groupId, memberIds),
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroupMembers"],
      })
      queryClient.invalidateQueries({
        queryKey: ["userCampaigns"],
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}

export const useDeleteCampaignGroupMember = (
  options?: UseMutationOptions<
    void,
    Error,
    { groupId: number; memberId: number }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ groupId, memberId }) =>
      campaignGroupMemberApi.delete(groupId, memberId),
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroupMembers"],
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
