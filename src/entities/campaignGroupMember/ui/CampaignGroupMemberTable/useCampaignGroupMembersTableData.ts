import { useMemo } from "react"
import { getColumns } from "./getColumns"

interface CampaignGroupMemberData {
  id: number
  name: string
}

interface UseCampaignGroupMembersTableDataProps {
  onDelete: (id: number) => void
  campaignGroupMembers: CampaignGroupMemberData[]
}

export const useCampaignGroupMembersTableData = ({
  onDelete,
  campaignGroupMembers,
}: UseCampaignGroupMembersTableDataProps) => {
  const columns = useMemo(() => getColumns({ onDelete }), [onDelete])

  const rows =
    useMemo(
      () => campaignGroupMembers?.map((row) => ({ ...row, id: row.id })),
      [campaignGroupMembers]
    ) ?? []

  return {
    columns,
    rows,
  }
}
