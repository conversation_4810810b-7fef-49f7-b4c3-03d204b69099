import { IconButton } from "@/shared/ui"
import DeleteIcon from "@mui/icons-material/Delete"
import { GridRenderCellParams } from "@mui/x-data-grid"

interface GetColumnsProps {
  onDelete: (id: number) => void
}

export const getColumns = ({ onDelete }: GetColumnsProps) => {
  return [
    {
      field: "id",
      headerName: "Group ID",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <>
          <IconButton onClick={() => onDelete(params.row.id)} title="Delete">
            <DeleteIcon />
          </IconButton>
        </>
      ),
    },
  ]
}
