import { API, apiUrls } from "@/shared/api"
import { PaginatedResponse, PaginationParams } from "@/shared/model"
import { MessageLogFilterParams } from "../model/type"
import { CampaignMessageLogDto } from "./dto/campaignRole.dto"

export const campaignMessageLogApi = {
  getAll: async (
    campaignId: number,
    params: PaginationParams & MessageLogFilterParams
  ): Promise<PaginatedResponse<CampaignMessageLogDto>> => {
    const response = await API.get(
      apiUrls.campaign.donations.inviteMessage(campaignId),
      {
        params: {
          page: params.page,
          per_page: params.per_page,
          start_date: params.start_date,
          end_date: params.end_date,
          message_status: params.message_status,
        },
      }
    )
    return {
      ...response.data,
      data: response.data.data,
    }
  },
}
