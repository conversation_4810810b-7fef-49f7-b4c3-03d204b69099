import { PaginationParams, queryKeys } from "@/shared/model"
import { useQuery } from "@tanstack/react-query"
import { campaignMessageLogApi } from "../api/campaignRole.queries"
import { MessageLogFilterParams } from "./type"

export const useGetCampaignMessageLog = (
  campaignId: number,
  params: PaginationParams & MessageLogFilterParams
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.messageLog.get, campaignId, params],
    queryFn: () => campaignMessageLogApi.getAll(campaignId, params),
  })
}
