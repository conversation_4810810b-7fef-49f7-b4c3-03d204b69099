import { PaginationParams, queryKeys } from "@/shared/model"
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { toast } from "react-toastify"
import { campaignDonationApi } from "../api/campaignDonations.queries"
import { CampaignDonationsParams } from "./campaignDonation.types"

const key = queryKeys.campaign.donation.get

export const useGetCampaignDonations = (
  campaignId: number,
  params: CampaignDonationsParams & PaginationParams
) => {
  return useQuery({
    queryKey: [key, campaignId, params],
    queryFn: () => campaignDonationApi.get(campaignId, params),
    retry: false,
  })
}

export const useGetCampaignDonationsFilters = (campaignId: number) => {
  const key = queryKeys.campaign.donation.getFilters

  return useQuery({
    queryKey: [key, campaignId],
    queryFn: () => campaignDonationApi.getFilters(campaignId),
  })
}

export const useExportCampaignDonations = (
  options?: UseMutationOptions<
    Blob,
    Error,
    { campaignId: number; params: CampaignDonationsParams }
  >
) => {
  return useMutation({
    mutationFn: async ({
      campaignId,
      params,
    }: {
      campaignId: number
      params: CampaignDonationsParams
    }) => campaignDonationApi.export(campaignId, params),
    onSuccess: (...args) => {
      const response = args[0] as Blob
      const url = window.URL.createObjectURL(response)
      const link = document.createElement("a")
      link.href = url
      const fileName = `donations_${new Date().toISOString().split("T")[0]}.csv`
      link.setAttribute("download", fileName)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      toast.success("Donations exported successfully")
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
    onError: () => {
      toast.error("Failed to export donations")
    },
    ...options,
  })
}

export const useDeleteCampaignDonation = (
  options?: UseMutationOptions<
    void,
    Error,
    { campaignId: number; donationId: number }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, donationId }) =>
      campaignDonationApi.delete(campaignId, donationId),
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.campaign.donation.get],
      })

      queryClient.invalidateQueries({
        queryKey: [queryKeys.campaign.dashboard.summary],
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
