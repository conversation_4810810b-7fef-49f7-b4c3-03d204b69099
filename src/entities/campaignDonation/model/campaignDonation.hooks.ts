import { ApiError } from "@/shared/model"
import { useMutation, UseMutationOptions } from "@tanstack/react-query"
import { campaignDonationApi } from "../api/campaignDonations.queries"
import { CampaignDonationDto } from "../api/dto/campaignDonations.dto"

export const useDonate = (
  options?: UseMutationOptions<void, ApiError, CampaignDonationDto>
) => {
  return useMutation({
    mutationFn: (data: CampaignDonationDto) => campaignDonationApi.donate(data),
    ...options,
  })
}
