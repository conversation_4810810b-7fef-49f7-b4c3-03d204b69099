import { API, apiUrls } from "@/shared/api"
import { PaginatedResponse, PaginationParams } from "@/shared/model"
import { CampaignDonationsParams } from "../model/campaignDonation.types"
import {
  CampaignDonationDto,
  CampaignDonationFilterDto,
} from "./dto/campaignDonations.dto"

export const campaignDonationApi = {
  get: async (
    id: number,
    params: PaginationParams
  ): Promise<PaginatedResponse<CampaignDonationDto>> => {
    const response = await API.get(apiUrls.campaign.donations.list(id), {
      params,
    })

    return {
      ...response.data,
      data: response.data.data,
    }
  },
  // @ts-expect-error need to implement
  donate: async (data: CampaignDonationDto) => {
    // TODO: add donation
  },

  getFilters: async (id: number): Promise<CampaignDonationFilterDto> => {
    const response = await API.get(apiUrls.campaign.donations.filters(id))

    return {
      ...response.data,
      data: response.data.data,
    }
  },
  export: async (id: number, params: CampaignDonationsParams) => {
    const response = await API.get(apiUrls.campaign.donations.export(id), {
      params,
    })

    const blob = new Blob([response.data], { type: "text/csv" })

    return blob
  },

  delete: async (campaignId: number, donationId: number): Promise<void> => {
    await API.delete(apiUrls.campaign.donations.details(campaignId, donationId))
  },
}
