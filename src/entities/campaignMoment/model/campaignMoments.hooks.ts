import {
  ApiError,
  CampaignImageType,
  CampaignImageTypes,
  queryKeys,
} from "@/shared/model"
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { campaignMomentsApi } from "../api/campaignMoments.queries"
import { CampaignMomentsDto } from "../api/dto/campaignMoments.dto"

export const useGetCampaignUserImagesByType = (
  type: CampaignImageTypes,
  campaignId?: number,
  campaignUserId?: number
) => {
  return useQuery({
    enabled: !!campaignId && !!campaignUserId,
    queryKey: [
      queryKeys.campaign.userMoments.get,
      campaignId,
      campaignUserId,
      type,
    ],
    queryFn: () =>
      campaignMomentsApi.user.getByType(type, campaignId!, campaignUserId!),
  })
}

export const useUpdateCampaignUserImage = (
  options?: UseMutationOptions<
    CampaignMomentsDto,
    ApiError,
    {
      campaignId: number
      campaignUserId: number
      type: CampaignImageTypes
      data: FormData
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, campaignUserId, data }) =>
      campaignMomentsApi.user.update({
        campaignId,
        campaignUserId,
        data,
      }),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [
          queryKeys.campaign.userMoments.get,
          variables.campaignId,
          variables.campaignUserId,
          variables.type,
        ],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useDeleteCampaignUserImage = (
  options?: UseMutationOptions<
    CampaignMomentsDto,
    ApiError,
    {
      campaignId: number
      campaignUserId: number
      imageId: number
      type?: CampaignImageTypes
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, campaignUserId, imageId }) =>
      campaignMomentsApi.user.delete({
        campaignId,
        campaignUserId,
        imageId,
      }),
    onSuccess: (data, variables, ...rest) => {
      if (variables.type) {
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.campaign.userMoments.get,
            variables.campaignId,
            variables.campaignUserId,
            variables.type,
          ],
        })
      } else {
        Object.values(CampaignImageType).forEach((type) => {
          queryClient.invalidateQueries({
            queryKey: [
              queryKeys.campaign.userMoments.get,
              variables.campaignId,
              variables.campaignUserId,
              type,
            ],
          })
        })
      }

      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useGetCampaignImagesByType = (
  type: CampaignImageTypes,
  campaignId?: number
) => {
  return useQuery({
    enabled: !!campaignId,
    queryKey: [queryKeys.campaign.moments.get, campaignId, type],
    queryFn: () => campaignMomentsApi.getByType(type, campaignId!),
  })
}

export const useUpdateCampaignImage = (
  options?: UseMutationOptions<
    CampaignMomentsDto,
    ApiError,
    {
      campaignId: number
      type: CampaignImageTypes
      data: FormData
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, data }) =>
      campaignMomentsApi.update({
        campaignId,
        data,
      }),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [
          queryKeys.campaign.moments.get,
          variables.campaignId,
          variables.type,
        ],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useDeleteCampaignImage = (
  options?: UseMutationOptions<
    CampaignMomentsDto,
    ApiError,
    {
      campaignId: number
      imageId: number
      type?: CampaignImageTypes
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, imageId }) =>
      campaignMomentsApi.delete({
        campaignId,
        imageId,
      }),
    onSuccess: (data, variables, ...rest) => {
      if (variables.type) {
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.campaign.moments.get,
            variables.campaignId,
            variables.type,
          ],
        })
      } else {
        Object.values(CampaignImageType).forEach((type) => {
          queryClient.invalidateQueries({
            queryKey: [
              queryKeys.campaign.moments.get,
              variables.campaignId,
              type,
            ],
          })
        })
      }

      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useReorderCampaignImages = (
  options?: UseMutationOptions<
    CampaignMomentsDto,
    ApiError,
    { campaignId: number; data: { images: { id: number; position: number }[] } }
  >
) => {
  return useMutation({
    ...options,
    mutationFn: ({ campaignId, data }) =>
      campaignMomentsApi.reorder({
        campaignId,
        data,
      }),
  })
}
