import { API, apiUrls } from "@/shared/api"
import { CampaignImageTypes } from "@/shared/model"
import { CampaignMomentsDto } from "./dto/campaignMoments.dto"

export const campaignMomentsApi = {
  user: {
    getByType: async (
      type: CampaignImageTypes,
      campaignId: number,
      campaignUserId: number
    ): Promise<CampaignMomentsDto[]> => {
      const response = await API.get(
        apiUrls.campaign.users.images.list(campaignId, campaignUserId),
        { params: { type } }
      )
      return response.data
    },

    update: async ({
      campaignId,
      campaignUserId,
      data,
    }: {
      campaignId: number
      campaignUserId: number
      data: FormData
    }): Promise<CampaignMomentsDto> => {
      const response = await API.post(
        apiUrls.campaign.users.images.list(campaignId, campaignUserId),
        data
      )
      return response.data
    },

    delete: async ({
      campaignId,
      campaignUserId,
      imageId,
    }: {
      campaignId: number
      campaignUserId: number
      imageId: number
    }): Promise<CampaignMomentsDto> => {
      const response = await API.delete(
        apiUrls.campaign.users.images.details(
          campaignId,
          campaignUserId,
          imageId
        )
      )
      return response.data
    },
  },

  getByType: async (
    type: CampaignImageTypes,
    campaignId: number
  ): Promise<CampaignMomentsDto[]> => {
    const response = await API.get(apiUrls.campaign.images.list(campaignId), {
      params: { type },
    })
    return response.data
  },

  update: async ({
    campaignId,
    data,
  }: {
    campaignId: number
    data: FormData
  }): Promise<CampaignMomentsDto> => {
    const response = await API.post(
      apiUrls.campaign.images.list(campaignId),
      data
    )
    return response.data
  },

  delete: async ({
    campaignId,
    imageId,
  }: {
    campaignId: number
    imageId: number
  }): Promise<CampaignMomentsDto> => {
    const response = await API.delete(
      apiUrls.campaign.images.details(campaignId, imageId)
    )
    return response.data
  },
  reorder: async ({
    campaignId,
    data,
  }: {
    campaignId: number
    data: {
      images: {
        id: number
        position: number
      }[]
    }
  }): Promise<CampaignMomentsDto> => {
    const response = await API.post(
      apiUrls.campaign.images.reorder(campaignId),
      data
    )
    return response.data
  },
}
