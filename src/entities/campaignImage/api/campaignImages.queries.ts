import { API, apiUrls } from "@/shared/api"
import { CampaignImageTypes } from "@/shared/model"
import { CampaignImageDto } from "./dto/campaignImages.dto"
export const campaignImagesApi = {
  getCampaignLogo: async (
    campaignId: number,
    type: CampaignImageTypes
  ): Promise<CampaignImageDto[]> => {
    const response = await API.get(apiUrls.campaign.images.list(campaignId), {
      params: { type },
    })
    return response.data
  },
  getCampaignActionShot: async (
    campaignId: number,
    type: CampaignImageTypes
  ): Promise<CampaignImageDto[]> => {
    const response = await API.get(apiUrls.campaign.images.list(campaignId), {
      params: { type },
    })
    return response.data
  },
  getCampaignHero: async (
    campaignId: number,
    type: CampaignImageTypes
  ): Promise<CampaignImageDto[]> => {
    const response = await API.get(apiUrls.campaign.images.list(campaignId), {
      params: { type },
    })
    return response.data
  },
}
