import { CampaignImageType, queryKeys } from "@/shared/model"
import { useQuery } from "@tanstack/react-query"
import { campaignImagesApi } from "../api/campaignImages.queries"

export const useGetCampaignLogo = (campaignId: number) => {
  return useQuery({
    queryKey: [queryKeys.campaign.images.getLogo, campaignId],
    queryFn: () =>
      campaignImagesApi.getCampaignLogo(campaignId, CampaignImageType.logo),
    enabled: !!campaignId,
  })
}

export const useGetCampaignActionShot = (campaignId: number) => {
  return useQuery({
    queryKey: [queryKeys.campaign.images.getActionShot, campaignId],
    queryFn: () =>
      campaignImagesApi.getCampaignActionShot(
        campaignId,
        CampaignImageType.action_shot
      ),
    enabled: !!campaignId,
  })
}

export const useGetCampaignHero = (campaignId: number) => {
  return useQuery({
    queryKey: [queryKeys.campaign.images.getHero, campaignId],
    queryFn: () =>
      campaignImagesApi.getCampaignHero(campaignId, CampaignImageType.hero),
    enabled: !!campaignId,
  })
}
