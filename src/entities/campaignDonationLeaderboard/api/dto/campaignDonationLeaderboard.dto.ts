export interface CampaignDonationLeaderboardData {
  payment_status: string
  donor_first_name: string
  donor_last_name: string
  base_donation_amount: string
  message: string
  is_anonymous: boolean
}

export interface CampaignDonationLeaderboardPlayerData {
  first_name: string
  last_name: string
  count_of_donors: number
  total_donation_amount: string
}
export interface CampaignDonationLeaderboardGroupPlayerData {
  first_name: string
  last_name: string
  total_donation_amount: string
}

export interface CampaignDonationLeaderboardGroupData {
  group_name: string
  amount_of_donations: string
  players: CampaignDonationLeaderboardGroupPlayerData[]
}

export interface CampaignDonationLeaderboardMeta {
  current_page: number
  total: number
  per_page: number
  last_page: number
}

export interface CampaignDonationLeaderboardDto {
  data: CampaignDonationLeaderboardData[]
  meta: CampaignDonationLeaderboardMeta
}

export interface CampaignDonationLeaderboardGroupDto {
  data: CampaignDonationLeaderboardGroupData[]
  meta: CampaignDonationLeaderboardMeta
}

export interface CampaignDonationLeaderboardPlayerDto {
  data: CampaignDonationLeaderboardPlayerData[]
  meta: CampaignDonationLeaderboardMeta
}
