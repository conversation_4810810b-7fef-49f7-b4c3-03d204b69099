import { API, apiUrls } from "@/shared/api/"
import {
  CampaignDonationLeaderboardDto,
  CampaignDonationLeaderboardGroupDto,
  CampaignDonationLeaderboardPlayerDto,
} from "./dto/campaignDonationLeaderboard.dto"

export const campaignDonationLeaderboardApi = {
  getList: async (
    campaignId: number,
    type: string,
    page: number
  ): Promise<CampaignDonationLeaderboardDto> => {
    const response = await API.get(
      apiUrls.campaign.donationLeaderboard.list(campaignId),
      {
        params: {
          type,
          page,
        },
      }
    )
    return response.data
  },
}

export const campaignDonationLeaderboardPlayerApi = {
  getList: async (
    campaignId: number,
    type: string,
    page: number
  ): Promise<CampaignDonationLeaderboardPlayerDto> => {
    const response = await API.get(
      apiUrls.campaign.donationLeaderboard.list(campaignId),
      {
        params: {
          type,
          page,
        },
      }
    )
    return response.data
  },
}

export const campaignDonationLeaderboardGroupApi = {
  getList: async (
    campaignId: number,
    type: string,
    page: number
  ): Promise<CampaignDonationLeaderboardGroupDto> => {
    const response = await API.get(
      apiUrls.campaign.donationLeaderboard.list(campaignId),
      {
        params: {
          type,
          page,
        },
      }
    )
    return response.data
  },
}
