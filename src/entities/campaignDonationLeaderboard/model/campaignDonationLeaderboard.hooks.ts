import { queryKeys } from "@/shared/model"
import { useQuery } from "@tanstack/react-query"
import {
  campaignDonationLeaderboardApi,
  campaignDonationLeaderboardGroupApi,
  campaignDonationLeaderboardPlayerApi,
} from "../api/campaingDonationLeaderboard.queries"

export const useGetDonorsLeaderboard = (
  campaignId: number,
  page: number,
  enabled: boolean
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.donationLeaderboard.donors, campaignId, page],
    queryFn: () =>
      campaignDonationLeaderboardApi.getList(campaignId, "donors", page),
    enabled,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export const useGetPlayersLeaderboard = (
  campaignId: number,
  page: number,
  enabled: boolean
) => {
  return useQuery({
    queryKey: [
      queryKeys.campaign.donationLeaderboard.players,
      campaignId,
      page,
    ],
    queryFn: () =>
      campaignDonationLeaderboardPlayerApi.getList(campaignId, "players", page),
    enabled,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export const useGetGroupsLeaderboard = (
  campaignId: number,
  page: number,
  enabled: boolean
) => {
  return useQuery({
    queryKey: [queryKeys.campaign.donationLeaderboard.groups, campaignId, page],
    queryFn: () =>
      campaignDonationLeaderboardGroupApi.getList(campaignId, "groups", page),
    enabled,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}
