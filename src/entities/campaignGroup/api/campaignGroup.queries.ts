import { API } from "@/shared/api"
import {
  CampaignGroupDto,
  CreateCampaignGroupDto,
  UpdateCampaignGroupDto,
} from "./dto/campaignGroup.dto"

export const campaignGroupApi = {
  getAll: async (): Promise<CampaignGroupDto[]> => {
    const response = await API.get("/groups")
    return response.data
  },

  getByCampaignId: async (campaignId: number): Promise<CampaignGroupDto[]> => {
    const response = await API.get(`/groups/campaign/${campaignId}`)
    return response.data
  },

  getById: async (groupId: number): Promise<CampaignGroupDto> => {
    const response = await API.get(`/groups/${groupId}`)
    return response.data
  },

  create: async (group: CreateCampaignGroupDto): Promise<CampaignGroupDto> => {
    const response = await API.post("/groups", group)
    return response.data
  },

  update: async (
    groupId: number,
    group: UpdateCampaignGroupDto
  ): Promise<CampaignGroupDto> => {
    const response = await API.put(`/groups/${groupId}`, group)
    return response.data
  },

  delete: async (groupId: number): Promise<void> => {
    const response = await API.delete(`/groups/${groupId}`)
    return response.data
  },
}
