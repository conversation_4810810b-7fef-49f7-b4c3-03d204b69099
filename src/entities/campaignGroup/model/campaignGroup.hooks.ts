import {
  useQuery,
  useMutation,
  UseMutationOptions,
  useQueryClient,
} from "@tanstack/react-query"
import { campaignGroupApi } from "../api/campaignGroup.queries"
import { ApiError } from "@/shared/model"
import {
  CampaignGroupDto,
  CreateCampaignGroupDto,
  UpdateCampaignGroupDto,
} from "../api/dto/campaignGroup.dto"

export const useGetCampaignGroups = () => {
  return useQuery({
    queryKey: ["useGetCampaignGroups"],
    queryFn: () => campaignGroupApi.getAll(),
  })
}

export const useGetCampaignGroupsByCampaignId = (campaignId: number) => {
  return useQuery({
    queryKey: ["useGetCampaignGroupsByCampaignId", campaignId],
    queryFn: () => campaignGroupApi.getByCampaignId(campaignId),
    enabled: !!campaignId,
  })
}

export const useGetCampaignGroupById = (groupId: number) => {
  return useQuery({
    queryKey: ["useGetCampaignGroupById", groupId],
    queryFn: () => campaignGroupApi.getById(groupId),
    enabled: !!groupId,
  })
}

export const useCreateCampaignGroup = (
  options?: UseMutationOptions<
    CampaignGroupDto,
    ApiError,
    CreateCampaignGroupDto
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (group) => campaignGroupApi.create(group),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroups"],
      })
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroupsByCampaignId", variables.campaign_id],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useUpdateCampaignGroup = (
  options?: UseMutationOptions<
    CampaignGroupDto,
    ApiError,
    { groupId: number; group: UpdateCampaignGroupDto }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ groupId, group }) => campaignGroupApi.update(groupId, group),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroups"],
      })
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroupById", variables.groupId],
      })
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroupsByCampaignId"],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}

export const useDeleteCampaignGroup = (
  options?: UseMutationOptions<void, ApiError, number>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: (groupId) => campaignGroupApi.delete(groupId),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroups"],
      })
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroupById", variables],
      })
      queryClient.invalidateQueries({
        queryKey: ["useGetCampaignGroupsByCampaignId"],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}
