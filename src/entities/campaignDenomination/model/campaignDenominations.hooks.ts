import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { ApiError, queryKeys } from "@/shared/model"
import {
  CampaignDenominationDto,
  UpdateCampaignDenominationDto,
} from "../api/dto/campaignDenominations.dto.ts"
import { CampaignDenominationsApi } from "../api/campaignDenominations.queries.ts"

const key = queryKeys.campaign.denomination.get

export const useGetCampaignDenominations = (campaignId: number) => {
  return useQuery({
    queryKey: [key, campaignId],
    queryFn: () => CampaignDenominationsApi.getAll(campaignId),
  })
}

export const useUpdateCampaignDenominations = (
  options?: UseMutationOptions<
    CampaignDenominationDto[],
    ApiError,
    { campaignId: number; data: UpdateCampaignDenominationDto }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, data }) =>
      CampaignDenominationsApi.update(campaignId, data),
    onSuccess: (data, variables, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [key],
      })

      if (options?.onSuccess) {
        options.onSuccess(data, variables, ...rest)
      }
    },
  })
}
