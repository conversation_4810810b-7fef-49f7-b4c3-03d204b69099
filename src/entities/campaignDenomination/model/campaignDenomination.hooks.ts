import { useMutation, UseMutationOptions } from "@tanstack/react-query"
import { campaignDenominationApi } from "../api/campaignDenomination.queries"
import { CampaignDenominationDto } from "../api/dto/campaignDenomination.dto"
import { ApiError } from "@/shared/model"

export const useDonate = (
  options?: UseMutationOptions<void, ApiError, CampaignDenominationDto>
) => {
  return useMutation({
    mutationFn: (data: CampaignDenominationDto) =>
      campaignDenominationApi.donate(data),
    ...options,
  })
}
