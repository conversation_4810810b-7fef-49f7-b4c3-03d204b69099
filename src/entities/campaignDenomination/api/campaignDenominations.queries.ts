import { API, apiUrls } from "@/shared/api"
import {
  CampaignDenominationDto,
  UpdateCampaignDenominationDto,
} from "./dto/campaignDenominations.dto"

export const CampaignDenominationsApi = {
  getAll: async (id: number): Promise<CampaignDenominationDto[]> => {
    const response = await API.get(apiUrls.campaign.denomination.list(id))
    return response.data
  },
  update: async (
    id: number,
    body: UpdateCampaignDenominationDto
  ): Promise<CampaignDenominationDto[]> => {
    const response = await API.put(
      apiUrls.campaign.denomination.updateAmounts(id),
      body
    )
    return response.data
  },
}
