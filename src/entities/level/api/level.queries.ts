import { API } from "@/shared/api"
import { LevelDto } from "./dto/level.dto"

export const levelApi = {
  getAll: async (): Promise<LevelDto[]> => {
    const response = await API.get(`/levels`)
    return response.data
  },

  getById: async (id: number): Promise<LevelDto> => {
    const response = await API.get(`/levels/${id}`)
    return response.data
  },

  add: async ({ name }: { name: string }): Promise<LevelDto> => {
    const response = await API.post(`/levels`, {
      name,
    })
    return response.data
  },

  update: async (id: number, { name }: { name: string }): Promise<LevelDto> => {
    const response = await API.put(`/levels/${id}`, {
      name,
    })
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(`/levels/${id}`)
    return response.data
  },
}
