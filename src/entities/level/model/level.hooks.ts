import { useQuery } from "@tanstack/react-query"
import { levelApi } from "../api/level.queries"

export const useGetLevels = () => {
  return useQuery({
    queryKey: ["getLevels"],
    queryFn: () => levelApi.getAll(),
  })
}

export const useGetLevelById = (id: number) => {
  return useQuery({
    queryKey: ["getLevel", id],
    queryFn: () => levelApi.getById(id),
  })
}

export const useCreateLevel = (body: { name: string }) => {
  return useQuery({
    queryKey: ["createLevel"],
    queryFn: () => levelApi.add(body),
  })
}

export const useUpdateLevel = (id: number, body: { name: string }) => {
  return useQuery({
    queryKey: ["updateLevel", id],
    queryFn: () => levelApi.update(id, body),
  })
}

export const useDeleteLevel = (id: number) => {
  return useQuery({
    queryKey: ["deleteLevel", id],
    queryFn: () => levelApi.delete(id),
  })
}