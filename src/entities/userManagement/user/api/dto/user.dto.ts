export interface UserDto {
  id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: number
  is_campaign_owner: boolean
  campaigns?: UserCampaignDto[]
}

export interface UserCampaignDto {
  id: number
  name: string
  campaign_role_id: number
}

export interface CreateUserDto {
  first_name: string
  last_name: string
  email: string
  phone: string
  password: string
  password_confirmation: string
  role_id: number
  campaigns: UpdateUserCampaignDto[]
}

export interface UpdateUserDto {
  id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: number
  campaigns: UpdateUserCampaignDto[]
}

export interface UpdateUserCampaignDto {
  id: number
  campaign_role_id: number
}
