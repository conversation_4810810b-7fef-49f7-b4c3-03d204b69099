import { API } from "@/shared/api"
import { UserDto, Create<PERSON>serDto, UpdateUserDto } from "./dto/user.dto"
import { PaginatedResponse, PaginationParams } from "@/shared/model"

const USERS_ENDPOINT = "/users"

export const usersApi = {
  getAll: async (
    params: PaginationParams
  ): Promise<PaginatedResponse<UserDto>> => {
    const response = await API.get(USERS_ENDPOINT + "?user_type=users", {
      params: {
        page: params.page,
        per_page: params.per_page,
      },
    })
    return response.data
  },

  getOne: async (id: number): Promise<UserDto> => {
    const response = await API.get(`${USERS_ENDPOINT}/${id}`)
    return response.data
  },

  create: async (data: CreateUserDto): Promise<UserDto> => {
    const response = await API.post(USERS_ENDPOINT, data)
    return response.data
  },

  update: async (data: UpdateUserDto): Promise<UserDto> => {
    const response = await API.put(`${USERS_ENDPOINT}/${data.id}`, data)
    return response.data
  },
}
