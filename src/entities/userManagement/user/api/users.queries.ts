import { API } from "@/shared/api"
import {
  UserDto,
  CreateUserDto,
  UpdateUserDto,
  UsersQueryParams
} from "./dto/user.dto"
import { PaginatedResponse } from "@/shared/model"

const USERS_ENDPOINT = "/users"

export const usersApi = {
  getAll: async (
    params: UsersQueryParams
  ): Promise<PaginatedResponse<UserDto>> => {
    const queryParams = Object.fromEntries(
      Object.entries(params).filter(
        ([, value]) => !!value
      )
    )

    const response = await API.get(USERS_ENDPOINT + "?user_type=users", {
      params: queryParams,
    })

    return response.data
  },

  getOne: async (id: number): Promise<UserDto> => {
    const response = await API.get(`${USERS_ENDPOINT}/${id}`)
    return response.data
  },

  create: async (data: CreateUserDto): Promise<UserDto> => {
    const response = await API.post(USERS_ENDPOINT, data)
    return response.data
  },

  update: async (data: UpdateUserDto): Promise<UserDto> => {
    const response = await API.put(`${USERS_ENDPOINT}/${data.id}`, data)
    return response.data
  },
}