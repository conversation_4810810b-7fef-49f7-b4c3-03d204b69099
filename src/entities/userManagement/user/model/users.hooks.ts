import {
  useQuery,
  useMutation,
  useQueryClient,
  UseMutationOptions,
} from "@tanstack/react-query"
import { ApiError, PaginationParams, queryKeys } from "@/shared/model"
import { usersApi } from "../api/users.queries"
import { CreateUserDto, UpdateUserDto, UserDto } from "../api/dto/user.dto"

export interface GetUsersParams extends PaginationParams {
  full_name?: string
  campaign_role_id?: number | null
}

export const useGetUsers = (params: GetUsersParams) => {
  return useQuery({
    queryKey: [queryKeys.userManagement.users.get, params],
    queryFn: () => usersApi.getAll(params),
  })
}

export const useGetUser = (id: number) => {
  return useQuery({
    queryKey: [queryKeys.userManagement.users.get, id],
    queryFn: () => usersApi.getOne(id),
    enabled: !!id,
  })
}

export const useCreateUser = (
  options?: UseMutationOptions<UserDto, ApiError, CreateUserDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: usersApi.create,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.userManagement.users.get],
        refetchType: "all",
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}

export const useUpdateUser = (
  options?: UseMutationOptions<UserDto, ApiError, UpdateUserDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: usersApi.update,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.userManagement.users.get],
        refetchType: "all",
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
