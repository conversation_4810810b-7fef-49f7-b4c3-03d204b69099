import {
  useQuery,
  useMutation,
  useQueryClient,
  UseMutationOptions,
} from "@tanstack/react-query"
import { ApiError, PaginationParams, queryKeys } from "@/shared/model"
import { usersApi } from "../api/users.queries"
import { CreateUserDto, UpdateUserDto, UserDto } from "../api/dto/user.dto"

export const useGetUsers = (params: PaginationParams) => {
  return useQuery({
    queryKey: [queryKeys.userManagement.ad.get, params],
    queryFn: () => usersApi.getAll(params),
  })
}

export const useGetUser = (id: number) => {
  return useQuery({
    queryKey: [queryKeys.userManagement.ad.get, id],
    queryFn: () => usersApi.getOne(id),
    enabled: !!id,
  })
}

export const useCreateUser = (
  options?: UseMutationOptions<UserDto, ApiError, CreateUserDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: usersApi.create,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.userManagement.ad.get],
        refetchType: "all",
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}

export const useUpdateUser = (
  options?: UseMutationOptions<UserDto, ApiError, UpdateUserDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: usersApi.update,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.userManagement.ad.get],
        refetchType: "all",
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
