import {
  useQuery,
  useMutation,
  useQueryClient,
  UseMutationOptions,
} from "@tanstack/react-query"
import { ApiError, PaginationParams, queryKeys } from "@/shared/model"
import { adApi } from "../api/ads.queries"
import { AdDto, CreateAdDto, UpdateAdDto } from "../api/dto/ads.dto"

export interface GetAdParams extends PaginationParams {
  full_name?: string
}

export const useGetAds = (params: GetAdParams) => {
  return useQuery({
    queryKey: [queryKeys.userManagement.ad.get, params],
    queryFn: () => adApi.getAll(params),
  })
}

export const useGetAd = (id: number) => {
  return useQuery({
    queryKey: [queryKeys.userManagement.ad.get, id],
    queryFn: () => adApi.getOne(id),
    enabled: !!id,
  })
}

export const useCreateAd = (
  options?: UseMutationOptions<AdDto, ApiError, CreateAdDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: adApi.create,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.userManagement.ad.get],
        refetchType: "all",
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}

export const useUpdateAd = (
  options?: UseMutationOptions<AdDto, ApiError, UpdateAdDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: adApi.update,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.userManagement.ad.get],
        refetchType: "all",
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
