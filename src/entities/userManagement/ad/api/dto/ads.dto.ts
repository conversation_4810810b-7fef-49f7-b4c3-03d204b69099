import { PaginationParams } from "@/shared/model"

export interface AdDto {
  id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: number
  role_name: string
  campaigns: AdCampaignDto[]
}

export interface AdCampaignDto {
  name: string
  id: number
}

export interface CreateAdDto {
  first_name: string
  last_name: string
  email: string
  phone: string
  password: string
  password_confirmation: string
  role_id: number
  campaigns: AdCampaignUpdateDto[]
}

export interface UpdateAdDto {
  id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: number
  campaigns: AdCampaignUpdateDto[]
}

export interface AdCampaignUpdateDto {
  id: number
}

export interface AdFiltersDTO {
  full_name?: string
}

export interface AdQueryParams extends PaginationParams, AdFiltersDTO {}
