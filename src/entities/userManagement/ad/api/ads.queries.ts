import { API } from "@/shared/api"
import {
  AdDto,
  CreateAdDto,
  UpdateAdDto,
  AdQueryParams
} from "./dto/ads.dto"
import { PaginatedResponse } from "@/shared/model"

const AD_ENDPOINT = "/users"

export const adApi = {
    getAll: async (
      params: AdQueryParams
    ): Promise<PaginatedResponse<AdDto>> => {
      const queryParams = Object.fromEntries(
        Object.entries(params).filter(
          ([, value]) => !!value
        )
      )

      const response = await API.get(AD_ENDPOINT + "?user_type=athletic_directors", {
        params: queryParams,
      })

      return response.data
  },

  getOne: async (id: number): Promise<AdDto> => {
    const response = await API.get(`${AD_ENDPOINT}/${id}`)
    return response.data
  },

  create: async (data: CreateAdDto): Promise<AdDto> => {
    const response = await API.post(AD_ENDPOINT, data)
    return response.data
  },

  update: async (data: UpdateAdDto): Promise<AdDto> => {
    const response = await API.put(`${AD_ENDPOINT}/${data.id}`, data)
    return response.data
  },
}
