import {
  useQuery,
  useMutation,
  useQueryClient,
  UseMutationOptions,
} from "@tanstack/react-query"
import { ApiError, PaginationParams, queryKeys } from "@/shared/model"
import { adminsApi } from "../api/admin.queries"
import { AdminDto, CreateAdminDto, UpdateAdminDto } from "../api/dto/admin.dto"

export const useGetAdmins = (params: PaginationParams) => {
  return useQuery({
    queryKey: [queryKeys.userManagement.admins.get, params],
    queryFn: () => adminsApi.getAll(params),
  })
}

export const useGetAdmin = (id: number) => {
  return useQuery({
    queryKey: [queryKeys.userManagement.admins.get, id],
    queryFn: () => adminsApi.getOne(id),
    enabled: !!id,
  })
}

export const useCreateAdmin = (
  options?: UseMutationOptions<AdminDto, ApiError, CreateAdminDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: adminsApi.create,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.userManagement.admins.get],
        refetchType: "all",
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}

export const useUpdateAdmin = (
  options?: UseMutationOptions<AdminDto, ApiError, UpdateAdminDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: adminsApi.update,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.userManagement.admins.get],
        refetchType: "all",
      })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
