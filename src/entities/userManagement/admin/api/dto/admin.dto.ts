import { PaginationParams } from "@/shared/model"

export interface AdminDto {
  id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: number
  is_campaign_owner: boolean
  campaigns?: AdminCampaignDto[]
}

export interface AdminCampaignDto {
  id: number
  name: string
  is_campaign_owner: boolean
}

export interface CreateAdminDto {
  first_name: string
  last_name: string
  email: string
  phone: string
  password: string
  password_confirmation: string
  role_id: number
  is_campaign_owner: boolean | undefined
  campaigns?: UpdateAdminCampaignDto[]
}

export interface UpdateAdminDto {
  id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: number
  is_campaign_owner: boolean
  campaigns: UpdateAdminCampaignDto[]
}

export interface UpdateAdminCampaignDto {
  id: number
  is_campaign_owner: boolean
}

export interface AdminFilterDto {
  full_name?: string
  role_id?: number | null
}

export interface AdminQueryParams extends PaginationParams, AdminFilterDto {}
