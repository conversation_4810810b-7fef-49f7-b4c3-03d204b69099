import { API } from "@/shared/api"
import { CreateAdminDto, UpdateAdminDto, AdminDto } from "./dto/admin.dto"
import { PaginatedResponse, PaginationParams } from "@/shared/model"

const ADMINS_ENDPOINT = "/users"

export const adminsApi = {
  getAll: async (
    params: PaginationParams
  ): Promise<PaginatedResponse<AdminDto>> => {
    const response = await API.get(ADMINS_ENDPOINT + "?user_type=admins", {
      params: {
        page: params.page,
        per_page: params.per_page,
      },
    })
    return response.data
  },

  getOne: async (id: number): Promise<AdminDto> => {
    const response = await API.get(`${ADMINS_ENDPOINT}/${id}`)
    return response.data
  },

  create: async (data: CreateAdminDto): Promise<AdminDto> => {
    const response = await API.post(ADMINS_ENDPOINT, data)
    return response.data
  },

  update: async (data: UpdateAdminDto | UpdateAdminDto): Promise<AdminDto> => {
    const response = await API.put(`${ADMINS_ENDPOINT}/${data.id}`, data)
    return response.data
  },
}
