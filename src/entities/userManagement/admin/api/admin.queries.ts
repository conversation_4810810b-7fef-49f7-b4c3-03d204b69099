import { API } from "@/shared/api"
import {
  CreateAdminDto,
  UpdateAdminDto,
  AdminDto,
  AdminQueryParams
} from "./dto/admin.dto"
import { PaginatedResponse } from "@/shared/model"

const ADMINS_ENDPOINT = "/users"

export const adminsApi = {
  getAll: async (
    params: AdminQueryParams
  ): Promise<PaginatedResponse<AdminDto>> => {
    const queryParams = Object.fromEntries(
      Object.entries(params).filter(
        ([, value]) => !!value
      )
    )

    const response = await API.get(ADMINS_ENDPOINT + "?user_type=admins", {
      params: queryParams,
    })

    return response.data
  },

  getOne: async (id: number): Promise<AdminDto> => {
    const response = await API.get(`${ADMINS_ENDPOINT}/${id}`)
    return response.data
  },

  create: async (data: CreateAdminDto): Promise<AdminDto> => {
    const response = await API.post(ADMINS_ENDPOINT, data)
    return response.data
  },

  update: async (data: UpdateAdminDto | UpdateAdminDto): Promise<AdminDto> => {
    const response = await API.put(`${ADMINS_ENDPOINT}/${data.id}`, data)
    return response.data
  },
}
