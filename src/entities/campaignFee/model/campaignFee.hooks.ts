import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { ApiError, queryKeys } from "@/shared/model"
import { CampaignFeeApi } from "../api/campaignFee.queries"
import {
  CampaignFeeDto,
  UpdateCampaignFeeDto,
} from "../api/dto/campaignFee.dto"

const key = queryKeys.campaign.fee.get

export const useGetCampaignFee = (campaign_id: number) => {
  return useQuery({
    queryKey: [key, campaign_id],
    queryFn: () => CampaignFeeApi.getAll(campaign_id),
  })
}

export const useUpdateCampaignFee = (
  options?: UseMutationOptions<
    CampaignFeeDto[],
    ApiError,
    {
      campaignId: number
      data: UpdateCampaignFeeDto
    }
  >
) => {
  const queryClient = useQueryClient()

  return useMutation({
    ...options,
    mutationFn: ({ campaignId, data }) =>
      CampaignFeeApi.updateMultiple(campaignId, data),
    onSuccess: (data, campaignId, ...rest) => {
      queryClient.invalidateQueries({
        queryKey: [key, campaignId],
      })
      if (options?.onSuccess) {
        options.onSuccess(data, campaignId, ...rest)
      }
    },
  })
}
