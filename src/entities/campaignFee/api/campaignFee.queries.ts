import { API, apiUrls } from "@/shared/api"
import { CampaignFeeDto, UpdateCampaignFeeDto } from "./dto/campaignFee.dto"

export const CampaignFeeApi = {
  getAll: async (campaign_id: number): Promise<CampaignFeeDto[]> => {
    const response = await API.get(apiUrls.campaign.fee.list(campaign_id))
    return response.data
  },

  updateMultiple: async (
    campaign_id: number,
    body: UpdateCampaignFeeDto
  ): Promise<CampaignFeeDto[]> => {
    const response = await API.put(
      apiUrls.campaign.fee.updateMultiple(campaign_id),
      body
    )
    return response.data
  },
}
