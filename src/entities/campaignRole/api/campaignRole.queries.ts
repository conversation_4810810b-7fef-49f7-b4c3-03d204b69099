import { API } from "@/shared/api"
import { CampaignRoleDto } from "./dto/campaignRole.dto"

export const campaignRoleApi = {
  getAll: async (): Promise<CampaignRoleDto[]> => {
    const response = await API.get(`/campaign-roles`)
    return response.data
  },

  getById: async (id: number): Promise<CampaignRoleDto> => {
    const response = await API.get(`/campaign-roles/${id}`)
    return response.data
  },

  add: async ({ name }: { name: string }): Promise<CampaignRoleDto> => {
    const response = await API.post(`/campaign-roles`, {
      name,
    })
    return response.data
  },

  update: async (
    id: number,
    { name }: { name: string }
  ): Promise<CampaignRoleDto> => {
    const response = await API.put(`/campaign-roles/${id}`, {
      name,
    })
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    const response = await API.delete(`/campaign-roles/${id}`)
    return response.data
  },
}
