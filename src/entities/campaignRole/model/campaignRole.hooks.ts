import { useQuery } from "@tanstack/react-query"
import { campaignRoleApi } from "../api/campaignRole.queries"

export const useGetCampaignRoles = () => {
  return useQuery({
    queryKey: ["getCampaignRoles"],
    queryFn: () => campaignRoleApi.getAll(),
  })
}

export const useGetCampaignRoleById = (id: number) => {
  return useQuery({
    queryKey: ["getCampaignRoleById", id],
    queryFn: () => campaignRoleApi.getById(id),
  })
}

export const useCreateCampaignRole = (body: { name: string }) => {
  return useQuery({
    queryKey: ["createCampaignRole"],
    queryFn: () => campaignRoleApi.add(body),
  })
}

export const useUpdateCampaignRole = (id: number, body: { name: string }) => {
  return useQuery({
    queryKey: ["updateCampaignRole", id],
    queryFn: () => campaignRoleApi.update(id, body),
  })
}

export const useDeleteCampaignRole = (id: number) => {
  return useQuery({
    queryKey: ["deleteCampaignRole", id],
    queryFn: () => campaignRoleApi.delete(id),
  })
}
