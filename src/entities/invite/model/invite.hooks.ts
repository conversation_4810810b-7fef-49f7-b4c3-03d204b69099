import { ApiError, queryKeys } from "@/shared/model"
import {
  MutationOptions,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query"
import { SendInviteDto, ValidateInviteTokenDto } from "../api/dto/invite.dto"
import { inviteApi } from "../api/invite.queries"

export const useSendInvite = (
  options?: MutationOptions<void, ApiError, SendInviteDto>
) => {
  return useMutation({
    ...options,
    mutationFn: (body: SendInviteDto) => {
      return inviteApi.sendInvite(body)
    },
  })
}

export const useValidateInviteToken = (
  token: string,
  options?: MutationOptions<ValidateInviteTokenDto, ApiError, void>
) => {
  return useMutation({
    ...options,
    mutationFn: () => {
      return inviteApi.validateToken(token)
    },
  })
}

export const useDeleteInvite = (
  options?: MutationOptions<void, ApiError, number>
) => {
  const queryClient = useQueryClient()
  const keyGet = queryKeys.campaign.members.get

  return useMutation({
    ...options,
    mutationFn: (inviteId: number) => {
      return inviteApi.deleteInvite(inviteId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [keyGet] })
    },
  })
}
