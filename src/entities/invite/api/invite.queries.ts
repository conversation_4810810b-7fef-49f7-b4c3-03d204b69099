import { API, apiUrls } from "@/shared/api"
import { SendInviteDto, ValidateInviteTokenDto } from "./dto/invite.dto"

export const inviteApi = {
  validateToken: async (token: string): Promise<ValidateInviteTokenDto> => {
    const response = await API.post(apiUrls.campaign.invites.validateToken(), {
      invite_token: token,
    })
    return response.data
  },
  sendInvite: async (body: SendInviteDto) => {
    const response = await API.post(apiUrls.campaign.invites.sendInvite(), body)

    return response.data
  },
  deleteInvite: async (inviteId: number) => {
    const response = await API.delete(apiUrls.campaign.invites.delete(inviteId))
    return response.data
  },
}
