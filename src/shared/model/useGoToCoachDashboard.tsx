import { useCallback } from "react"
import { useNavigate } from "react-router-dom"
import { PATH } from "@/shared/config"
import {
  SELECTED_CAMPAIGN_KEY,
  useCampaignStore,
} from "./campaign/CampaignContext"
import { CampaignRole, campaignRoleToCampaignRoleId } from "./types"

export const AVAILABLE_COACH_DASHBOARD_ROLES = [
  campaignRoleToCampaignRoleId[CampaignRole.Coach],
  campaignRoleToCampaignRoleId[CampaignRole.AssistantCoach],
  campaignRoleToCampaignRoleId[CampaignRole.ProgramLeader],
  campaignRoleToCampaignRoleId[CampaignRole.BoosterClub],
]

export const useGoToCoachDashboard = () => {
  const { selectedCampaign, setSelectedCampaign } = useCampaignStore()
  const navigate = useNavigate()

  const goToCoachDashboard = useCallback(() => {
    if (!selectedCampaign) return

    const canChange = AVAILABLE_COACH_DASHBOARD_ROLES.includes(
      selectedCampaign?.campaign_role_id || 0
    )

    if (!canChange) return

    const campaignRoleName = selectedCampaign?.campaign_role_name
    const campaignRoleId = selectedCampaign?.campaign_role_id

    if (!campaignRoleId || !campaignRoleName) return

    localStorage.setItem(
      SELECTED_CAMPAIGN_KEY,
      JSON.stringify({
        ...selectedCampaign,
        current_campaign_role_id: campaignRoleId,
        current_campaign_role_name: campaignRoleName,
      })
    )
    setSelectedCampaign({
      ...selectedCampaign,
      current_campaign_role_id: campaignRoleId,
      current_campaign_role_name: campaignRoleName,
    })
    navigate(
      PATH.withAuth.campaign.dashboard.url(selectedCampaign?.campaign_id)
    )
  }, [navigate, selectedCampaign, setSelectedCampaign])

  return { goToCoachDashboard }
}
