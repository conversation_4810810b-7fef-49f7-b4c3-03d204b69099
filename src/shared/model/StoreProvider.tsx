import { ReactNode } from "react"
import { CampaignProvider } from "./campaign/CampaignContext"
import { MetaTagsProvider } from "./meta/MetaContext"
import { UserProvider } from "./user/UserContext"

interface StoreProviderProps {
  children: ReactNode
}

export const StoreProvider = ({ children }: StoreProviderProps) => {
  return (
    <UserProvider>
      <CampaignProvider>
        <MetaTagsProvider>{children}</MetaTagsProvider>
      </CampaignProvider>
    </UserProvider>
  )
}
