export const queryKeys = {
  salesPerson: {
    get: "getSalesPersons",
    create: "createSales<PERSON>erson",
    delete: "deleteSalesPerson",
  },

  campaign: {
    tips: {
      get: "getCampaignTips",
    },
    denomination: {
      get: "getCampaignDenominations",
    },
    donation: {
      get: "getCampaignDonations",
      getFilters: "getCampaignDonationsFilters",
      export: "exportCampaignDonations",
    },
    messageTemplates: {
      get: "getCampaignMessageTemplates",
      getById: "getCampaignMessageTemplate",
    },
    byGuid: "getCampaignByGuid",
    moments: {
      get: "useGetMoments",
    },
    settings: {
      get: "getCampaignSettings",
    },
    dripSchedules: {
      get: "useGetCampaignDripSchedules",
    },
    userMoments: {
      get: "useGetUserMoments",
    },
    members: {
      get: "getCampaignMembers",
      getById: "getCampaignMemberById",
    },
    donationMessage: {
      getById: "getCampaignDonationMessage",
    },
    images: {
      getLogo: "getCampaignLogo",
      getActionShot: "getCampaignActionShot",
      getHero: "getCampaignHero",
    },
    donationLeaderboard: {
      donors: "getCampaignDonationLeaderboardDonors",
      players: "getCampaignDonationLeaderboardPlayers",
      groups: "getCampaignDonationLeaderboardGroups",
    },
    donationUser: {
      get: "getDonationUser",
    },
    fund: {
      get: "getCampaignFund",
    },
    fee: {
      get: "getCampaignFee",
    },
    dashboard: {
      summary: "getCampaignSummary",
      quickStats: "getCampaignQuickStats",
      donationsReport: "getCampaignDonationsReport",
      checkRequests: "getCampaignCheckRequests",
      checkRequestFilters: "getCampaignCheckRequestFilters",
      messageLogs: "getCampaignMessageLogs",
      messageLogFilters: "getMessageLogFilters",
      checkRequestTotalAmount: "getCheckRequestTotalAmount",
    },
    messageLog: {
      get: "getCampaignMessageLog",
    },
  },
  sports: {
    get: "useGetSports",
  },
  userManagement: {
    users: {
      get: "getUsers",
    },
    admins: {
      get: "getAdmin",
    },
    ad: {
      get: "getAds",
    },
  },
  athleticDirectorDashboard: {
    summary: "getAdSummary",
    donations: "getAdDonations",
    donationsExport: "exportAdDonations",
    totals: "getAdTotals",
    donationFilters: "getAdDonationFilters",
  },
}
