export const Helpers = {
  objectToFormData: (
    obj: Record<string, any>,
    options?: {
      useEmpty?: boolean
    }
  ) => {
    const { useEmpty = false } = options || {}
    const formData = new FormData()

    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const value = obj[key]

        if (value === undefined || value === null) {
          if (useEmpty) {
            formData.append(key, "")
          }
        } else if (Array.isArray(value)) {
          value.forEach((item) => {
            if (item instanceof Blob) {
              formData.append(key, item)
            } else if (typeof item === "object" && item !== null) {
              formData.append(key, JSON.stringify(item))
            } else {
              formData.append(key, String(item))
            }
          })
        } else if (value instanceof Blob) {
          formData.append(key, value)
        } else if (typeof value === "object" && value !== null) {
          formData.append(key, JSON.stringify(value))
        } else {
          formData.append(key, String(value))
        }
      }
    }

    return formData
  },

  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return "0 Bytes"
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"]
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    const formattedSize = Math.round(bytes / Math.pow(1024, i))
    return `${formattedSize} ${sizes[i]}`
  },
}
