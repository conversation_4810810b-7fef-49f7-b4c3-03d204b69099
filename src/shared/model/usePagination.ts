import { useState } from "react"
import { GridPaginationModel } from "@mui/x-data-grid"
import { config } from "../config"

export const usePagination = () => {
  const [pageSize, setPageSize] = useState(config.pagination.perPage)
  const [page, setPage] = useState(1)

  const handlePaginationModelChange = (model: GridPaginationModel) => {
    setPage(model.page + 1)
    setPageSize(model.pageSize)
  }

  return {
    pageSize,
    page,
    onPaginationModelChange: handlePaginationModelChange,
  }
}
