import { UseFormSetError } from "react-hook-form"
import { toast } from "react-toastify"
import { ApiError } from "@/shared/model"

type BackendErrorResponse = {
  error: ApiError
  setError: UseFormSetError<any>
  message: string
}

export const handleServerErrors = ({
  error,
  setError,
  message,
}: BackendErrorResponse) => {
  const errors = error?.response?.data?.errors

  if (errors) {
    Object.entries(errors).forEach(([field, messages]) => {
      setError(field, {
        type: "manual",
        message: messages[0],
      })
    })
  } else {
    toast.error(error?.response?.data?.message || message)
  }
}
