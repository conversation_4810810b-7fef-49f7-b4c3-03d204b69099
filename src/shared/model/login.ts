import { PATH } from "@/shared/config"
import {
  CampaignRole,
  Roles,
  campaignRoleToCampaignRoleId,
} from "@/shared/model"

export const getInitialRouteByRole = (role: Roles) => {
  switch (role) {
    case Roles.SuperAdmin:
      return PATH.withAuth.home
    case Roles.Admin:
      return PATH.withAuth.home
    case Roles.User:
      return PATH.withAuth.selectCampaign
    case Roles.AD:
      return PATH.withAuth.athleticDirectorDashboard
    default:
      return PATH.withAuth.selectCampaign
  }
}

export const getInitialCampaignsRouteByRole = (role: Roles) => {
  switch (role) {
    case Roles.SuperAdmin:
      return PATH.withAuth.campaign.list
    case Roles.Admin:
      return PATH.withAuth.campaign.list
    case Roles.User:
      return PATH.withAuth.selectCampaign
    default:
      return PATH.withAuth.selectCampaign
  }
}

export const getInitialCampaignRouteByRole = (
  role: Roles | CampaignRole | null | number
) => {
  switch (role) {
    case CampaignRole.Participant:
    case campaignRoleToCampaignRoleId.Participant:
      return PATH.withAuth.campaign.donationInvite.list.url
    case CampaignRole.AssistantCoach:
    case CampaignRole.Coach:
    case CampaignRole.ProgramLeader:
    case CampaignRole.BoosterClub:
    case campaignRoleToCampaignRoleId.Coach:
    case campaignRoleToCampaignRoleId[CampaignRole.AssistantCoach]:
    case campaignRoleToCampaignRoleId[CampaignRole.ProgramLeader]:
    case campaignRoleToCampaignRoleId[CampaignRole.BoosterClub]:
      return PATH.withAuth.campaign.dashboard.url
    default:
      return PATH.withAuth.campaign.details.url
  }
}
