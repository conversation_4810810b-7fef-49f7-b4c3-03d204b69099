import { AxiosError } from "axios"

export interface User {
  id: number
  userRoleId: number
  role: Roles
  firstName: string
  lastName: string
  email: string
  phone: string
}

export enum Roles {
  SuperAdmin = "Super Admin",
  Admin = "Admin",
  User = "User",
  AD = "Athletic Director",
}

export enum CampaignRole {
  Coach = "Coach",
  Assistant<PERSON><PERSON><PERSON> = "Assistant Coach",
  AthleticDirector = "Athletic Director",
  Participant = "Participant",
  ProgramLeader = "Program Leader",
  BoosterClub = "Booster Club",
}

export const CampaignRoles = {
  Coach: "Coach",
  AssistantCoach: "Assistant Coach",
  AthleticDirector: "Athletic Director",
  Participant: "Participant",
  ProgramLeader: "Program Leader",
  BoosterClub: "Booster Club",
} as const

export const campaignRoleIdToCampaignRole: Record<number, CampaignRole> = {
  1: CampaignRole.Coach,
  2: CampaignRole.AssistantCoach,
  3: CampaignRole.AthleticDirector,
  4: CampaignRole.Participant,
  5: CampaignRole.ProgramLeader,
  6: CampaignRole.BoosterClub,
}

export const campaignRoleToCampaignRoleId: Record<CampaignRole, number> = {
  [CampaignRole.Coach]: 1,
  [CampaignRole.AssistantCoach]: 2,
  [CampaignRole.AthleticDirector]: 3,
  [CampaignRole.Participant]: 4,
  [CampaignRole.ProgramLeader]: 5,
  [CampaignRole.BoosterClub]: 6,
}

export interface PaginationParams {
  page: number
  per_page: number
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: {
    current_page: number
    per_page: number
    total: number
  }
}

export type ApiError = AxiosError<{
  message: string
  success: boolean
  data: unknown
  errors: Record<string, string | string[]>
}>

export const CampaignImageType = {
  portrait: "portrait",
  action_shot: "action_shot",
  hero: "hero",
  logo: "logo",
} as const

export const CampaignImageTypeById = {
  [CampaignImageType.portrait]: 1,
  [CampaignImageType.action_shot]: 2,
  [CampaignImageType.hero]: 3,
  [CampaignImageType.logo]: 4,
} as const

export type CampaignImageTypes =
  (typeof CampaignImageType)[keyof typeof CampaignImageType]
