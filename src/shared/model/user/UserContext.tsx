import { User } from "@/shared/model/types"
import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useState,
} from "react"
import { UserContextType } from "./user.types"

const UserContext = createContext<UserContextType | undefined>(undefined)

export const USER_STORE_KEY = "user"

interface UserProviderProps {
  children: ReactNode
}

const getInitialUserInfo = (): User | null => {
  const storedUser = localStorage.getItem(USER_STORE_KEY)
  if (storedUser) {
    try {
      return JSON.parse(storedUser)
    } catch (error) {
      console.error("Error parsing user from localStorage:", error)
      return null
    }
  }
  return null
}

export const UserProvider = ({ children }: UserProviderProps) => {
  const [userInfo, setUserInfo] = useState<User | null>(getInitialUserInfo())

  const handleSetUserInfo = useCallback((newUserInfo: User | null) => {
    setUserInfo(newUserInfo)
    if (newUserInfo) {
      localStorage.setItem(USER_STORE_KEY, JSON.stringify(newUserInfo))
    } else {
      localStorage.removeItem(USER_STORE_KEY)
    }
  }, [])

  const reset = useCallback(() => {
    setUserInfo(null)
    localStorage.removeItem(USER_STORE_KEY)
  }, [])

  return (
    <UserContext.Provider
      value={{ userInfo, setUserInfo: handleSetUserInfo, resetUserInfo: reset }}
    >
      {children}
    </UserContext.Provider>
  )
}

export const useUserStore = () => {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}
