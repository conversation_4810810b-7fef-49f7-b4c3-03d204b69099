import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useState,
} from "react"

export interface MetaTagsContextType {
  title: string
  description: string
  image: string
  url: string
  setMetaTags: (tags: Partial<MetaTags>) => void
  resetMetaTags: () => void
}

export interface MetaTags {
  title: string
  description: string
  image: string
  url: string
}

const defaultMetaTags: MetaTags = {
  title: "Aktivate Fundraising",
  description: "Support our fundraising campaign and make a difference!",
  image: "https://fundraising.aktivate.com/hubfs/Aktivate_Horz_RGB_Color-2.svg",
  url: window.location.origin,
}

const MetaTagsContext = createContext<MetaTagsContextType | undefined>(
  undefined
)

interface MetaTagsProviderProps {
  children: ReactNode
}

export const MetaTagsProvider = ({ children }: MetaTagsProviderProps) => {
  const [metaTags, setMetaTags] = useState<MetaTags>(defaultMetaTags)

  const handleSetMetaTags = useCallback((newMetaTags: Partial<MetaTags>) => {
    setMetaTags((prev) => ({
      ...prev,
      ...newMetaTags,
    }))
  }, [])

  const reset = useCallback(() => {
    setMetaTags(defaultMetaTags)
  }, [])

  return (
    <MetaTagsContext.Provider
      value={{
        ...metaTags,
        setMetaTags: handleSetMetaTags,
        resetMetaTags: reset,
      }}
    >
      {children}
    </MetaTagsContext.Provider>
  )
}

export const useMetaTags = () => {
  const context = useContext(MetaTagsContext)
  if (context === undefined) {
    throw new Error("useMetaTags must be used within a MetaTagsProvider")
  }
  return context
}
