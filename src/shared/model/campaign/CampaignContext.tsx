import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useState,
} from "react"
import { CampaignContextType, SelectedCampaign } from "./campaign.types"

const CampaignContext = createContext<CampaignContextType | undefined>(
  undefined
)

export const SELECTED_CAMPAIGN_KEY = "selectedCampaign"

const getInitialCampaign = (): SelectedCampaign | null => {
  const storedCampaign = localStorage.getItem(SELECTED_CAMPAIGN_KEY)
  if (storedCampaign) {
    try {
      return JSON.parse(storedCampaign)
    } catch (error) {
      console.error("Error parsing campaign from localStorage:", error)
      return null
    }
  }
  return null
}

interface CampaignProviderProps {
  children: ReactNode
}

export const CampaignProvider = ({ children }: CampaignProviderProps) => {
  const [selectedCampaign, setSelectedCampaignState] =
    useState<SelectedCampaign | null>(getInitialCampaign())

  const setSelectedCampaign = useCallback(
    (campaign: SelectedCampaign | null) => {
      setSelectedCampaignState(campaign)

      if (campaign) {
        localStorage.setItem(SELECTED_CAMPAIGN_KEY, JSON.stringify(campaign))
      } else {
        localStorage.removeItem(SELECTED_CAMPAIGN_KEY)
      }
    },
    []
  )

  const resetSelectedCampaign = useCallback(() => {
    setSelectedCampaignState(null)
    localStorage.removeItem(SELECTED_CAMPAIGN_KEY)
  }, [])

  return (
    <CampaignContext.Provider
      value={{
        selectedCampaign,
        setSelectedCampaign,
        resetSelectedCampaign,
      }}
    >
      {children}
    </CampaignContext.Provider>
  )
}

export const useCampaignStore = () => {
  const context = useContext(CampaignContext)
  if (context === undefined) {
    throw new Error("useCampaignStore must be used within a CampaignProvider")
  }
  return context
}
