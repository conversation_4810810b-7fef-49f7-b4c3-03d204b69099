// TODO: fix this
// It should not be imported from entities and should not copy type
// import { UserCampaignDto } from "@/entities/userCampaign"

export interface BaseSelectedCampaign {
  campaign_user_id: number | null
  campaign_id: number
  campaign_name: string
  campaign_role_id: number | null
  campaign_role_name: string | null
  campaign_group_id: number | null
}

interface CampaignWithoutCurrent extends BaseSelectedCampaign {
  current_campaign_role_name?: undefined
  current_campaign_role_id?: undefined
}
interface CampaignWithCurrent extends BaseSelectedCampaign {
  current_campaign_role_name: string | null
  current_campaign_role_id: number | null
}

export type SelectedCampaign = CampaignWithoutCurrent | CampaignWithCurrent

export interface CampaignContextType {
  selectedCampaign: SelectedCampaign | null
  setSelectedCampaign: (campaign: SelectedCampaign | null) => void
  resetSelectedCampaign: () => void
}
