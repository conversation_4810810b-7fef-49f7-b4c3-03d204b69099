import { useCallback } from "react"
import {
  SELECTED_CAMPAIGN_KEY,
  useCampaignStore,
} from "./campaign/CampaignContext"
import { getInitialCampaignRouteByRole } from "./login"
import {
  CampaignRole,
  CampaignRoles,
  campaignRoleToCampaignRoleId,
} from "./types"

export const AVAILABLE_CHANGE_DASHBOARD_ROLES = [
  campaignRoleToCampaignRoleId[CampaignRole.Coach],
  campaignRoleToCampaignRoleId[CampaignRole.AssistantCoach],
  campaignRoleToCampaignRoleId[CampaignRole.ProgramLeader],
  campaignRoleToCampaignRoleId[CampaignRole.BoosterClub],
]

export const useChangeDashboard = () => {
  const { selectedCampaign } = useCampaignStore()

  const changeDashboard = useCallback(() => {
    if (!selectedCampaign) return

    const canChange = AVAILABLE_CHANGE_DASHBOARD_ROLES.includes(
      selectedCampaign?.campaign_role_id || 0
    )

    const campaignRoleName = selectedCampaign?.campaign_role_name
    const campaignRoleId = selectedCampaign?.campaign_role_id

    if (!canChange || !campaignRoleId || !campaignRoleName) return

    const currentCampaignRoleId = selectedCampaign?.current_campaign_role_id

    const isCoach =
      !currentCampaignRoleId || currentCampaignRoleId === campaignRoleId

    const current_campaign_role_id = isCoach
      ? campaignRoleToCampaignRoleId.Participant
      : campaignRoleId

    const current_campaign_role_name = isCoach
      ? CampaignRoles.Participant
      : campaignRoleName

    localStorage.setItem(
      SELECTED_CAMPAIGN_KEY,
      JSON.stringify({
        ...selectedCampaign,
        current_campaign_role_id,
        current_campaign_role_name,
      })
    )

    const url = getInitialCampaignRouteByRole(
      current_campaign_role_name as CampaignRole
    )
    window.location.href = url(selectedCampaign.campaign_id)
  }, [selectedCampaign])

  return {
    changeDashboard,
  }
}
