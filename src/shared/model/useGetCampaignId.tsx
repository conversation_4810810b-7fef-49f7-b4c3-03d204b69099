import { useParams } from "react-router"

export function useGetCampaignId(returnAsNumber: true): number
export function useGetCampaignId(returnAsNumber?: false): string | undefined

export function useGetCampaignId(returnAsNumber?: boolean) {
  const { campaignId } = useParams<{ campaignId: string }>()

  if (returnAsNumber) {
    const parsed = Number(campaignId)
    return isNaN(parsed) ? 0 : parsed
  }

  return campaignId
}
