import "@testing-library/jest-dom"
import { fireEvent, render, screen } from "@testing-library/react"
import { useForm } from "react-hook-form"
import { Checkbox } from "./Checkbox"

describe("Checkbox", () => {
  it("renders checkbox with label", () => {
    render(<Checkbox name="test" label="Test Checkbox" />)

    expect(screen.getByText("Test Checkbox")).toBeInTheDocument()
    expect(screen.getByRole("checkbox")).toBeInTheDocument()
  })

  it("calls onChange when clicked", () => {
    const onChange = jest.fn()
    render(<Checkbox name="test" label="Test Checkbox" onChange={onChange} />)

    fireEvent.click(screen.getByRole("checkbox"))
    expect(onChange).toHaveBeenCalledWith(true)
  })

  it("can be disabled", () => {
    render(<Checkbox name="test" label="Test Checkbox" disabled />)

    expect(screen.getByRole("checkbox")).toBeDisabled()
  })

  it("shows error message", () => {
    render(
      <Checkbox
        name="test"
        label="Test Checkbox"
        error
        helperText="Error message"
      />
    )

    expect(screen.getByText("Error message")).toBeInTheDocument()
  })

  it("works with react-hook-form", () => {
    const formValues = { test: false }

    const TestComponent = () => {
      const { control } = useForm({
        defaultValues: formValues,
      })

      return <Checkbox name="test" label="Test Checkbox" control={control} />
    }

    render(<TestComponent />)

    const checkbox = screen.getByRole("checkbox")
    expect(checkbox).not.toBeChecked()

    fireEvent.click(checkbox)
    expect(checkbox).toBeChecked()
  })
})
