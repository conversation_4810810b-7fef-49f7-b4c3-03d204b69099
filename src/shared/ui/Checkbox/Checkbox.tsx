import {
  FormControlLabel,
  FormHelperText,
  Checkbox as M<PERSON><PERSON>heckbox,
} from "@mui/material"
import { ReactNode } from "react"
import {
  Control,
  Controller,
  FieldValues,
  Path,
  PathValue,
} from "react-hook-form"

type CheckboxProps<T extends FieldValues = FieldValues> = {
  name: Path<T>
  label: ReactNode
  control?: Control<T>
  defaultValue?: boolean
  onChange?: (checked: boolean) => void
  disabled?: boolean
  error?: boolean
  helperText?: string
}

export const Checkbox = <T extends FieldValues = FieldValues>({
  name,
  label,
  control,
  defaultValue = false,
  onChange,
  disabled = false,
  error = false,
  helperText,
}: CheckboxProps<T>) => {
  return control ? (
    <div>
      <Controller<T>
        name={name}
        control={control}
        defaultValue={defaultValue as PathValue<T, Path<T>>}
        render={({ field }) => (
          <>
            <FormControlLabel
              control={
                <MUICheckbox
                  {...field}
                  checked={field.value}
                  onChange={(e) => {
                    field.onChange(e.target.checked)
                    if (onChange) onChange(e.target.checked)
                  }}
                  disabled={disabled}
                />
              }
              label={label}
            />
            {error && helperText && (
              <FormHelperText error>{helperText}</FormHelperText>
            )}
          </>
        )}
      />
    </div>
  ) : (
    <div>
      <FormControlLabel
        control={
          <MUICheckbox
            name={name}
            defaultChecked={defaultValue}
            onChange={(e) => onChange?.(e.target.checked)}
            disabled={disabled}
          />
        }
        label={label}
      />
      {error && helperText && (
        <FormHelperText error>{helperText}</FormHelperText>
      )}
    </div>
  )
}
