import { IconButton } from "@/shared/ui"
import CloseIcon from "@mui/icons-material/Close"
import {
  DialogActions,
  DialogContent,
  DialogTitle,
  Dialog as MUIDialog,
} from "@mui/material"

type DialogProps = {
  open: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  actions?: React.ReactNode
  maxWidth?: "xs" | "sm" | "md" | "lg" | "xl"
  fullWidth?: boolean
  fullScreen?: boolean
}

export const Dialog = ({
  open,
  onClose,
  title,
  children,
  actions,
  maxWidth = "sm",
  fullWidth = true,
  fullScreen = false,
}: DialogProps) => {
  return (
    <MUIDialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      fullScreen={fullScreen}
    >
      <DialogTitle className="flex items-center justify-between">
        {title}
        <IconButton onClick={onClose} size="small" title="Close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>{children}</DialogContent>
      {actions && <DialogActions>{actions}</DialogActions>}
    </MUIDialog>
  )
}
