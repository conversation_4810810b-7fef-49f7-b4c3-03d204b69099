import { Button } from "@mui/material"
import "@testing-library/jest-dom"
import { fireEvent, render, screen } from "@testing-library/react"
import { Dialog } from "./Dialog"

describe("Dialog", () => {
  const defaultProps = {
    open: true,
    onClose: jest.fn(),
    title: "Test Dialog",
    children: <div>Dialog content</div>,
  }

  it("renders dialog with title and content", () => {
    render(<Dialog {...defaultProps} />)
    
    expect(screen.getByText("Test Dialog")).toBeInTheDocument()
    expect(screen.getByText("Dialog content")).toBeInTheDocument()
  })

  it("calls onClose when close button is clicked", () => {
    const onClose = jest.fn()
    render(<Dialog {...defaultProps} onClose={onClose} />)
    
    fireEvent.click(screen.getByRole("button"))
    expect(onClose).toHaveBeenCalled()
  })

  it("renders actions when provided", () => {
    const actions = (
      <>
        <Button>Cancel</Button>
        <Button>Confirm</Button>
      </>
    )
    
    render(<Dialog {...defaultProps} actions={actions} />)
    
    expect(screen.getByText("Cancel")).toBeInTheDocument()
    expect(screen.getByText("Confirm")).toBeInTheDocument()
  })

  it("does not render actions when not provided", () => {
    render(<Dialog {...defaultProps} />)
    
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument()
  })
}) 