import { useState } from "react"
import { <PERSON> } from "react-router-dom"
import {
  Avatar,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Popover,
} from "@mui/material"
type IAction = {
  label: string
  icon: React.ReactNode
  onClick?: () => void
  selected?: boolean
  path?: string
}

interface UserMenuProps {
  titlePrimary?: string
  titleSecondary?: string
  actions?: IAction[]
  additionalActions?: IAction[]
  userName: string
}

const mapActions = (action: IAction) => (
  <ListItemButton
    key={action.label}
    component={"onClick" in action ? "button" : Link}
    onClick={"onClick" in action ? action.onClick : undefined}
    to={"path" in action ? action.path : undefined}
    className="!px-4 !py-2 w-full hover:bg-gray-100"
    selected={!!action.selected}
  >
    <ListItemIcon>{action.icon}</ListItemIcon>
    <ListItemText primary={action.label} />
  </ListItemButton>
)

export const UserMenu = ({
  titlePrimary,
  titleSecondary,
  actions,
  additionalActions,
  userName,
}: UserMenuProps) => {
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null)

  const handleClick = (event: React.MouseEvent) => {
    setAnchorEl(event.currentTarget as HTMLDivElement)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const open = Boolean(anchorEl)
  const id = open ? "user-menu" : undefined

  return (
    <div>
      <button
        aria-describedby={id}
        onClick={handleClick}
        className="cursor-pointer !p-0 !bg-transparent outline-none border-none"
      >
        <Avatar>{userName}</Avatar>
      </button>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <List className="!p-2">
          <ListItem className="!px-4 !py-2">
            <ListItemText primary={titlePrimary} secondary={titleSecondary} />
          </ListItem>

          {(actions?.length || 0) > 0 && (
            <>
              <Divider className="!mb-1" />
              {actions?.map(mapActions)}
            </>
          )}
          {(additionalActions?.length || 0) > 0 && (
            <>
              <Divider className="!mb-1" />
              {additionalActions?.map(mapActions)}
            </>
          )}
          <Divider className="!mb-1" />
        </List>
      </Popover>
    </div>
  )
}
