import "@testing-library/jest-dom"
import { fireEvent, render, screen } from "@testing-library/react"
import { Drawer } from "./Drawer"

describe("Drawer", () => {
  const defaultProps = {
    open: true,
    onClose: jest.fn(),
    title: "Test Drawer",
    children: <div>Drawer content</div>,
  }

  it("renders drawer with title and content", () => {
    render(<Drawer {...defaultProps} />)
    
    expect(screen.getByText("Test Drawer")).toBeInTheDocument()
    expect(screen.getByText("Drawer content")).toBeInTheDocument()
  })

  it("calls onClose when close button is clicked", () => {
    const onClose = jest.fn()
    render(<Drawer {...defaultProps} onClose={onClose} />)
    
    fireEvent.click(screen.getByRole("button"))
    expect(onClose).toHaveBeenCalled()
  })

  it("renders with custom width", () => {
    render(<Drawer {...defaultProps} width={600} />)
    
    const drawer = screen.getByRole("presentation").querySelector(".MuiPaper-root")
    expect(drawer).toHaveStyle({ width: "600px" })
  })

  it("renders with custom anchor", () => {
    render(<Drawer {...defaultProps} anchor="left" />)
    
    const drawer = screen.getByRole("presentation")
    expect(drawer).toHaveClass("MuiDrawer-root", "MuiDrawer-modal")
    expect(drawer.querySelector(".MuiDrawer-paperAnchorLeft")).toBeInTheDocument()
  })
}) 