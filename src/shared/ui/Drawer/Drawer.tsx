import { IconButton } from "@/shared/ui"
import CloseIcon from "@mui/icons-material/Close"
import { Box, Drawer as <PERSON><PERSON><PERSON>raw<PERSON>, Typography } from "@mui/material"
import React from "react"

type DrawerProps = {
  open: boolean
  onClose: () => void
  title: string

  children: React.ReactNode
  anchor?: "left" | "right" | "top" | "bottom"
  width?: number | string
}

export const Drawer = ({
  open,
  onClose,
  title,
  children,
  anchor = "left",
  width = 400,
}: DrawerProps) => {
  return (
    <MUIDrawer
      anchor={anchor}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: width,
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Box className="flex items-center justify-between mb-4">
          <Typography variant="h6">{title}</Typography>
          <IconButton onClick={onClose} size="small" title="Close">
            <CloseIcon />
          </IconButton>
        </Box>
        {children}
      </Box>
    </MUIDrawer>
  )
}
