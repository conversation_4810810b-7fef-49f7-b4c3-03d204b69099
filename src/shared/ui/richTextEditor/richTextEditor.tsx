import { Box, Paper } from "@mui/material"
import Color from "@tiptap/extension-color"
import Image from "@tiptap/extension-image"
import TextStyle from "@tiptap/extension-text-style"
import { EditorContent, useEditor } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import { FC, useEffect } from "react"
import { FontSize } from "./extensions/fontSize"
import "./style.css"

import HardBreak from "@tiptap/extension-hard-break"
import { MenuBar } from "./components/menuBar"

interface RichTextEditorProps {
  content?: string
  initialContent?: string
  onChange?: (content: string) => void
  hideToolbar?: boolean
}

export const RichTextEditor: FC<RichTextEditorProps> = ({
  content = "",
  initialContent = "",
  hideToolbar,
  onChange,
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit,
      TextStyle.configure(),
      Color,
      Image,
      FontSize,
      HardBreak.extend({
        addKeyboardShortcuts() {
          return {
            Enter: () => this.editor.commands.setHardBreak(),
          }
        },
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML())
    },
  })

  useEffect(() => {
    if (initialContent) {
      editor?.commands.setContent(initialContent)
    }
  }, [initialContent, editor])

  if (!editor) {
    return null
  }

  return (
    <Paper variant="outlined">
      {!hideToolbar && (
        <Box className="flex gap-2 p-2 border-b border-gray-200">
          <MenuBar editor={editor} />
        </Box>
      )}
      <Box className="min-h-[300px] p-2">
        <EditorContent editor={editor} />
      </Box>
    </Paper>
  )
}
