import { Button } from "@/shared/ui/Button"
import { Input } from "@/shared/ui/Input"
import { Select } from "@/shared/ui/Select"
import FormatBoldIcon from "@mui/icons-material/FormatBold"
import { Editor } from "@tiptap/react"

const fontSizeOptions = [
  { value: "12px", label: "12px" },
  { value: "14px", label: "14px" },
  { value: "16px", label: "16px" },
  { value: "18px", label: "18px" },
  { value: "20px", label: "20px" },
]

export const MenuBar = ({ editor }: { editor: Editor }) => {

  if (!editor) {
    return null
  }

  const toggleBold = () => {
    editor.chain().focus().toggleBold().run()
  }

  const setFontSize = (size: string) => {
    editor.chain().focus().setFontSize(size).run()
  }

  const setColor = (color: string) => {
    editor.chain().focus().setColor(color).run()
  }

  const currentFontSize = editor.getAttributes("textStyle").size

  const currentColor = editor.getAttributes("textStyle").color

  return (
    <>
      <Button
        onClick={toggleBold}
        variant={editor.isActive("bold") ? "contained" : "outlined"}
        size="small"
        fullWidth={false}
      >
        <FormatBoldIcon />
      </Button>

      <Select
        name="fontSize"
        label="Size"
        options={fontSizeOptions}
        value={currentFontSize ?? "16px"}
        defaultValue={currentFontSize ?? "16px"}
        onChange={(e) => setFontSize(e.target.value as string)}
        fullWidth={false}
        size="small"
      />

      <Input
        variant="outlined"
        type="color"
        label="Color"
        fullWidth={false}
        size="small"
        value={currentColor ?? "#000000"}
        showHelperText={false}
        onChange={(e) => setColor((e.target as HTMLInputElement).value)}
        sx={{
          width: "80px",
        }}
      />
    </>
  )
}
