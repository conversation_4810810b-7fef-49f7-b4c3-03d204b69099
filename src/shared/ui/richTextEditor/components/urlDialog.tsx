import { Input } from "@/shared/ui/Input"
import { useState } from "react"
import { ConfirmDialog } from "../../ConfirmDialog/ConfirmDialog"

export const UrlDialog = ({
  isOpen,
  onClose,
  handleSetUrl,
}: {
  isOpen: boolean
  onClose: () => void
  handleSetUrl: (url: string) => void
}) => {
  const [url, setUrl] = useState("")

  const handleApply = () => {
    handleSetUrl(url)
    onClose()
  }

  return (
    <ConfirmDialog
      open={isOpen}
      onClose={onClose}
      onConfirm={handleApply}
      title="Paste your image URL"
      content=""
    >
      <Input value={url} label="URL" onChange={(e) => setUrl(e.target.value)} />
    </ConfirmDialog>
  )
}
