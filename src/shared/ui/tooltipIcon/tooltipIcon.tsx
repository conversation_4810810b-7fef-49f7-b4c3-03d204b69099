import { Tooltip, Typography, TooltipProps } from "@mui/material"
import HelpIcon from "@mui/icons-material/Help"
import { ReactNode, useState } from "react"

interface TooltipIconProps {
  text?: string | ReactNode
  iconSize?: "inherit" | "small" | "medium" | "large"
  iconClassName?: string
  placement?: TooltipProps["placement"]
  tooltipProps?: Partial<TooltipProps>
}

export const TooltipIcon = ({
  text = "Tooltip info goes here",
  iconSize = "small",
  iconClassName = "cursor-pointer",
  placement = "right",
  tooltipProps = {},
}: TooltipIconProps) => {
  const [showTooltip, setShowTooltip] = useState(false)

  return (
    <Tooltip
      open={showTooltip}
      onOpen={() => setShowTooltip(true)}
      onClose={() => setShowTooltip(false)}
      title={
        typeof text === "string" ? (
          <Typography className="!text-white">{text}</Typography>
        ) : (
          text
        )
      }
      placement={placement}
      {...tooltipProps}
    >
      <HelpIcon
        fontSize={iconSize}
        className={iconClassName}
        onClick={() => setShowTooltip(!showTooltip)}
      />
    </Tooltip>
  )
}
