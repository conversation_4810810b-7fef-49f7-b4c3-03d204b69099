import {
  CircularProgress,
  Autocomplete as MUIAutocomplete,
  TextField,
} from "@mui/material"
import { Control, Controller } from "react-hook-form"

// NOTE: make sure if you pass 'multiple' prop, you also pass 'value' prop as an array

export interface Option {
  label: string
  value: string | number
}

interface AutocompleteProps {
  name: string
  label: string
  options: Option[]
  control?: Control<any>
  error?: boolean
  helperText?: string
  disabled?: boolean
  required?: boolean
  placeholder?: string
  showHelperText?: boolean
  multiple?: boolean
  defaultValue?: Option | Option[] | null
  value?: Option | Option[] | null
  onChange?: (value: Option | Option[] | null) => void
  isLoading?: boolean
  fieldVariant?: "outlined" | "standard"
  disableClearable?: boolean
}

export const Autocomplete = ({
  name,
  label,
  options,
  control,
  error = false,
  helperText = " ",
  disabled = false,
  required = false,
  placeholder,
  multiple = false,
  defaultValue = null,
  showHelperText = true,
  fieldVariant = "outlined",
  value,
  onChange,
  isLoading = false,
  disableClearable = false,
  ...rest
}: AutocompleteProps) => {
  const autocompleteComponent = (
    <MUIAutocomplete
      options={options}
      getOptionLabel={(option: string | Option) =>
        typeof option === "string" ? option : option.label
      }
      isOptionEqualToValue={(option, value) =>
        typeof option === "string" || typeof value === "string"
          ? option === value
          : option.value === value.value
      }
      multiple={multiple}
      disabled={disabled}
      loading={isLoading}
      defaultValue={defaultValue}
      onChange={(_, value: string | Option | (string | Option)[] | null) =>
        onChange?.(value as Option | Option[] | null)
      }
      value={value}
      disableClearable={disableClearable}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          error={error}
          helperText={showHelperText ? helperText : undefined}
          placeholder={placeholder}
          variant={fieldVariant}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {isLoading ? (
                  <CircularProgress color="inherit" size={20} />
                ) : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
          InputLabelProps={{
            required: required,
            sx: {
              "& .MuiInputLabel-asterisk": {
                color: "error.main",
              },
            },
          }}
        />
      )}
      {...rest}
    />
  )

  if (control) {
    return (
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        render={({ field: { onChange: fieldOnChange, value, ...field } }) => (
          <MUIAutocomplete
            {...field}
            disableClearable={disableClearable}
            options={options}
            getOptionLabel={(option: string | Option) =>
              typeof option === "string" ? option : option.label
            }
            isOptionEqualToValue={(option, value) =>
              typeof option === "string" || typeof value === "string"
                ? option === value
                : option.value === value.value
            }
            multiple={multiple}
            disabled={disabled}
            value={value}
            loading={isLoading}
            onChange={(_, value) => {
              fieldOnChange(value as Option | Option[] | null)
              onChange?.(value as Option | Option[] | null)
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label={label}
                error={error}
                helperText={showHelperText ? helperText : undefined}
                placeholder={placeholder}
                variant={fieldVariant}
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isLoading ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
                InputLabelProps={{
                  required: required,
                  sx: {
                    "& .MuiInputLabel-asterisk": {
                      color: "error.main",
                    },
                  },
                }}
              />
            )}
            {...rest}
          />
        )}
      />
    )
  }

  return autocompleteComponent
}
