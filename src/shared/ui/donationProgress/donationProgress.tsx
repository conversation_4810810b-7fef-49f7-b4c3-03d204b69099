import { formatCurrency } from "@/shared/lib/formatCurrency"
import { getDaysLeftText } from "@/shared/lib/getDaysLeftText"
import { hexToRgba } from "@/shared/lib/hexToRgba"
import { Box, LinearProgress } from "@mui/material"

type DonationProgressProps = {
  progressInfo: {
    raised: number
    goal: number
    daysLeft: number
    progress?: number
  }
  colors?: {
    primary: string
    secondary: string
  }
  isDashboard?: boolean
}

export const DonationProgress = ({
  progressInfo,
  colors = { primary: "#ad4120", secondary: "#ad4120" },
  isDashboard = false,
}: DonationProgressProps) => {
  return (
    <div
      className={`mt-6 px-4 md:px-[30px] py-8 mx-4 md:mx-[30px] rounded-lg ${
        isDashboard ? "bg-white shadow-md" : "bg-[#F6F6F6]"
      }`}
      role="region"
      aria-label="Fundraising progress"
    >
      <div className="flex hidden md:flex">
        <div className="flex order-1 w-1/6 flex-col">
          <div className="h-[17px]">
            <span className="text-xs tracking-[0.17em] text-gray-500">
              RAISED
            </span>
          </div>
          <div
            className="mt-[10px] text-3xl font-bold text-primary tracking-tight"
            aria-label={`Raised ${formatCurrency(progressInfo.raised)}`}
          >
            ${formatCurrency(progressInfo.raised)}
          </div>
        </div>
        <div className="flex-1 order-2 mx-4">
          <div className="flex flex-col justify-between h-[52px]">
            <div className="text-center">
              <span
                className="text-sm font-bold text-white rounded-full px-2 py-1"
                style={{ backgroundColor: colors.primary }}
                role="status"
                aria-label={`${progressInfo.daysLeft} days remaining`}
              >
                {getDaysLeftText(progressInfo.daysLeft ?? 0)}
              </span>
            </div>
            <Box sx={{ width: "100%" }}>
              <LinearProgress
                variant="determinate"
                value={progressInfo.progress}
                aria-label={`Progress: ${progressInfo.progress}%`}
                sx={{
                  height: "20px",
                  borderRadius: "14px",
                  backgroundColor: hexToRgba(colors.primary ?? "", 0.5),
                  "& .MuiLinearProgress-bar": {
                    backgroundColor: colors.primary,
                  },
                }}
              />
            </Box>
          </div>
        </div>
        <div className="flex order-3 flex-col w-1/6 items-end">
          <div className="h-[17px]">
            <span className="text-xs tracking-[0.17em] text-gray-500">
              GOAL
            </span>
          </div>
          <div
            className="mt-[10px] text-3xl font-bold tracking-tight"
            aria-label={`Goal amount ${formatCurrency(progressInfo.goal)}`}
          >
            ${formatCurrency(progressInfo.goal)}
          </div>
        </div>
      </div>
      {/* Mobile version */}
      <div className="flex flex-col md:hidden">
        <div className="text-center">
          <span
            className="text-sm font-bold text-white rounded-full px-2 py-1"
            role="status"
            style={{ backgroundColor: colors.primary }}
            aria-label={`${progressInfo.daysLeft} days remaining`}
          >
            {getDaysLeftText(progressInfo.daysLeft)}
          </span>
        </div>
        <div className="flex justify-between w-full mb-5">
          <div className="flex flex-col">
            <div className="h-[17px]">
              <span className="text-xs tracking-[0.17em] text-gray-500">
                RAISED
              </span>
            </div>
            <div
              className="mt-[10px] text-3xl font-bold text-primary tracking-tight"
              aria-label={`Raised ${formatCurrency(progressInfo.raised)}`}
            >
              ${formatCurrency(progressInfo.raised)}
            </div>
          </div>

          <div className="flex flex-col items-end">
            <div className="h-[17px]">
              <span className="text-xs tracking-[0.17em] text-gray-500">
                GOAL
              </span>
            </div>
            <div
              className="mt-[10px] text-3xl font-bold tracking-tight"
              aria-label={`Goal amount ${formatCurrency(progressInfo.goal)}`}
            >
              ${formatCurrency(progressInfo.goal)}
            </div>
          </div>
        </div>
        <Box sx={{ width: "100%" }}>
          <LinearProgress
            variant="determinate"
            value={progressInfo.progress}
            aria-label={`Progress: ${progressInfo.progress}%`}
            sx={{
              height: "20px",
              borderRadius: "14px",
              backgroundColor: hexToRgba(colors.primary ?? "", 0.5),
              "& .MuiLinearProgress-bar": {
                backgroundColor: colors.primary,
              },
            }}
          />
        </Box>
      </div>
    </div>
  )
}
