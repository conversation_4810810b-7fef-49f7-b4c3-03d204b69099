import { Button } from "../Button"
import { Dialog } from "../Dialog"

type ConfirmDialogProps = {
  open: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  content: string
  confirmText?: string
  cancelText?: string
  children?: React.ReactNode
}

export const ConfirmDialog = ({
  open,
  onClose,
  onConfirm,
  title,
  content,
  confirmText = "Confirm",
  cancelText = "Cancel",
  children,
}: ConfirmDialogProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      title={title}
      actions={
        <>
          <Button
            variant="outlined"
            onClick={onClose}
            fullWidth={false}
          >
            {cancelText}
          </Button>
          <Button
            variant="contained"
            onClick={onConfirm}
            fullWidth={false}
            color="error"
          >
            {confirmText}
          </Button>
        </>
      }
    >
      <p>{content}</p>
      {children}
    </Dialog>
  )
}