import { config } from "@/shared/config"
import { Box, CircularProgress, SxProps } from "@mui/material"
import {
  DataGrid,
  GridColDef,
  GridFilterModel,
  GridPaginationModel,
  GridRowParams,
  GridSortModel,
  DataGridProps,
} from "@mui/x-data-grid"
import { Button } from "../Button"

export type Column<T> = Omit<GridColDef, "field"> & {
  field: keyof T | string
}

type Action = {
  label: string
  onClick: () => void
  variant?: "text" | "contained" | "outlined"
}

interface TableProps<T> extends DataGridProps {
  columns: GridColDef[]
  rows: T[]
  rowCount?: number
  page?: number
  pageSize?: number
  loading?: boolean
  sortModel?: GridSortModel
  filterModel?: GridFilterModel
  hideFooter?: boolean
  pageSizeOptions?: number[]
  hideFooterPagination?: boolean
  onPaginationModelChange?: (model: GridPaginationModel) => void
  onSortModelChange?: (model: GridSortModel) => void
  onFilterModelChange?: (model: GridFilterModel) => void
  height?: number | string
  onRowClick?: (params: GridRowParams) => void
  boxClassName?: string
  sx?: SxProps
  actions?: Action[]
  isLoading?: boolean
}

export const Table = <T extends { id: string | number }>({
  columns,
  rows,
  rowCount,
  page = 0,
  pageSize = config.pagination.perPage,
  pageSizeOptions = config.pagination.pageSizeOptions,
  loading,
  sortModel,
  filterModel,
  onPaginationModelChange,
  onSortModelChange,
  onFilterModelChange,
  hideFooterPagination = false,
  hideFooter = false,
  onRowClick,
  boxClassName = "w-full",
  sx,
  actions = [],
  isLoading = false,
  ...rest
}: TableProps<T>) => {
  return (
    <Box className={boxClassName}>
      {isLoading && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}
      <div className="flex gap-2 justify-end mb-2 ">
        {actions.map((action, index) => (
          <Button
            key={`${index}-${action.label}`}
            onClick={action.onClick}
            variant={action.variant || "contained"}
            fullWidth={false}
          >
            {action.label}
          </Button>
        ))}
      </div>
      <DataGrid
        rows={rows}
        columns={columns}
        rowCount={rowCount || rows.length}
        loading={loading}
        pageSizeOptions={pageSizeOptions}
        paginationModel={{ page, pageSize }}
        paginationMode="server"
        sortingMode="server"
        filterMode="server"
        sortModel={sortModel}
        filterModel={filterModel}
        onPaginationModelChange={onPaginationModelChange}
        onSortModelChange={onSortModelChange}
        onFilterModelChange={onFilterModelChange}
        disableRowSelectionOnClick
        hideFooterPagination={hideFooterPagination}
        hideFooter={hideFooter}
        onRowClick={onRowClick}
        {...rest}
        sx={{
          "& .MuiDataGrid-row": {
            cursor: onRowClick ? "pointer" : "default",
          },
          ...sx,
        }}
      />
    </Box>
  )
}
