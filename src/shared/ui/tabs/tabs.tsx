import { Tabs as MuiTabs, Tab } from "@mui/material"

interface TabsProps {
  tabs: {
    label: string
    path: string
  }[]
  currentTab: string
  handleTabChange: (event: React.SyntheticEvent, newValue: string) => void
}

export const Tabs = ({ tabs, currentTab, handleTabChange }: TabsProps) => {
    return (
        <MuiTabs
        value={currentTab}
        onChange={handleTabChange}      
        variant="scrollable"
        scrollButtons="auto"
      >
        {tabs.map((tab) => (
          <Tab key={tab.label} label={tab.label} value={tab.path} />
        ))}
      </MuiTabs>
    )
}