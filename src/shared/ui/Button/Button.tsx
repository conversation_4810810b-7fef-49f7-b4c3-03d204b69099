import {
  CircularProgress,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tonProps as <PERSON><PERSON>ButtonP<PERSON>,
} from "@mui/material"
import React, { PropsWithChildren } from "react"

export type ButtonProps = PropsWithChildren &
  Omit<MUIButtonProps, "startIcon" | "endIcon" | "children"> & {
    size?: "small" | "medium" | "large"
    disabled?: boolean
    dataTestId?: string
    type?: "button" | "reset" | "submit"
    isLoading?: boolean
    startIcon?: React.ReactNode
    endIcon?: React.ReactNode
  }

export const Button = (props: ButtonProps) => {
  const {
    size = "small",
    variant = "contained",
    disabled = false,
    fullWidth = true,
    isLoading = false,
    startIcon = null,
    children,
    ...rest
  } = props

  return (
    <MUIButton
      {...rest}
      size={size}
      variant={variant}
      fullWidth={fullWidth}
      data-testid={props.dataTestId || "global-button-component"}
      disabled={isLoading || disabled}
      startIcon={isLoading ? <CircularProgress size={20} /> : startIcon}
    >
      {children}
    </MUIButton>
  )
}
