import {
  confirmable,
  createConfirmationCreater,
  createReactTreeMounter,
  createMountPoint,
} from "react-confirm"
import { ConfirmDialog } from "../ConfirmDialog"

const mounter = createReactTreeMounter()

export const createConfirmation = createConfirmationCreater(mounter)
export const MountConfirmation = createMountPoint(mounter)

interface IOptions {
  title: string
  message: string
  acceptText?: string
  declineText?: string
  shouldAcceptOnClose?: boolean
}

interface AsyncConfirmDialogProps extends IOptions {
  show?: boolean
  proceed?: (value?: boolean) => void
}

const AsyncConfirmDialog = confirmable(
  ({
    show,
    proceed,
    title,
    message,
    acceptText,
    declineText,
    shouldAcceptOnClose,
  }: AsyncConfirmDialogProps) => (
    <ConfirmDialog
      open={!!show}
      title={title}
      content={message}
      confirmText={acceptText}
      cancelText={declineText}
      onConfirm={() => {
        proceed?.(true)
      }}
      onClose={() => (shouldAcceptOnClose ? proceed?.(true) : proceed?.(false))}
    />
  )
)

const confirm = createConfirmation(AsyncConfirmDialog)

export const asyncConfirm = (options?: IOptions) => {
  const {
    title = "Are you sure?",
    message = "Are you sure you want to do this?",
    acceptText = "Yes",
    declineText = "Cancel",
    shouldAcceptOnClose,
  } = options || {}

  const func = confirm({
    title,
    message,
    acceptText,
    declineText,
    shouldAcceptOnClose,
  })

  return func
}
