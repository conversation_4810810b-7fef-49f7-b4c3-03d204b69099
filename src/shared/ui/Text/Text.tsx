import { Typography } from "@mui/material"
import { Variant } from "@mui/material/styles/createTypography"
import { PropsWithChildren } from "react"

export type TextProps = PropsWithChildren & {
  noWrap?: boolean
  classes?: string
  variant?: Variant
  color?: string
  fontSize?: number
  dataTestId?: string
  className?: string
}

export const Text = (props: TextProps) => {
  const {
    noWrap = false,
    className = "",
    variant = "inherit",
    fontSize = 12,
    color = "black",
    dataTestId = "text-style-wrapper",
    children,
  } = props

  return (
    <Typography
      data-testid={dataTestId}
      noWrap={noWrap}
      className={className}
      variant={variant}
      sx={{
        fontSize,
        color,
      }}
    >
      {children || null}
    </Typography>
  )
}
