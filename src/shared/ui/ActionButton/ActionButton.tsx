import { canAccess, generatePatternFromPath } from "@/shared/config"
import { useUserStore } from "@/shared/model"
import { usePermissions } from "@/shared/model/usePermissions"
import { IconButton } from "@/shared/ui"
import DeleteIcon from "@mui/icons-material/Delete"
import EditIcon from "@mui/icons-material/Edit"
import OpenInNewIcon from "@mui/icons-material/OpenInNew"
import VisibilityIcon from "@mui/icons-material/Visibility"
import { useMemo } from "react"
import { useLocation, useParams } from "react-router"
import { Button, ButtonProps } from "../Button"

type IconType = "delete" | "edit" | "details" | "view"
type ActionType = "delete" | "edit" | "create" | "details" | "view"

type BaseProps = {
  typeAction: ActionType
  path?: string
}

type IconButtonProps = BaseProps & {
  isIcon: true
  icon?: IconType
  title?: string
}

type RegularButtonProps = BaseProps & {
  isIcon?: false
  icon?: never
}

export type ActionButtonProps = (IconButtonProps | RegularButtonProps) &
  ButtonProps
const getIcon = (typeAction: string) => {
  switch (typeAction) {
    case "delete":
      return <DeleteIcon />
    case "edit":
      return <EditIcon />
    case "details":
      return <OpenInNewIcon />
    case "view":
      return <VisibilityIcon />
    default:
      return null
  }
}

export const ActionButton = (props: ActionButtonProps) => {
  const { userInfo } = useUserStore()
  const location = useLocation()
  const permissions = usePermissions()
  const params = useParams()
  const pathName = location.pathname
  const { isIcon, children, typeAction, path, icon, ...rest } = props

  const currentPath = useMemo(
    () => path ?? generatePatternFromPath(pathName, params),
    [path, params, pathName]
  )

  const render = useMemo(() => {
    if (!userInfo?.role) return
    if (!permissions) return
    return canAccess({
      path: currentPath,
      permissions,
      typeAction,
    })
  }, [userInfo, currentPath, permissions, typeAction])

  if (!render) {
    return null
  }

  if (isIcon) {
    const iconComponent = getIcon(icon ?? typeAction)
    return <IconButton {...rest}>{children || iconComponent}</IconButton>
  }

  return <Button {...rest}>{children}</Button>
}
