import { PATH } from "@/shared/config/routes"
import {
  CampaignRoles,
  campaignRoleToCampaignRoleId,
  getInitialCampaignsRouteByRole,
  Roles,
  useCampaignStore,
  useUserStore,
} from "@/shared/model"
import { useGoToCoachDashboard } from "@/shared/model/useGoToCoachDashboard"
import CampaignIcon from "@mui/icons-material/Campaign"
import UserIcon from "@mui/icons-material/Person"
import DashboardIcon from "@mui/icons-material/Dashboard"
import HomeIcon from "@mui/icons-material/Home"
import Sports from "@mui/icons-material/Sports"
import { List, ListItemButton, ListItemIcon, ListItemText } from "@mui/material"
import { useMemo } from "react"
import { Link, useLocation } from "react-router-dom"
import { v4 as uuidv4 } from "uuid"

const AVAILABLE_COACH_DASHBOARD_ROLES_ID = [
  campaignRoleToCampaignRoleId.Coach,
  campaignRoleToCampaignRoleId[CampaignRoles.AssistantCoach],
  campaignRoleToCampaignRoleId[CampaignRoles.ProgramLeader],
  campaignRoleToCampaignRoleId[CampaignRoles.BoosterClub],
]

const AVAILABLE_ADMIN_DASHBOARD_ROLES_ID = [Roles.SuperAdmin, Roles.Admin]

const getNavigationItems = ({
  campaignUrl,
  campaignId,
  campaignRoleId,
  currentRole,
  actions,
}: {
  campaignId?: number
  campaignUrl?: string
  campaignRoleId?: number | null
  currentRole?: Roles | null
  currentCampaignRoleId?: number | null
  actions?: {
    onChangeDashboard?: () => void
    goToCoachDashboard?: () => void
  }
}) => {
  const isCoach =
    campaignRoleId &&
    AVAILABLE_COACH_DASHBOARD_ROLES_ID.includes(campaignRoleId)

  const isAdmin = currentRole
    ? AVAILABLE_ADMIN_DASHBOARD_ROLES_ID.includes(currentRole)
    : false

  const isAd = currentRole === Roles.AD

  const isParticipant = campaignRoleId === campaignRoleToCampaignRoleId.Participant

  return [
    {
      label: "Dashboard",
      icon: <HomeIcon />,
      visible: isAdmin,
      path: PATH.withAuth.home,
    },
    {
      label: "Dashboard",
      icon: <HomeIcon />,
      onClick: actions?.goToCoachDashboard,
      visible: isCoach,
      mathPath: PATH.withAuth.campaign.dashboard.url(campaignId || 0),
    },
    {
      label: "Dashboard",
      icon: <HomeIcon />,
      path: PATH.withAuth.athleticDirectorDashboard,
      visible: isAd,
      mathPath: PATH.withAuth.campaign.dashboard.url(campaignId || 0),
    },
    {
      label: "My Campaign",
      icon: <DashboardIcon />,
      path: PATH.withAuth.campaign.members.list.url(campaignId || 0),
      mathPath: PATH.withAuth.campaign.home.url(campaignId || 0),
      visible: isCoach,
    },
    {
      label: "Campaigns",
      icon: <CampaignIcon />,
      path: campaignUrl || PATH.withAuth.campaign.list,
      visible: isCoach || isAdmin || isParticipant,
    },
    {
      label: "Users",
      icon: <UserIcon />,
      path: PATH.withAuth.userManagement.home,
      visible: isAdmin,
    },
    {
      label: "Sports",
      icon: <Sports />,
      visible: isAdmin,
      path: PATH.withAuth.sports,
    },
  ]
}

export const NavigationList = () => {
  const { userInfo } = useUserStore()
  const { selectedCampaign } = useCampaignStore()
  const { goToCoachDashboard } = useGoToCoachDashboard()
  const location = useLocation()

  const navigationItems = useMemo(() => {
    const currentRole = userInfo?.role
    const campaignRoleId = selectedCampaign?.campaign_role_id
    const currentCampaignRoleId = selectedCampaign?.current_campaign_role_id
    const url = currentRole && getInitialCampaignsRouteByRole(currentRole)

    return getNavigationItems({
      campaignUrl: url,
      campaignRoleId,
      campaignId: selectedCampaign?.campaign_id,
      currentCampaignRoleId,
      currentRole,
      actions: {
        goToCoachDashboard,
      },
    })
  }, [
    goToCoachDashboard,
    selectedCampaign?.campaign_role_id,
    selectedCampaign?.campaign_id,
    selectedCampaign?.current_campaign_role_id,
    userInfo?.role,
  ])

  const formattedItems = useMemo(() => {
    const currentPath = location.pathname

    if (!navigationItems) {
      return []
    }

    const exactMatchItem = navigationItems.find(
      (item) => item.visible && currentPath === (item.mathPath || item.path)
    )

    const fallbackMatchItem = !exactMatchItem
      ? navigationItems.find(
          (item) =>
            item.visible &&
            currentPath.startsWith((item.mathPath || item.path) + "/")
        )
      : null

    const matchItem = exactMatchItem || fallbackMatchItem

    const selectedPath = matchItem?.mathPath || matchItem?.path

    return navigationItems
      .filter((item) => item.visible)
      .map((item) => {
        const targetPath = item.mathPath || item.path
        return {
          id: uuidv4(),
          selected: targetPath === selectedPath,
          ...item,
        }
      })
  }, [navigationItems, location])

  return (
    <List>
      {formattedItems.map((item) => (
        <ListItemButton
          key={item.id}
          component={"onClick" in item ? "button" : Link}
          to={"path" in item ? item.path : undefined}
          onClick={"onClick" in item ? item.onClick : undefined}
          className="hover:bg-gray-100 w-full"
          selected={!!item.selected}
        >
          <ListItemIcon>{item.icon}</ListItemIcon>
          <ListItemText primary={item.label} />
        </ListItemButton>
      ))}
    </List>
  )
}
