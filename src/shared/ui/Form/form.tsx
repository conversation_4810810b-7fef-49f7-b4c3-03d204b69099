import { CircularProgress } from "@mui/material"
import { FormEvent<PERSON><PERSON><PERSON>, PropsWithChildren } from "react"

interface FormProps {
  onSubmit: FormEventHandler<HTMLFormElement>
  isLoading?: boolean
}

function Form({
  onSubmit,
  children,
  isLoading = false,
}: PropsWithChildren<FormProps>) {
  return (
    <div className="w-full bg-white rounded p-6 mx-auto max-w-3xl">
      {isLoading && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}
      <form onSubmit={onSubmit} className="max-w-3xl mx-auto">
        {children}
      </form>
    </div>
  )
}

export default Form
