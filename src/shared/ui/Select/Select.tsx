import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select as MUISelect,
  SelectChangeEvent,
} from "@mui/material"
import React from "react"
import { Control, Controller } from "react-hook-form"

type Option = {
  label: string
  value: string | number
}

type SharedSelectProps = {
  name: string
  label: string
  options: Option[]
  control?: Control<any>
  defaultValue?: string | undefined
  onChange?: (event: SelectChangeEvent) => void
  fullWidth?: boolean
  disabled?: boolean
  error?: boolean
  helperText?: string
  value?: string | undefined
  size?: "small" | "medium"
  fieldVariant?: "outlined" | "standard"
  multiple?: boolean
}

export const Select: React.FC<SharedSelectProps> = ({
  name,
  label,
  options,
  control,
  defaultValue = "",
  onChange,
  fullWidth = true,
  disabled = false,
  error = false,
  helperText,
  value,
  size = "medium",
  fieldVariant = "outlined",
  multiple = false,
}) => {
  return control ? (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      render={({ field }) => (
        <FormControl
          fullWidth={fullWidth}
          disabled={disabled}
          error={error}
          id={name}
          variant={fieldVariant}
        >
          <InputLabel htmlFor={name} sx={{ backgroundColor: "white", px: 1 }}>
            {label}
          </InputLabel>
          <MUISelect
            {...field}
            size={size}
            id={name}
            onChange={(e) => {
              field.onChange(e)
              if (onChange) onChange(e)
            }}
            multiple={multiple}
          >
            {options.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </MUISelect>
          {helperText && <FormHelperText>{helperText}</FormHelperText>}
        </FormControl>
      )}
    />
  ) : (
    <FormControl
      fullWidth={fullWidth}
      disabled={disabled}
      error={error}
      id={name}
    >
      <InputLabel htmlFor={name} sx={{ backgroundColor: "white", px: 1 }}>
        {label}
      </InputLabel>
      <MUISelect
        size={size}
        name={name}
        value={value}
        onChange={onChange}
        multiple={multiple}
      >
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </MUISelect>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  )
}
