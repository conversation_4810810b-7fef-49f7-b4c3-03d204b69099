import { EmblaOptionsType } from "embla-carousel"
import useEmblaCarousel from "embla-carousel-react"
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from "./carouselArrowButtons"
import { DotButton, useDotButton } from "./carouselDotButton"
import cn from "classnames"

import "./style.css"

type PropType = {
  slides: {
    image: string
    title?: string
    id: number
    description?: string
  }[]
  options?: EmblaOptionsType
  fullWidth?: boolean
  className?: string
  renderSlide?: (slide: {
    image: string
    title?: string
    id: number
    description?: string
  }) => React.ReactNode
}

export const Carousel = (props: PropType) => {
  const { slides, options, fullWidth, className, renderSlide } = props
  const [emblaRef, emblaApi] = useEmblaCarousel(options)

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi)

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi)

  return (
    <section
      className={cn("embla", className, { "embla--full-width": fullWidth })}
    >
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">
          {slides.map((slide) => (
            <div className="embla__slide" key={slide.id}>
              {renderSlide ? (
                renderSlide(slide)
              ) : (
                <img
                  className="w-full h-[500px] object-scale-down"
                  src={slide.image}
                  alt={slide.title || ""}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="embla__controls">
        <div className="embla__buttons">
          <PrevButton onClick={onPrevButtonClick} disabled={prevBtnDisabled} />
          <NextButton onClick={onNextButtonClick} disabled={nextBtnDisabled} />
        </div>

        <div className="embla__dots">
          {scrollSnaps.map((_, index) => (
            <DotButton
              key={index}
              onClick={() => onDotButtonClick(index)}
              className={"embla__dot".concat(
                index === selectedIndex ? " embla__dot--selected" : ""
              )}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
