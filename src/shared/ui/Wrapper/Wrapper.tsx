import React from "react"
import { Typography } from "@mui/material"
import ArrowBackIcon from "@mui/icons-material/ArrowBack"

import { Button } from "@/shared/ui"

interface Props extends React.PropsWithChildren {
  backTitle?: string
  onBack?: () => void
  isForm?: boolean
}

export const Wrapper: React.FC<Props> = ({
  children,
  backTitle,
  onBack,
  isForm,
}) => {
  return (
    <div className="bg-gray-100">
      {!!onBack && (
        <div className="flex items-center justify-between p-4 bg-white">
          <div className="flex items-center gap-2 w-[350px]">
            <Button
              variant="text"
              onClick={onBack}
              className="min-w-0 !p-2 !w-[64px]"
            >
              <ArrowBackIcon />
            </Button>
            <Typography variant="h6" className="text-xl font-semibold">
              {backTitle}
            </Typography>
          </div>
        </div>
      )}

      <div
        className={`container mx-auto p-6 max-w-2xl mt-4 ${isForm ? "rounded bg-white" : ""}`}
      >
        {children}
      </div>
    </div>
  )
}
