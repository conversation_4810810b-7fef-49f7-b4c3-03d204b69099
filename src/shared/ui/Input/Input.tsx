import { Visibility, VisibilityOff } from "@mui/icons-material"
import { Icon<PERSON>utton, TextField } from "@mui/material"
import {
  TextFieldProps,
  TextFieldVariants,
} from "@mui/material/TextField/TextField"
import { useState } from "react"

interface InputProps {
  variant?: TextFieldVariants
  label?: string
  value?: string
  dataTestId?: string
  placeholder?: string
  className?: string
  handleInputChange?: (event: never) => void
  error?: boolean | undefined
  helperText?: string | undefined
  type?: React.HTMLInputTypeAttribute | undefined
  disabled?: boolean
  required?: boolean
  showHelperText?: boolean
  passwordInput?: boolean
}

export const Input = (props: InputProps & TextFieldProps) => {
  const [showPassword, setShowPassword] = useState(false)
  const {
    label,
    variant = "standard",
    value,
    placeholder,
    className,
    dataTestId,
    handleInputChange,
    helperText = " ",
    required,
    showHelperText = true,
    passwordInput = false,
    ...rest
  } = props

  const handleShowPassword = () => {
    setShowPassword(!showPassword)
  }

  return (
    <TextField
      label={label}
      data-testid={dataTestId}
      variant={variant}
      placeholder={placeholder}
      fullWidth
      className={className}
      value={value}
      onChange={handleInputChange}
      helperText={showHelperText ? helperText : undefined}
      {...rest}
      InputLabelProps={{
        required: required,
        sx: {
          "& .MuiInputLabel-asterisk": {
            color: "error.main",
          },
        },
      }}
      type={passwordInput ? (showPassword ? "text" : "password") : rest.type}
      InputProps={{
        endAdornment: passwordInput ? (
          <IconButton onClick={handleShowPassword}>
            {showPassword ? <VisibilityOff /> : <Visibility />}
          </IconButton>
        ) : undefined,
      }}
    />
  )
}
