import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers"
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns"
import React from "react"
import { Control, Controller, FieldError } from "react-hook-form"

interface DatePickerFieldProps {
  name: string
  label: string
  control: Control<any>
  error?: FieldError
  required?: boolean
  fieldVariant?: "outlined" | "standard"
  disabled?: boolean
}

export const DatePickerField: React.FC<DatePickerFieldProps> = ({
  name,
  label,
  control,
  error,
  required = false,
  fieldVariant = "outlined",
  disabled = false,
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <DatePicker
            label={label}
            value={field.value ?? null}
            onChange={field.onChange}
            disabled={disabled}
            slotProps={{
              textField: {
                variant: fieldVariant,
                fullWidth: true,
                required: required,
                error: !!error,
                helperText: error?.message,
                disabled: disabled,
              },
            }}
          />
        )}
      />
    </LocalizationProvider>
  )
}
