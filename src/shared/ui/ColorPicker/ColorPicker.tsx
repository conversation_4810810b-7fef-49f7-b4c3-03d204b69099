import { Box, FormControl, Popover, TextField } from "@mui/material"
import React, { useState } from "react"
import { HexColorPicker } from "react-colorful"
import { Control, Controller } from "react-hook-form"

const PRESET_COLORS = [
  "#000000",
  "#FFFFFF",
  "#FF0000",
  "#00FF00",
  "#0000FF",
  "#FFFF00",
  "#FF00FF",
  "#00FFFF",
  "#808080",
  "#800000",
  "#808000",
  "#008000",
  "#800080",
  "#008080",
  "#000080",
]

type ColorPickerProps = {
  name: string
  label: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control?: Control<any>
  defaultValue?: string
  onChange?: (color: string) => void
  error?: boolean
  disabled?: boolean
  required?: boolean
}

export const ColorPicker: React.FC<ColorPickerProps> = ({
  name,
  label,
  control,
  defaultValue = "#FFFFFF",
  onChange,
  error = false,
  disabled = false,
  required = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null)
  const open = Boolean(anchorEl)
  const [currentColor, setCurrentColor] = useState(defaultValue)
  const [isValidColor, setIsValidColor] = useState(true)

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!disabled) {
      setAnchorEl(event.currentTarget)
    }
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const colorPickerComponent = (handleChange: (color: string) => void) => (
    <FormControl fullWidth disabled={disabled} data-testid="color-form-control">
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <TextField
          id={name}
          fullWidth
          required={required}
          variant="outlined"
          value={currentColor}
          error={error || !isValidColor}
          helperText={!isValidColor || error ? "Invalid color" : ""}
          label={label}
          onChange={(e) => {
            const newColor = e.target.value
            if (/^#[0-9A-F]{3}([0-9A-F]{3})?$/i.test(newColor)) {
              setIsValidColor(true)
              setCurrentColor(newColor)
              handleChange(newColor)
            } else {
              setIsValidColor(false)
              setCurrentColor(newColor)
            }
          }}
          InputLabelProps={{
            required: required,
            sx: {
              "& .MuiInputLabel-asterisk": {
                color: "error.main",
              },
            },
          }}
          slotProps={{
            input: {
              startAdornment: (
                <Box
                  onClick={handleClick}
                  sx={{
                    width: 24,
                    height: 24,
                    borderRadius: 1,
                    border: "1px solid #ccc",
                    backgroundColor: currentColor,
                    cursor: disabled ? "default" : "pointer",
                    mr: 1,
                  }}
                />
              ),
            },
          }}
        />
      </Box>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <Box sx={{ p: 2 }}>
          <HexColorPicker
            color={currentColor}
            onChange={(newColor) => {
              handleChange(newColor)
              setCurrentColor(newColor)
            }}
          />
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(5, 1fr)",
              gap: 1,
              mt: 2,
            }}
          >
            {PRESET_COLORS.map((color) => (
              <Box
                key={color}
                onClick={() => {
                  setCurrentColor(color)
                  handleChange(color)
                  handleClose()
                }}
                sx={{
                  width: 24,
                  height: 24,
                  backgroundColor: color,
                  border: "1px solid #ccc",
                  cursor: "pointer",
                  "&:hover": {
                    transform: "scale(1.1)",
                  },
                }}
              />
            ))}
          </Box>
        </Box>
      </Popover>
    </FormControl>
  )

  if (control) {
    return (
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        data-testid="color-form-control"
        render={({ field }) =>
          colorPickerComponent((color) => {
            field.onChange(color)
            if (onChange) onChange(color)
          })
        }
      />
    )
  }

  return colorPickerComponent(onChange || (() => {}))
}
