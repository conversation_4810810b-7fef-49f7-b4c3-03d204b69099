import "@testing-library/jest-dom"
import { fireEvent, render, screen } from "@testing-library/react"
import { useForm } from "react-hook-form"
import { ColorPicker } from "./ColorPicker"

describe("ColorPicker", () => {
  it("renders color picker with label and default color", () => {
    render(<ColorPicker name="color" label="Choose Color" />)

    expect(screen.getByLabelText("Choose Color")).toBeInTheDocument()
    expect(screen.getByRole("textbox")).toHaveValue("#FFFFFF")
  })

  it("allows entering custom hex color", () => {
    const handleChange = jest.fn()
    render(
      <ColorPicker name="color" label="Choose Color" onChange={handleChange} />
    )

    const input = screen.getByRole("textbox")
    fireEvent.change(input, { target: { value: "#FF5733" } })

    expect(handleChange).toHaveBeenCalledWith("#FF5733")
  })

  it("validates hex color format", () => {
    const handleChange = jest.fn()
    render(
      <ColorPicker name="color" label="Choose Color" onChange={handleChange} />
    )

    const input = screen.getByRole("textbox")
    fireEvent.change(input, { target: { value: "invalid-color" } })

    expect(handleChange).not.toHaveBeenCalled()
  })

  it("shows error state and helper text", () => {
    render(<ColorPicker name="color" label="Choose Color" error={true} />)

    expect(screen.getByText("Invalid color")).toBeInTheDocument()
  })

  it("works with react-hook-form", () => {
    const TestComponent = () => {
      const { control } = useForm({
        defaultValues: {
          color: "#FF0000",
        },
      })
      return (
        <ColorPicker
          name="color"
          label="Choose Color"
          control={control}
          defaultValue="#FF0000"
        />
      )
    }

    render(<TestComponent />)
    const input = screen.getByRole("textbox", { name: "Choose Color" })
    expect(input).toHaveValue("#FF0000")
  })
})
