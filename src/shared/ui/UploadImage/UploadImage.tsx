import { config } from "@/shared/config"
import { Helpers } from "@/shared/model"
import { <PERSON><PERSON>, Dialog } from "@/shared/ui"
import ImageIcon from "@mui/icons-material/Image"
import { CircularProgress } from "@mui/material"
import cn from "classnames"
import React, { useRef, useState } from "react"
import ReactCrop, { Crop } from "react-image-crop"
import "react-image-crop/dist/ReactCrop.css"
import { toast } from "react-toastify"

interface Props {
  src?: string
  loading?: boolean
  size?: number
  onSubmit?: (image: Blob) => void
  isUploading?: boolean
  renderPhoto?: boolean
  className?: string
  textButton?: string
  fileSize?: number // in bytes
  fileTypes?: string[]
  disabled?: boolean
  circularCrop?: boolean
}
const defaultCrop = {
  unit: "%",
  x: 0,
  y: 0,
  width: 100,
  height: 100,
} as const

const circularCropProps = {
  unit: "px",
  x: 0,
  y: 0,
  width: 290,
  height: 290,
} as const

export const UploadImage: React.FC<Props> = ({
  src = "",
  size = 250,
  renderPhoto = true,
  loading = false,
  isUploading,
  textButton = "Upload image",
  className,
  fileSize = config.files.image.maxFileSize,
  fileTypes = config.files.image.acceptedTypes,
  disabled,
  onSubmit,
  circularCrop = false,
}) => {
  const imgRef = useRef<HTMLImageElement | null>(null)
  const [open, setOpen] = useState(false)
  const [imageSrc, setImageSrc] = useState<string | undefined>()
  const [crop, setCrop] = useState<Crop>(
    circularCrop ? circularCropProps : defaultCrop
  )
  const [completedCrop, setCompletedCrop] = useState<Crop | null>(null)

  const handleCloseAndReset = () => {
    setOpen(false)
    setCrop(circularCrop ? circularCropProps : defaultCrop)
    setCompletedCrop(null)
  }
  const uploadNewImage = () => {
    const fileInput = document.createElement("input")
    fileInput.type = "file"
    fileInput.accept = fileTypes.join(",")
    fileInput.click()

    fileInput.onchange = () => {
      const file = fileInput.files?.[0]
      if (file) {
        if (!fileTypes.includes(file.type)) {
          return toast.error(
            `Not allowed file type. Allowed: ${fileTypes.map((t) => t.split("/")[1]).join(", ")}`
          )
        }

        if (file.size > fileSize) {
          return toast.error(
            `File size must be less than ${Helpers.formatFileSize(fileSize)}`
          )
        }

        const reader = new FileReader()
        reader.onload = () => {
          const result = reader.result as string
          setImageSrc(result)
          setCompletedCrop(null)
          setCrop(circularCrop ? circularCropProps : defaultCrop)
          setOpen(true)
        }
        reader.readAsDataURL(file)
      }
    }
  }

  const getCroppedImg = async (): Promise<void> => {
    if (!imgRef.current) return

    const image = imgRef.current
    const crop = completedCrop

    const scaleX = image.naturalWidth / image.width
    const scaleY = image.naturalHeight / image.height

    const isUnchangedCrop =
      !crop ||
      (Math.round(crop.width) === Math.round(image.width) &&
        Math.round(crop.height) === Math.round(image.height) &&
        Math.round(crop.x) === 0 &&
        Math.round(crop.y) === 0)

    const canvas = document.createElement("canvas")
    canvas.width = isUnchangedCrop ? image.naturalWidth : crop.width * scaleX
    canvas.height = isUnchangedCrop ? image.naturalHeight : crop.height * scaleY
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    if (isUnchangedCrop) {
      ctx.drawImage(image, 0, 0, image.naturalWidth, image.naturalHeight)
    } else {
      ctx.drawImage(
        image,
        crop.x * scaleX,
        crop.y * scaleY,
        crop.width * scaleX,
        crop.height * scaleY,
        0,
        0,
        crop.width * scaleX,
        crop.height * scaleY
      )
    }

    canvas.toBlob((blob) => {
      if (blob && onSubmit) {
        onSubmit(blob)
        setOpen(false)
      }
    }, "image/jpeg")
  }

  const isEmpty = !src

  const canRenderAndEmpty = renderPhoto && isEmpty

  const styleBlock = {
    height: canRenderAndEmpty ? size : undefined,
    width: canRenderAndEmpty ? size : undefined,
    maxWidth: canRenderAndEmpty ? undefined : size,
    maxHeight: canRenderAndEmpty ? undefined : size,
  }

  const styleBlockWrapImage = {
    height: isEmpty ? size : undefined,
    width: isEmpty ? size : undefined,
    maxWidth: isEmpty ? undefined : size,
    maxHeight: isEmpty ? undefined : size,
  }

  const circularProps = {
    circularCrop,
    aspect: 1,
  }

  return (
    <>
      <div className="inline-block text-center" style={styleBlock}>
        {renderPhoto && (
          <div
            style={styleBlockWrapImage}
            className={cn(
              className,
              " width-full flex items-center justify-center text-center"
            )}
          >
            <div className="overflow-hidden rounded relative">
              {(!src && <ImageIcon sx={{ fontSize: size / 2 }} />) || (
                <img
                  src={src}
                  style={{
                    height: size,
                    width: size,
                    objectFit: circularCrop ? "cover" : "contain",
                    borderRadius: circularCrop ? "50%" : "0",
                  }}
                  alt="Preview"
                />
              )}
              {loading && (
                <div className="absolute top-0 left-0 w-full h-full">
                  <div className="w-full h-full bg-black opacity-30"></div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <CircularProgress />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {onSubmit && (
          <div className="mt-4">
            <Button
              loading={isUploading}
              disabled={loading || disabled}
              onClick={uploadNewImage}
            >
              {textButton}
            </Button>
          </div>
        )}
      </div>

      <Dialog
        open={open}
        onClose={handleCloseAndReset}
        title="Upload image"
        actions={
          <>
            <Button onClick={handleCloseAndReset} variant="outlined">
              Cancel
            </Button>
            <Button onClick={getCroppedImg} variant="contained">
              Save
            </Button>
          </>
        }
      >
        <div className="relative w-full text-center overflow-hidden p-1">
          {imageSrc && (
            <ReactCrop
              crop={crop}
              onChange={(c) => setCrop(c)}
              onComplete={(c) => setCompletedCrop(c)}
              minWidth={20}
              keepSelection={true}
              {...(circularCrop ? circularProps : {})}
            >
              <img
                ref={imgRef}
                src={imageSrc}
                alt="Source"
                style={{
                  maxWidth: "100%",
                  maxHeight: "calc(100vh - 250px)",
                  objectFit: "contain",
                }}
              />
            </ReactCrop>
          )}
        </div>
      </Dialog>
    </>
  )
}
