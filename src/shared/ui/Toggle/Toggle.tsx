import { FormControlLabel, Switch } from "@mui/material"
import { Control, Controller } from "react-hook-form"

type ToggleProps = {
  name: string
  label?: string
  control?: Control<any>
  defaultValue?: boolean
  onChange?: (checked: boolean) => void
  disabled?: boolean
}

export const Toggle = ({
  name,
  label,
  control,
  defaultValue = false,
  onChange,
  disabled = false,
  ...rest
}: ToggleProps) => {
  return control ? (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      render={({ field }) => (
        <FormControlLabel
          control={
            <Switch
              {...rest}
              {...field}
              checked={field.value}
              onChange={(e) => {
                field.onChange(e.target.checked)
                if (onChange) onChange(e.target.checked)
              }}
              disabled={disabled}
            />
          }
          label={label}
        />
      )}
    />
  ) : (
    <FormControlLabel
      control={
        <Switch
          {...rest}
          name={name}
          defaultChecked={defaultValue}
          onChange={(e) => onChange?.(e.target.checked)}
          disabled={disabled}
        />
      }
      label={label}
    />
  )
}
