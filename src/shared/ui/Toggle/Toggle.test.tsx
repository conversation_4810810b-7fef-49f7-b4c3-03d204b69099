import "@testing-library/jest-dom"
import { fireEvent, render, screen } from "@testing-library/react"
import { useForm } from "react-hook-form"
import { Toggle } from "./Toggle"

describe("Toggle", () => {
  it("renders toggle with label", () => {
    render(<Toggle name="test" label="Test Toggle" />)
    
    expect(screen.getByText("Test Toggle")).toBeInTheDocument()
    expect(screen.getByRole("checkbox")).toBeInTheDocument()
  })

  it("calls onChange when clicked", () => {
    const onChange = jest.fn()
    render(<Toggle name="test" label="Test Toggle" onChange={onChange} />)
    
    fireEvent.click(screen.getByRole("checkbox"))
    expect(onChange).toHaveBeenCalledWith(true)
  })

  it("can be disabled", () => {
    render(<Toggle name="test" label="Test Toggle" disabled />)
    
    expect(screen.getByRole("checkbox")).toBeDisabled()
  })

  it("works with react-hook-form", () => {
   
    const TestComponent = () => {
        const { control } = useForm({
            defaultValues: {
              test: false,
            },
          })
      return <Toggle name="test" label="Test Toggle" control={control} />
    }

    render(<TestComponent />)
    
    const toggle = screen.getByRole("checkbox")
    expect(toggle).not.toBeChecked()

    fireEvent.click(toggle)
    expect(toggle).toBeChecked()
  })
})
