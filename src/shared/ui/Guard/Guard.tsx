import {
  CampaignRoles,
  Roles,
  useCampaignStore,
  useUserStore,
} from "@/shared/model"
import { useMemo } from "react"

type TCampaignRoles = (typeof CampaignRoles)[keyof typeof CampaignRoles]
type Props =
  | {
      roles: Roles[]
      campaignRoles?: never
    }
  | {
      roles?: never
      campaignRoles: TCampaignRoles[]
    }
  | {
      roles: Roles[]
      campaignRoles: TCampaignRoles[]
    }

export const Guard: React.FC<React.PropsWithChildren<Props>> = ({
  children,
  roles,
  campaignRoles,
}) => {
  const { userInfo } = useUserStore()
  const { selectedCampaign } = useCampaignStore()

  const can = useMemo(() => {
    const currentRole = userInfo?.role
    const currentCampaignRole =
      selectedCampaign?.campaign_role_name as TCampaignRoles

    if (
      campaignRoles?.length &&
      currentCampaignRole &&
      campaignRoles.includes(currentCampaignRole)
    ) {
      return true
    }

    if (roles?.length && currentRole && roles.includes(currentRole)) {
      return true
    }

    return false
  }, [userInfo, roles, campaignRoles, selectedCampaign])

  if (!can) return null

  return children
}
