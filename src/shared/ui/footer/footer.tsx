import { Typography } from "@mui/material"
import { <PERSON> } from "react-router-dom"
import { LINKS } from "@/shared/config/links"

export const Footer = () => {
  return (
    <div className="flex flex-col md:flex-row gap-10 md:gap-40 w-full p-9 bg-black !text-white justify-center">
      <div className="flex items-center">
        <a href={LINKS.fundraising} target="_blank" rel="noopener noreferrer">
          <img
            src="/Aktivate_Horz_Logo_RGB_White.svg"
            alt="logo"
            width={200}
            className="!object-contain"
          />
        </a>
      </div>
      <div className="flex flex-col gap-1">
        <Typography className="!text-xl !font-bold !text-white">
          Resources
        </Typography>
        <div className="flex flex-row gap-10">
          <div className="flex flex-col gap-1">
            <Link className="!text-white" to={LINKS.contact}>
              Contact
            </Link>
            <Link
              className="!text-white"
              target="_blank"
              to={LINKS.fundraising}
            >
              Fundraising
            </Link>
          </div>
          <div className="flex flex-col gap-1">
            <Link
              className="!text-white"
              target="_blank"
              to={LINKS.termsOfService}
            >
              Terms
            </Link>
            <Link
              className="!text-white"
              target="_blank"
              to={LINKS.privacyPolicy}
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
