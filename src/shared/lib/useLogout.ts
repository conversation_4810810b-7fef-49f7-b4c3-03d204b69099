import { useNavigate } from "react-router-dom"

import { PATH } from "@/shared/config"
import { useCampaignStore, useUserStore } from "@/shared/model"
import { useQueryClient } from "@tanstack/react-query"

export const useLogout = () => {
  const { resetUserInfo } = useUserStore()
  const { resetSelectedCampaign } = useCampaignStore()
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  const handleLogout = () => {
    localStorage.removeItem("token")
    resetUserInfo()
    resetSelectedCampaign()
    queryClient.clear()
    navigate(PATH.withoutAuth.login)
  }

  return handleLogout
}
