import { toast } from "react-toastify"

export const copyToClipboard = async (text: string): Promise<void> => {
  if (navigator.clipboard) {
    try {
      await navigator.clipboard.writeText(text)
      toast.success("Link Copied")
    } catch (err) {
      toast.error("Failed to copy")
    }
  } else {
    // HACK: This is a hack to copy the text to the clipboard on http hosting
    // TODO: Remove this once we host on https
    const range = document.createRange()
    const selection = window.getSelection()

    const textNode = document.createTextNode(text)
    const div = document.createElement("div")
    div.appendChild(textNode)
    div.style.position = "fixed"
    div.style.opacity = "0"
    div.style.pointerEvents = "none"
    div.style.left = "0"
    div.style.top = "0"

    document.body.appendChild(div)

    try {
      range.selectNodeContents(textNode)
      selection?.removeAllRanges()
      selection?.addRange(range)

      const successful = document.execCommand("copy")
      if (successful) {
        toast.success("Link Copied")
      } else {
        toast.error("Failed to copy")
      }
    } catch (error) {
      console.error(error)
      toast.error("Failed to copy")
    } finally {
      selection?.removeAllRanges()
      document.body.removeChild(div)
    }
  }
}
