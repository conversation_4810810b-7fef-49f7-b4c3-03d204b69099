export const getAllErrorMessages = (error: any): string => {
  try {
    const errors = error.response?.data?.errors
    if (errors && typeof errors === "object") {
      const messages: string[] = []

      for (const field in errors) {
        if (Array.isArray(errors[field])) {
          messages.push(...errors[field])
        }
      }

      return messages.join("\n")
    }

    return "An unknown error occurred."
  } catch {
    return "An error occurred while processing the error."
  }
}
