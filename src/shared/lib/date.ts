const MILLISECONDS_IN_DAY = 1000 * 60 * 60 * 24

export const calculateDaysLeft = (endDate: Date | null): number => {
  if (!endDate) return 0

  return Math.max(
    0,
    Math.ceil((endDate.getTime() - new Date().getTime()) / MILLISECONDS_IN_DAY)
  )
}

/**
 * Converts a Date object to a string in YYYY-MM-DD format
 * @param date - Date object to convert
 * @returns string in YYYY-MM-DD format (e.g., "2024-01-15")
 */
export const getLocalDateString = (date: Date) =>
  `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`
