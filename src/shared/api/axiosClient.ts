import axios from "axios"
import { resetLocalStorageData } from "../model"
import { toast } from "react-toastify"

const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:5000"

const API_TIMEOUT = 30000

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: API_TIMEOUT,
})

apiClient.interceptors.request.use(
  (config) => {
    console.log(`Request: ${config.method?.toUpperCase()} ${config.url}`)

    if (config.data instanceof FormData) {
      config.headers["Content-Type"] = "multipart/form-data"
    }

    const token = localStorage.getItem("token")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => Promise.reject(error)
)

apiClient.interceptors.response.use(
  (response) => {
    if (typeof response.data === "string") {
      return {
        ...response,
        data: response.data,
      }
    }

    return {
      ...response,
      data: response.data?.data,
    }
  },
  (error) => {
    console.error("API Error:", error.response?.data || error.message)

    const token = localStorage.getItem("token")

    if (error.code === "ECONNABORTED" && error.message.includes("timeout")) {
      toast.error("Timeout exceeded. Poor internet connection.")
    }

    if (!error.response) {
      toast.error("No connection to the server. Check the internet.")
    }

    if (error.response?.status === 401 && token) {
      console.error("Unauthorized! Logging out...")
      resetLocalStorageData()
      window.location.href = "/login"
    }

    return Promise.reject(error)
  }
)
