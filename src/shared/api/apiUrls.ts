type Id = number | string

export const apiUrls = {
  donation: {
    donate: () => "/donate",
  },
  singUp: {
    createAccount: () => "/create-account",
  },
  login: {
    login: () => "/login",
  },
  campaign: {
    denomination: {
      list: (id: Id) => `/campaign/${id}/denomination`,
      updateAmounts: (id: Id) => `/campaign/${id}/denominations`,
    },
    donations: {
      list: (id: Id) => `/campaigns/${id}/donations`,
      filters: (id: Id) => `/campaigns/${id}/donations/filter`,
      export: (id: Id) => `/campaigns/${id}/donations/export`,
      inviteMessage: (id: Id) => `/campaign/${id}/donation-invite/messages`,
      details: (campaignId: Id, donationId: Id) =>
        `/campaign/${campaignId}/donations/${donationId}`,
    },
    tip: {
      list: (id: Id) => `/campaign/${id}/get-tip-amounts`,
      updateAmounts: (id: Id) => `/campaign/${id}/update-tip-amounts`,
    },
    messageTemplates: {
      list: (id: Id) => `/campaign/${id}/message-templates`,
      details: (campaignId: Id, templateId: Id) =>
        `/campaign/${campaignId}/message-templates/${templateId}`,
    },
    byGuid: (guid: string) => `/campaigns/guid/${guid}`,
    invite: {
      byPin: () => "/campaign-invites/accept-by-pin",
    },
    members: {
      list: (campaignId: Id) => `/campaign/${campaignId}/users`,
      details: (campaignId: Id, userId: Id) =>
        `/campaign/${campaignId}/users/${userId}`,
    },
    images: {
      list: (campaignId: Id) => `/campaign/${campaignId}/images`,
      details: (campaignId: Id, imageId: Id) =>
        `/campaign/${campaignId}/images/${imageId}`,
      reorder: (campaignId: Id) => `/campaign/${campaignId}/images/reorder`,
    },
    dripSchedules: {
      list: (campaignId: Id) => `/campaign/${campaignId}/drip-schedules`,
      update: (campaignId: Id) =>
        `/campaign/${campaignId}/drip-schedules/update`,
    },
    settings: {
      list: (campaignId: Id) => `/campaign/${campaignId}/settings`,
      details: (campaignId: Id, id: Id) =>
        `/campaign/${campaignId}/settings/${id}`,
    },
    users: {
      images: {
        list: (campaignId: Id, campaignUserId: Id) =>
          `/campaign/${campaignId}/users/${campaignUserId}/images`,
        details: (campaignId: Id, campaignUserId: Id, imageId: Id) =>
          `/campaign/${campaignId}/users/${campaignUserId}/images/${imageId}`,
      },
      donationMessage: {
        details: (campaignId: Id, campaignUserId: Id) =>
          `/campaign/${campaignId}/users/${campaignUserId}/message`,
      },
    },
    donationLeaderboard: {
      list: (campaignId: Id) => `/campaign/${campaignId}/donate`,
    },
    donationUser: {
      getDonationUser: (guid: string) => `/campaigns/guid/${guid}`,
    },
    checkRequest: {
      list: (campaignId: Id) => `/campaign/${campaignId}/check-request`,
      details: (campaignId: Id, id: Id) =>
        `/campaign/${campaignId}/check-request/${id}`,
      finalize: (campaignId: Id) =>
        `/campaign/${campaignId}/check-request/finalize`,
      clear: (campaignId: Id) =>
        `/campaign/${campaignId}/check-request/cleared`,
    },
    fee: {
      list: (campaignId: Id) => `/campaign/${campaignId}/get-fee-percentages`,
      updateMultiple: (campaignId: Id) =>
        `/campaign/${campaignId}/update-fee-multiple`,
      details: (campaignId: Id, feeId: Id) =>
        `/campaign/${campaignId}/get-fee-percentages/${feeId}`,
    },
    dashboard: {
      summary: (campaignId: Id) => `/campaign/${campaignId}/summary`,
      quickStats: () => `/dashboard/quick-stats`,
      donationsReport: () => `/dashboard/donations`,
      checkRequests: () => `/dashboard/check-requests`,
      checkRequestFilters: () => `/dashboard/check-requests/filters`,
      messageLogs: () => `/dashboard/message-logs`,
      messageLogFilters: () => `/dashboard/message-logs/filters`,
      checkRequestTotalAmount: () => `/dashboard/check-requests/total-amount`,
    },
    invites: {
      delete: (inviteId: Id) => `/campaigns/remove-invite/${inviteId}`,
      sendInvite: () => `/campaigns/send-invite`,
      validateToken: () => `/campaign/validate`,
    },
  },

  ad: {
    summary: () => "/athletic-director/summary",
    donations: {
      list: () => "/athletic-director/donations",
      filter: () => "/athletic-director/donations/filter",
      export: () => "/athletic-director/donations/export",
    },
    totals: () => "/athletic-director/totals",
  },

  salesPerson: {
    list: () => "/sales-persons",
    details: (id: Id) => `/sales-persons/${id}`,
  },
  sports: {
    list: () => "/sports",
    details: (id: Id) => `/sports/${id}`,
  },
}
