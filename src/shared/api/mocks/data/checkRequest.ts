export const mockCheckRequest = {
  "current_page": 1,
  "data": [{
  "id": 2,
  "campaign_id": 2,
  "school_org_name": "Polk County TEST TEST Athletics",
  "campaign_display_name": "Polk County Ice Hockey Team",
  "request_date": "2025-07-07 18:14:12",
  "processed_date": null,
  "cleared_date": null,
  "recipient_name": "<PERSON>",
  "check_address": "3182 Pearly Dr, Lakeland, Florida, 33812",
  "memo": "Reimbursement <PERSON><PERSON>",
  "org_contact_email": "<EMAIL>",
  "org_contact_phone": "8132700278",
  "request_status": "processed",
  "check_number": null,
  "check_amount": "9.60"
},
{
  "id": 3,
  "campaign_id": 2,
  "school_org_name": "Polka County TEST TEST Athletics",
  "campaign_display_name": "Polk County Ice Hockey Team",
  "request_date": "2025-07-07 18:14:12",
  "processed_date": null,
  "cleared_date": null,
  "recipient_name": "<PERSON>",
  "check_address": "3182 Pearly Dr, Lakeland, Florida, 33812",
  "memo": "Reimbursement <PERSON>",
  "org_contact_email": "<PERSON><PERSON><EMAIL>",
  "org_contact_phone": "8132700278",
  "request_status": "processed",
  "check_number": null,
  "check_amount": "9.60"
},
{
  "id": 4,
  "campaign_id": 2,
  "school_org_name": "Polkb County TEST TEST Athletics",
  "campaign_display_name": "Polk County Ice Hockey Team",
  "request_date": "2025-07-07 18:14:12",
  "processed_date": null,
  "cleared_date": null,
  "recipient_name": "Daniel Payne",
  "check_address": "3182 Pearly Dr, Lakeland, Florida, 33812",
  "memo": "Reimbursement D.Payne",
  "org_contact_email": "<EMAIL>",
  "org_contact_phone": "8132700278",
  "request_status": "processed",
  "check_number": null,
  "check_amount": "9.60"
}],
  "first_page_url": "http://************/api/dashboard/check-requests?page=1",
  "from": 1,
  "last_page": 1,
  "last_page_url": "http://************/api/dashboard/check-requests?page=1",
  "links": [
    {
      "url": null,
      "label": "&laquo; Previous",
      "active": false
    },
    {
      "url": "http://************/api/dashboard/check-requests?page=1",
      "label": "1",
      "active": true
    },
    {
      "url": null,
      "label": "Next &raquo;",
      "active": false
    }
  ],
  "next_page_url": null,
  "path": "http://************/api/dashboard/check-requests",
  "per_page": 10,
  "prev_page_url": null,
  "to": 1,
  "total": 1
}