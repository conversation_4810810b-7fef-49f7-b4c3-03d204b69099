import { http, HttpResponse } from "msw"
import { mockUserCampaign } from "../data/userCampaign"
import { mockCampaign } from "../data/campaign"
import { mockCampaignByGuid } from "../data/campaignByGuid"

const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:5000"

export const campaignHandlers = [
  http.get(`${API_BASE_URL}/user-campaigns?user_id=:userId`, () => {
    return HttpResponse.json({ data: mockUserCampaign })
  }),
  http.get(`${API_BASE_URL}/campaigns/:campaignId`, () => {
    return HttpResponse.json({ data: mockCampaign })
  }),
  http.get(`${API_BASE_URL}/campaigns/guid/:guid`, () => {
    return HttpResponse.json({ data: mockCampaignByGuid })
  }),
]
