import { useState } from "react"
import { useLocation } from "react-router"
import { IconButton } from "@/shared/ui"
import { Drawer } from "@/shared/ui/Drawer"
import { NavigationList } from "@/shared/ui/navigationList"
import { UserMenu } from "@/shared/ui/userMenu"
import LogoutIcon from "@mui/icons-material/Logout"
import LockIcon from "@mui/icons-material/Lock"
import MenuIcon from "@mui/icons-material/Menu"
import { AppBar, CircularProgress, Toolbar, Typography } from "@mui/material"
import { useLogout } from "../lib"
import { useUserStore } from "../model/user/UserContext"
import { PATH } from "../config"

type DashboardLayoutProps = {
  children: React.ReactNode
  title?: string
  isLoading?: boolean
}

export const DashboardLayout = ({
  children,
  title,
  isLoading = false,
}: DashboardLayoutProps) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const location = useLocation()

  const { userInfo } = useUserStore()

  const showLogout = !!userInfo?.id

  const handleLogout = useLogout()

  const userName = showLogout
    ? `${userInfo?.firstName?.charAt(0)}${userInfo?.lastName?.charAt(0)}`
    : ""

  const userMenu = {
    titlePrimary: `${userInfo?.firstName} ${userInfo?.lastName}`,
    titleSecondary: userInfo?.email,
    userName: userName,
    actions: [
      {
        label: "Change Password",
        icon: <LockIcon />,
        path: PATH.withAuth.changePassword,
        selected: location.pathname === PATH.withAuth.changePassword,
      },
    ],
    additionalActions: [
      {
        label: "Logout",
        icon: <LogoutIcon />,
        onClick: handleLogout,
      },
    ],
  }

  return (
    <div className="min-h-screen min-w-screen bg-gray-100">
      {isLoading && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}
      <AppBar position="static">
        <Toolbar className="flex justify-between">
          <div className="flex items-center">
            <div className="mr-8">
              <IconButton
                title="Menu"
                color="inherit"
                onClick={() => setIsDrawerOpen(true)}
                edge="end"
                className="!p-0"
              >
                <MenuIcon />
              </IconButton>
            </div>
            <Typography variant="h6" className="!text-white">
              {title}
            </Typography>
          </div>
          <div>
            <img
              src="/Aktivate_Logo_Horz_Color.png"
              alt="logo"
              width={200}
              className="!object-contain"
            />
          </div>
          {showLogout && <UserMenu {...userMenu} />}
        </Toolbar>
      </AppBar>

      <Drawer
        open={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        title="Menu"
      >
        <NavigationList />
      </Drawer>

      <main>{children}</main>
    </div>
  )
}
