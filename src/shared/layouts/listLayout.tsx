import { But<PERSON> } from "@/shared/ui/Button/Button"
import ArrowBackIcon from "@mui/icons-material/ArrowBack"
import { CircularProgress, Typography } from "@mui/material"
import { useNavigate } from "react-router-dom"

type Action = {
  label: string
  onClick: () => void
  variant?: "text" | "contained" | "outlined"
}

type ListLayoutProps = {
  children: React.ReactNode
  title: string
  actions?: Action[]
  isLoading?: boolean
  onBackClick?: () => void
  hideBackButton?: boolean
}

export const ListLayout = ({
  children,
  title,
  actions = [],
  isLoading = false,
  onBackClick,
  hideBackButton = false,
}: ListLayoutProps) => {
  const navigate = useNavigate()

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick()
    } else {
      navigate(-1)
    }
  }

  return (
    <div className="min-w-screen bg-gray-100">
      {isLoading && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}

      <div className="flex items-center justify-between p-4 bg-white border-b">
        <div className="flex items-center gap-2 w-[300px]">
          {!hideBackButton && (
            <Button
              variant="text"
              onClick={handleBackClick}
              className="min-w-0 !p-2 !w-[64px]"
            >
              <ArrowBackIcon />
            </Button>
          )}
          <Typography variant="h6" className="text-xl font-semibold">
            {title}
          </Typography>
        </div>

        <div className="flex gap-2 items-center">
          {actions.map((action, index) => (
            <Button
              key={`${index}-${action.label}`}
              onClick={action.onClick}
              variant={action.variant || "contained"}
              fullWidth={false}
            >
              {action.label}
            </Button>
          ))}
        </div>
      </div>

      <main className="container mx-auto p-6">{children}</main>
    </div>
  )
}
