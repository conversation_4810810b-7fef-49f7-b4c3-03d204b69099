import { Footer } from "@/shared/ui/footer/footer"
import { AppBar, CircularProgress, Toolbar } from "@mui/material"
import { PropsWithChildren } from "react"
import { LINKS } from "@/shared/config/links"

type AuthLayoutProps = PropsWithChildren<{
  title: string
  isLoading?: boolean
  disableHeader?: boolean
}>

export const AuthLayout = ({
  children,
  title,
  isLoading = false,
  disableHeader = false,
}: AuthLayoutProps) => {
  const headerHeight = 64
  const footerHeight = 156

  return (
    <div className="flex flex-col min-w-screen min-h-screen">
      {!disableHeader && (
        <AppBar position="static">
          <Toolbar className="flex justify-center">
            <div>
              <img
                src="/Aktivate_Logo_Horz_Color.png"
                alt="logo"
                width={200}
                className="!object-contain"
              />
            </div>
          </Toolbar>
        </AppBar>
      )}
      <div
        className="flex flex-col min-w-screen items-center justify-center bg-gray-100 relative"
        style={{
          minHeight: `calc(100vh - ${headerHeight + footerHeight}px)`,
        }}
      >
        {isLoading && (
          <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
            <CircularProgress size={48} />
          </div>
        )}

        <div className="bg-white p-6 rounded-lg shadow-lg min-w-96">
          <h2 className="text-2xl font-bold mb-4 text-center text-black">
            {title}
          </h2>
          {children}
        </div>
        <div className="relative bottom-0 left-0 right-0 text-center text-sm p-4 flex justify-center gap-4">
          <a href={LINKS.privacyPolicy} target="_blank">
            Privacy Policy
          </a>
          <a href={LINKS.termsOfService} target="_blank">
            Terms of Service
          </a>
        </div>
      </div>
      <Footer />
    </div>
  )
}
