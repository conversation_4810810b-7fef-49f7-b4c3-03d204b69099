import { createTheme } from "@mui/material/styles"

export const theme = createTheme({
  palette: {
    primary: {
      main: "#ec7b1a",
    },
    secondary: {
      main: "#FBBF24",
    },
  },
  typography: {
    fontFamily: ["Inter", "sans-serif"].join(","),
    allVariants: {
      color: "#1F2937",
    },
  },
  components: {
    MuiTypography: {
      styleOverrides: {
        root: {
          color: "#1F2937",
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          minWidth: "unset",
        },
      },
    },
  },
})
