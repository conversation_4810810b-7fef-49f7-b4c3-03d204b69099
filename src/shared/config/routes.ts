type Id = number | string

export const PATH = {
  withoutAuth: {
    welcome: "/welcome",
    login: "/login",
    signup: "/signup",
    forgotPassword: "/forgot-password",
    resetPassword: "/reset-password/:token",
    termsOfService: "/terms-of-service",
    donation: {
      path: "/campaign/donate/:guid",
      url: ({
        guid,
        queryParams,
      }: {
        guid: string
        queryParams?: URLSearchParams
      }) => {
        return `/campaign/donate/${guid}${
          queryParams ? `?${queryParams.toString()}` : ""
        }`
      },
    },
    notFound: "/404",
  },
  withAuth: {
    home: "/",
    forbidden: "/403",
    selectCampaign: "/campaign-selection",
    selectGroup: "/group-selection",
    changePassword: "/change-password",
    sports: "/sports",
    userManagement: {
      home: "/user-management",
      create: "/user-management/create",
      user: {
        create: "/user-management/users/create",
        update: {
          path: "/user-management/users/:id",
          url: (userId: Id) => `/user-management/users/${userId}`,
        },
        list: "/user-management/users",
      },
      admin: {
        create: "/user-management/admins/create",
        update: {
          path: "/user-management/admins/:id",
          url: (userId: Id) => `/user-management/admins/${userId}`,
        },
        list: "/user-management/admins",
      },
      ad: {
        create: "/user-management/athletic-directors/create",
        update: {
          path: "/user-management/athletic-directors/:id",
          url: (userId: Id) => `/user-management/athletic-directors/${userId}`,
        },
        list: "/user-management/athletic-directors",
      },
    },
    campaign: {
      list: "/campaigns",
      home: {
        path: "/campaigns/:campaignId",
        url: (campaignId: Id) => `/campaigns/${campaignId}`,
      },
      create: "/campaigns/create",
      dashboard: {
        path: "/campaigns/:campaignId/dashboard",
        url: (campaignId: Id) => `/campaigns/${campaignId}/dashboard`,
      },

      edit: {
        path: "/campaigns/:campaignId/edit",
        url: (campaignId: Id) => `/campaigns/${campaignId}/edit`,
      },

      details: {
        path: "/campaigns/:campaignId/details",
        url: (campaignId: Id) => `/campaigns/${campaignId}/details`,
      },

      groups: {
        path: "/campaigns/:campaignId/groups",
        url: (campaignId: Id) => `/campaigns/${campaignId}/groups`,
      },
      group: {
        path: "/campaigns/:campaignId/groups/:groupId",
        url: (campaignId: Id, groupId: Id) =>
          `/campaigns/${campaignId}/groups/${groupId}`,
      },

      members: {
        list: {
          path: "/campaigns/:campaignId/members",
          url: (campaignId: Id) => `/campaigns/${campaignId}/members`,
        },
        update: {
          path: "/campaigns/:campaignId/members/:userId",
          url: (campaignId: Id, userId: Id) =>
            `/campaigns/${campaignId}/members/${userId}`,
        },
      },
      messageLog: {
        list: {
          path: "/campaigns/:campaignId/message-log",
          url: (campaignId: Id) => `/campaigns/${campaignId}/message-log`,
        },
      },
      donationInvite: {
        list: {
          path: "/campaigns/:campaignId/donation-invites",
          url: (campaignId: Id) => `/campaigns/${campaignId}/donation-invites`,
        },
        home: "/campaigns/:campaignId/donation-invites/:id",
        create: {
          path: "/campaigns/:campaignId/donation-invites/create",
          url: (campaignId: Id) =>
            `/campaigns/${campaignId}/donation-invites/create`,
        },
        update: {
          path: "/campaigns/:campaignId/donation-invites/:id/update",
          url: (campaignId: Id, id: Id) =>
            `/campaigns/${campaignId}/donation-invites/${id}/update`,
        },
      },

      donations: {
        list: {
          path: "/campaigns/:campaignId/donations",
          url: (campaignId: Id) => `/campaigns/${campaignId}/donations`,
        },
      },
      smsTemplates: {
        list: {
          path: "/campaigns/:campaignId/sms-templates",
          url: (campaignId: Id) => `/campaigns/${campaignId}/sms-templates`,
        },
        details: {
          path: "/campaigns/:campaignId/sms-templates/:templateId",
          url: (campaignId: Id, id: Id) =>
            `/campaigns/${campaignId}/sms-templates/${id}`,
        },
      },

      emailTemplates: {
        list: {
          path: "/campaigns/:campaignId/email-templates",
          url: (campaignId: Id) => `/campaigns/${campaignId}/email-templates`,
        },
        details: {
          path: "/campaigns/:campaignId/email-templates/:templateId",
          url: (campaignId: Id, id: Id) =>
            `/campaigns/${campaignId}/email-templates/${id}`,
        },
      },

      dripSchedules: {
        path: "/campaigns/:campaignId/drip-schedules",
        url: (campaignId: Id) => `/campaigns/${campaignId}/drip-schedules`,
      },

      donationMessage: {
        path: "/campaigns/:campaignId/donation-message",
        url: (campaignId: Id) => `/campaigns/${campaignId}/donation-message`,
      },

      moments: {
        path: "/campaigns/:campaignId/moments",
        url: (campaignId: Id) => `/campaigns/${campaignId}/moments`,
      },

      donationsSettings: {
        path: "/campaigns/:campaignId/donation-settings",
        url: (campaignId: Id) => `/campaigns/${campaignId}/donation-settings`,
        topAmount: {
          path: "/campaigns/:campaignId/donation-settings/tip-amount",
          url: (campaignId: Id) =>
            `/campaigns/${campaignId}/donation-settings/tip-amount`,
        },
        donationAmounts: {
          path: "/campaigns/:campaignId/donation-settings/donation-amounts",
          url: (campaignId: Id) =>
            `/campaigns/${campaignId}/donation-settings/donation-amounts`,
        },
        fee: {
          path: "/campaigns/:campaignId/donation-settings/fee",
          url: (campaignId: Id) =>
            `/campaigns/${campaignId}/donation-settings/fee`,
        },
      },

      fund: {
        list: {
          path: "/campaigns/:campaignId/fund",
          url: (campaignId: Id) => `/campaigns/${campaignId}/fund`,
        },
        update: {
          path: "/campaigns/:campaignId/fund/edit",
          url: (campaignId: Id) => `/campaigns/${campaignId}/fund/edit`,
        },
      },
    },

    athleticDirectorDashboard: "/athletic-director/dashboard",
  },
} as const
