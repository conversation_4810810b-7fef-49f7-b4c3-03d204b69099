import * as yup from "yup"
export const phoneRegExp =
  /^\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/

export const passwordValidation = yup
  .string()
  .required("Password is required")
  .test("min-length", "Minimum 8 characters", (value) => value?.length >= 8)
  .test("max-length", "Maximum 20 characters", (value) => value?.length <= 20)
  .test("number", "Password must contain at least one number", (value) =>
    /\d/.test(value)
  )
  .test(
    "special-character",
    "Password must contain at least one special symbol",
    (value) => /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(value)
  )
  .test(
    "lowercase",
    "Password must contain at least one lowercase letter",
    (value) => /[a-z]/.test(value)
  )
  .test(
    "uppercase",
    "Password must contain at least one uppercase letter",
    (value) => /[A-Z]/.test(value)
  )

export const emptyFieldRegExp = /^(?!\s*$).+/

export const emptyFieldMessage = "Field cannot be empty or just spaces"

export const passwordHelperText =
  "8-20 characters with number, upper & lower case, and special character"

export const nameValidator = {
  name: (label: string) =>
    yup
      .string()
      .trim()
      .min(2, `${label} must be at least 2 characters`)
      .max(35, `${label} cannot exceed 35 characters`)
      .matches(emptyFieldRegExp, emptyFieldMessage),
}

export const emailOrPhoneValidator = {
  email: () =>
    yup
      .string()
      .email("Invalid email")
      .test(
        "email-or-phone-required",
        "Either email or phone number is required",
        function (value) {
          const { phone } = this.parent || {}
          return !!(value?.trim() || phone?.trim())
        }
      ),
  phone: () =>
    yup
      .string()
      .test(
        "phone-format",
        "Please enter a valid phone number",
        function (value) {
          if (!value || value.trim() === "") {
            return true // skip validation if field is empty
          }
          return phoneRegExp.test(value)
        }
      )
      .test(
        "email-or-phone-required",
        "Either email or phone number is required",
        function (value) {
          const { email } = this.parent || {}
          return !!(value?.trim() || email?.trim())
        }
      ),
}

export const richTextValidator = {
  content: (label: string) =>
    yup.string().test("is-not-empty", `${label} is required`, (value) => {
      if (!value) return false

      const cleaned = value
        .replace(/<[^>]*>/g, "")
        .replace(/&nbsp;/g, "")
        .replace(/\s+/g, "")
        .trim()

      return cleaned.length > 0
    }),
}
