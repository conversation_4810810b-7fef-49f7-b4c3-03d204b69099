import { Button, Input } from "@/shared/ui"
import { useState } from "react"
import { useBillingInfoForm } from "../model/billingInfo.stripe.form"
import { BillingInfoFormData } from "../model/billingInfo.stripe.schema"
import { CircularProgress } from "@mui/material"
import { CardElement, useElements, useStripe } from "@stripe/react-stripe-js"
import { StripeCardElement } from "@stripe/stripe-js"

interface BillingInfoProps {
  onSubmit: (data: {
    paymentMethodId: string
    billingInfo: BillingInfoFormData
    zip: string
    paymentType: string
    get_way: string
  }) => void
  totalDonationAmount: number
  navigationSlot: React.ReactNode
  billingInfo: BillingInfoFormData
  buttonClassName: string
}

export const StripeBillingInfo = ({
  onSubmit,
  totalDonationAmount,
  navigationSlot,
  billingInfo,
  buttonClassName,
}: BillingInfoProps) => {
  const stripe = useStripe()
  const elements = useElements()
  const { register, handleSubmit, errors } = useBillingInfoForm(billingInfo)
  const [isLoading, setIsLoading] = useState(false)

  const submitWrapper = async (data: BillingInfoFormData) => {
    if (!stripe || !elements) return
    setIsLoading(true)

    const { error, paymentMethod } = await stripe.createPaymentMethod({
      type: "card",
      card: elements.getElement(CardElement) as StripeCardElement,
    })

    if (error) {
      setIsLoading(false)
      console.error(error)
      return
    }

    onSubmit({
      paymentMethodId: paymentMethod.id,
      billingInfo: data,
      zip: paymentMethod.billing_details?.address?.postal_code || "",
      paymentType: paymentMethod.card?.wallet?.type ?? "credit_card",
      get_way: "stripe",
    })
  }

  return (
    <form onSubmit={handleSubmit(submitWrapper)}>
      {isLoading && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}

      {navigationSlot}

      <div className="mb-7">
        <CardElement />
      </div>

      <Input
        {...register("nameOnCard")}
        label="Name on Card"
        variant="outlined"
        error={!!errors.nameOnCard}
        helperText={errors.nameOnCard?.message}
        required
      />
      <Input
        {...register("address")}
        label="Address"
        variant="outlined"
        error={!!errors.address}
        helperText={errors.address?.message}
        required
      />
      <Input
        {...register("country")}
        label="Country"
        variant="outlined"
        error={!!errors.country}
        helperText={errors.country?.message}
        required
      />
      <Input
        {...register("state")}
        label="State"
        variant="outlined"
        error={!!errors.state}
        helperText={errors.state?.message}
        required
      />
      <Input
        {...register("city")}
        label="City"
        variant="outlined"
        error={!!errors.city}
        helperText={errors.city?.message}
        required
      />
      <Button
        size="large"
        type="submit"
        variant="contained"
        className={buttonClassName}
      >
        Confirm ${totalDonationAmount} donation
      </Button>
    </form>
  )
}
