import { PropsWithChildren, useEffect } from "react"

export const SquareWrapper = ({ children }: PropsWithChildren) => {
  useEffect(() => {
    const script = document.createElement("script")
    script.src = import.meta.env.VITE_SQUARE_CDN
    script.async = true
    script.onload = () => {
      console.log("Square SDK loaded")
    }
    document.body.appendChild(script)

    return () => {
      document.body.removeChild(script)
    }
  }, [])
  return <div>{children}</div>
}
