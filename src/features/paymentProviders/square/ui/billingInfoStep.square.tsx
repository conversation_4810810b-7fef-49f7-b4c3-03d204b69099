import { Button, Input } from "@/shared/ui"
import { useEffect, useRef, useState } from "react"
import { useBillingInfoForm } from "../model/billingInfo.square.form"
import { BillingInfoFormData } from "../model/billingInfo.square.schema"
import { CircularProgress } from "@mui/material"
import { formatCentsToDollarStr } from "@/shared/lib"

// Square API types
declare global {
  interface Window {
    Square: {
      payments: (
        applicationId: string,
        locationId: string
      ) => Promise<{
        card: () => Promise<{
          attach: (element: HTMLElement) => Promise<void>
          tokenize: () => Promise<{
            status: string
            token: string
            details: {
              billing: {
                postalCode: string
              }
            }
          }>
        }>
      }>
    }
  }
}

interface BillingInfoProps {
  onSubmit: (data: {
    paymentMethodId: string
    billingInfo: BillingInfoFormData
    zip: string
    paymentType: string
    get_way: string
  }) => void
  totalDonationAmountCents: number
  billingInfo: BillingInfoFormData
  buttonClassName: string
  navigationSlot: React.ReactNode
}

export const SquareBillingInfoStep = ({
  onSubmit,
  totalDonationAmountCents,
  navigationSlot,
  billingInfo,
  buttonClassName,
}: BillingInfoProps) => {
  const cardRef = useRef<HTMLDivElement>(null)
  const cardInstance = useRef<any>(null)
  const { register, handleSubmit, errors } = useBillingInfoForm(billingInfo)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    let isMounted = true
    let alreadyAttached = false

    const initSquare = async () => {
      if (!window.Square) {
        console.error("Square.js not loaded")
        return
      }

      if (cardInstance.current || alreadyAttached) {
        return
      }

      try {
        const payments = await window.Square.payments(
          import.meta.env.VITE_SQUARE_APPLICATION_ID,
          import.meta.env.VITE_SQUARE_LOCATION_ID
        )

        const card = await payments.card()
        if (isMounted && cardRef.current) {
          await card.attach(cardRef.current)
          cardInstance.current = card
          alreadyAttached = true
        }
      } catch (error) {
        console.error("Square init error:", error)
      }
    }

    initSquare()

    return () => {
      isMounted = false
    }
  }, [])

  const submitWrapper = async (data: BillingInfoFormData) => {
    if (!cardInstance.current) {
      console.error("Card not initialized")
      return
    }
    setIsLoading(true)

    const result = await cardInstance.current.tokenize()

    if (result.status === "OK") {
      onSubmit({
        paymentMethodId: result.token,
        billingInfo: data,
        zip: result.details.billing.postalCode,
        paymentType: "credit_card",
        get_way: "square",
      })
    } else {
      setIsLoading(false)
      console.error("Tokenization failed", result)
    }
  }

  return (
    <form onSubmit={handleSubmit(submitWrapper)}>
      {isLoading && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}

      {navigationSlot}

      <div className="mb-4" ref={cardRef} />

      <Input
        {...register("nameOnCard")}
        label="Name on Card"
        variant="outlined"
        error={!!errors.nameOnCard}
        helperText={errors.nameOnCard?.message}
        required
      />
      <Input
        {...register("address")}
        label="Address"
        variant="outlined"
        error={!!errors.address}
        helperText={errors.address?.message}
        required
      />
      <Input
        {...register("country")}
        label="Country"
        variant="outlined"
        error={!!errors.country}
        helperText={errors.country?.message}
        required
      />
      <Input
        {...register("state")}
        label="State"
        variant="outlined"
        error={!!errors.state}
        helperText={errors.state?.message}
        required
      />
      <Input
        {...register("city")}
        label="City"
        variant="outlined"
        error={!!errors.city}
        helperText={errors.city?.message}
        required
      />
      <Button
        size="large"
        type="submit"
        variant="contained"
        className={buttonClassName}
      >
        Confirm ${formatCentsToDollarStr(totalDonationAmountCents)} donation
      </Button>
    </form>
  )
}
