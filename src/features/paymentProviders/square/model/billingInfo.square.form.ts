import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  BillingInfoFormData,
  billingInfoSchema,
} from "./billingInfo.square.schema"

export const useBillingInfoForm = (billingInfo: BillingInfoFormData) => {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<BillingInfoFormData>({
    resolver: yupResolver(billingInfoSchema),
    defaultValues: billingInfo,
  })

  return {
    register,
    handleSubmit,
    control,
    errors,
  }
}
