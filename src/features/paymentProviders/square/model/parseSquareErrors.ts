export const parseSquareErrors = (rawErrors: string[]): string[] => {
  const readableErrors: string[] = []

  for (const rawError of rawErrors) {
    const bodyMatch = rawError.match(/Body:\s*(\{.*\})/s)
    if (!bodyMatch) {
      readableErrors.push("An unknown error occurred.")
      continue
    }

    try {
      const bodyJson = JSON.parse(bodyMatch[1])
      if (bodyJson.errors && Array.isArray(bodyJson.errors)) {
        for (const err of bodyJson.errors) {
          const code = err.code?.replace(/_/g, " ").toLowerCase()
          const detail = err.detail || ""
          let message = ""

          switch (err.code) {
            case "CVV_FAILURE":
              message = "The CVV code is incorrect."
              break
            case "ADDRESS_VERIFICATION_FAILURE":
              message =
                "Address verification failed. Please check the billing ZIP/postal code."
              break
            case "INVALID_EXPIRATION":
              message = "The card expiration date is invalid."
              break
            case "CARD_DECLINED":
              message =
                "The card was declined. Please use a different payment method."
              break
            default:
              message = detail || `Error: ${code}`
          }

          readableErrors.push(message)
        }
      } else {
        readableErrors.push("Unknown error format in Square response.")
      }
    } catch (e) {
      readableErrors.push("Failed to parse error body.")
    }
  }

  return readableErrors
}
