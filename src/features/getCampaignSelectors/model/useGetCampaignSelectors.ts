import { useGetCauses } from "@/entities/cause"
import { useGetGenders } from "@/entities/gender"
import { useGetLevels } from "@/entities/level"
import { useGetSalesPersons } from "@/entities/salesPerson"
import { useGetSports } from "@/entities/sport"
import { US_STATES } from "@/shared/config"

export const useGetCampaignData = () => {
  const { data: levels, isLoading: isLoadingLevels } = useGetLevels()
  const { data: genders, isLoading: isLoadingGenders } = useGetGenders()
  const { data: sports, isLoading: isLoadingSports } = useGetSports()
  const { data: causes, isLoading: isLoadingCauses } = useGetCauses()

  const { data: salesPersons, isLoading: isLoadingSalesPersons } =
    useGetSalesPersons()

  return {
    levelsOptions:
      levels?.map((level) => ({ value: level.id, label: level.name })) ?? [],
    gendersOptions:
      genders?.map((gender) => ({ value: gender.id, label: gender.name })) ??
      [],
    sportsOptions:
      sports?.map((sport) => ({ value: sport.sport_id, label: sport.name })) ??
      [],
    causesOptions:
      causes?.map((cause) => ({ value: cause.cause_id, label: cause.name })) ??
      [],
    salesPersonsOptions:
      salesPersons?.map((salesPerson) => ({
        value: salesPerson.user_id,
        label: `${salesPerson.first_name} ${salesPerson.last_name}`,
      })) ?? [],
    stateOptions: US_STATES.map((state) => ({
      value: state,
      label: state,
    })),
    isLoadingLevels,
    isLoadingGenders,
    isLoadingSports,
    isLoadingCauses,
    isLoadingSalesPersons,
    isLoading:
      isLoadingLevels ||
      isLoadingGenders ||
      isLoadingSports ||
      isLoadingCauses ||
      isLoadingSalesPersons,
  }
}
