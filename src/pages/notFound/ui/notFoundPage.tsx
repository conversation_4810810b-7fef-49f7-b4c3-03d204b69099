import { useNavigate } from "react-router-dom"

import { PATH } from "@/shared/config"
import { <PERSON><PERSON> } from "@/shared/ui"
import { usePageTitle } from "@/shared/lib"

const NotFoundPage = () => {
  usePageTitle("Not found")
  const navigate = useNavigate()

  const handleGoHome = () => {
    navigate(PATH.withAuth.home)
  }

  return (
    <div className="flex flex-col items-center justify-center h-screen min-w-screen bg-white text-black">
      <h1 className="text-4xl font-bold mb-4 text-center">
        404 - Page Not Found
      </h1>
      <p className="text-lg mb-4 text-center">
        Sorry, the page you are looking for does not exist.
      </p>
      <Button onClick={handleGoHome} fullWidth={false} size="medium">
        Go to home
      </Button>
    </div>
  )
}

export default NotFoundPage
