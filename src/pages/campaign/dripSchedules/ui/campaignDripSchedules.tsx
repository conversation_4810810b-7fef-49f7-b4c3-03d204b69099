import {
  CampaignDripSchedulesDto,
  useGetCampaignDelayStartDripSchedules,
  useGetCampaignDripSchedules,
  useUpdateCampaignDelayStartDripSchedule,
  useUpdateCampaignDripSchedule,
} from "@/entities/campaignDripSchedule"
import { usePageTitle } from "@/shared/lib"
import { useGetCampaignId, usePagination } from "@/shared/model"
import { Button, Dialog, Table } from "@/shared/ui"
import { DatePickerField } from "@/shared/ui/DatePickerField"
import { CircularProgress } from "@mui/material"
import { format } from "date-fns"
import { useState } from "react"
import { toast } from "react-toastify"
import { useCampaignDripSchedulesDelayStartForm } from "../model/campaignDripSchedules.form"
import { useCampaignDripSchedulesTableData } from "../model/campaignDripSchedules.hooks"
import { CampaignDripSchedulesFormData } from "../model/campaignDripSchedules.schema"
import { DripSchedulesForm } from "./campaignDripSchedulesForm"

const CampaignDripSchedules = () => {
  usePageTitle("Drip Schedules")
  const [selectedItem, setSelectedItem] =
    useState<CampaignDripSchedulesDto | null>(null)
  const campaignId = useGetCampaignId(true)
  const handleEdit = (item: CampaignDripSchedulesDto) => {
    setSelectedItem(item)
  }

  const { pageSize, page, onPaginationModelChange } = usePagination()

  const { data, isLoading } = useGetCampaignDripSchedules(campaignId, {
    page,
    per_page: pageSize,
  })

  const { columns, rows } = useCampaignDripSchedulesTableData({
    onEdit: handleEdit,
    data: data?.data,
  })

  const { data: delayStartData, isLoading: isLoadingDelayStart } =
    useGetCampaignDelayStartDripSchedules(campaignId)

  const { mutate: updateDelayStart, isPending: isUpdatingDelayStart } =
    useUpdateCampaignDelayStartDripSchedule({
      onSuccess: () => {
        toast.success("Delay start updated successfully")
      },
      onError: (error) => {
        toast.error(error.message)
      },
    })

  const {
    control,
    handleSubmit: onSubmitDelayStart,
    formState: { errors, isDirty },
  } = useCampaignDripSchedulesDelayStartForm(
    delayStartData?.donation_invite_delay_start_at
  )

  const { mutate: updateDripSchedule, isPending: isUpdating } =
    useUpdateCampaignDripSchedule({
      onSuccess: () => {
        toast.success("Drip schedule updated successfully")
      },
      onError: (error) => {
        toast.error(error.message)
      },
    })

  const handleCloseEditDialog = () => {
    setSelectedItem(null)
  }

  const handleSubmit = (data: CampaignDripSchedulesFormData) => {
    if (!selectedItem) return
    updateDripSchedule({
      campaignId: Number(campaignId),
      data: {
        id: selectedItem!.id,
        interval: data.days_after_event,
      },
    })
    handleCloseEditDialog()
  }

  const handleSubmitDelayStart = (data: {
    donation_invite_delay_start_at: Date
  }) => {
    if (!delayStartData?.id) return
    updateDelayStart({
      campaignId,
      data: {
        id: delayStartData?.id,
        donation_invite_delay_start_at: format(
          data.donation_invite_delay_start_at,
          "yyyy-MM-dd"
        ),
      },
    })
  }

  const currentPage = data?.meta?.current_page ? data.meta.current_page - 1 : 0

  return (
    <>
      <form
        onSubmit={onSubmitDelayStart(handleSubmitDelayStart)}
        className="mb-4"
      >
        <div className="flex flex-wrap items-center gap-4">
          {isLoadingDelayStart ? (
            <CircularProgress />
          ) : (
            <>
              <div>
                <DatePickerField
                  label="Delay Start"
                  name="donation_invite_delay_start_at"
                  control={control}
                  error={errors.donation_invite_delay_start_at}
                  required
                />
              </div>

              <div>
                <Button
                  disabled={!isDirty}
                  loading={isUpdatingDelayStart}
                  type="submit"
                  variant="contained"
                >
                  Update
                </Button>
              </div>
            </>
          )}
        </div>
      </form>
      <Table
        columns={columns}
        loading={isLoading}
        rows={rows}
        rowCount={data?.meta?.total || 0}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
        boxClassName="h-full"
      />
      <Dialog
        open={!!selectedItem}
        onClose={handleCloseEditDialog}
        title={`Edit ${selectedItem?.template?.template_name || "Drip Schedule"}`}
      >
        <DripSchedulesForm
          isLoading={isUpdating}
          onClose={handleCloseEditDialog}
          onSubmit={handleSubmit}
          initialData={selectedItem?.days_after_event}
        />
      </Dialog>
    </>
  )
}

export default CampaignDripSchedules
