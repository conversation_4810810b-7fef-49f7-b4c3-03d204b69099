import { Button } from "@/shared/ui/Button"
import { Input } from "@/shared/ui/Input"
import { useCampaignDripSchedulesForm } from "../model/campaignDripSchedules.form"
import { CampaignDripSchedulesFormData } from "../model/campaignDripSchedules.schema"

interface DripSchedulesFormProps {
  onClose: () => void
  onSubmit: (data: CampaignDripSchedulesFormData) => void
  initialData?: number
  isLoading?: boolean
  isEdit?: boolean
}

export const DripSchedulesForm = ({
  onClose,
  onSubmit,
  initialData,
  isLoading = false,
}: DripSchedulesFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useCampaignDripSchedulesForm(initialData)

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col gap-4 pt-2"
    >
      <Input
        {...register("days_after_event")}
        label="Days after event"
        error={!!errors.days_after_event}
        type="number"
        helperText={errors.days_after_event?.message}
        autoFocus
        disabled={isLoading}
      />
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outlined" onClick={onClose} disabled={isLoading}>
          Cancel
        </Button>
        <Button type="submit" variant="contained" isLoading={isLoading}>
          Update
        </Button>
      </div>
    </form>
  )
}
