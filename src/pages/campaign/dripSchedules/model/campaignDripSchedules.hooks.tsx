import { useMemo } from "react"
import { getColumns } from "./campaignDripSchedules.columns"
import { CampaignDripSchedulesDto } from "@/entities/campaignDripSchedule"

interface UseCampaignDripSchedulesTableDataProps {
  onEdit: (id: CampaignDripSchedulesDto) => void
  data?: CampaignDripSchedulesDto[]
}

export const useCampaignDripSchedulesTableData = ({
  onEdit,
  data,
}: UseCampaignDripSchedulesTableDataProps) => {
  const columns = useMemo(() => getColumns({ onEdit }), [onEdit])

  const rows =
    useMemo(() => data?.map((row) => ({ ...row, id: row.id })), [data]) ?? []

  return {
    columns,
    rows,
  }
}
