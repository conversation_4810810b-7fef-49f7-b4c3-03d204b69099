import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import {
  CampaignDripSchedulesDelayStartFormData,
  campaignDripSchedulesDelayStartSchema,
  CampaignDripSchedulesFormData,
  campaignDripSchedulesSchema,
} from "./campaignDripSchedules.schema"
import { useEffect, useMemo } from "react"

export const useCampaignDripSchedulesForm = (initialValue?: number) => {
  const methods = useForm<CampaignDripSchedulesFormData>({
    resolver: yupResolver(campaignDripSchedulesSchema),
    defaultValues: {
      days_after_event: initialValue,
    },
  })

  return methods
}

export const useCampaignDripSchedulesDelayStartForm = (
  initialValue?: string | null
) => {
  const formattedInitialValue = useMemo(() => {
    if (!initialValue) {
      return undefined
    }

    return new Date(initialValue as string | number) as unknown as Date
  }, [initialValue])

  const methods = useForm<CampaignDripSchedulesDelayStartFormData>({
    resolver: yupResolver(campaignDripSchedulesDelayStartSchema),
    defaultValues: {
      donation_invite_delay_start_at: formattedInitialValue,
    },
  })

  useEffect(() => {
    if (formattedInitialValue !== undefined) {
      methods.reset({
        donation_invite_delay_start_at: formattedInitialValue,
      })
    }
  }, [formattedInitialValue, methods])
  return methods
}
