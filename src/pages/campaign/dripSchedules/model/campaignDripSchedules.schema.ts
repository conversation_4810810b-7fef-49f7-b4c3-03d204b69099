import { format } from "date-fns"
import * as yup from "yup"

export const campaignDripSchedulesSchema = yup.object().shape({
  days_after_event: yup
    .number()
    .label("Days after event")
    .transform((value, originalValue) => (originalValue === "" ? null : value))
    .nullable()
    .positive()
    .integer()
    .min(0, "Must be 0 or greater")
    .required(),
})

export type CampaignDripSchedulesFormData = yup.InferType<
  typeof campaignDripSchedulesSchema
>

export const campaignDripSchedulesDelayStartSchema = yup.object().shape({
  donation_invite_delay_start_at: yup
    .date()
    .label("Delay start at date")
    .min(format(new Date(), "yyyy-MM-dd"), "Date must be in the future")
    .nullable()
    .required(),
})

export type CampaignDripSchedulesDelayStartFormData = yup.InferType<
  typeof campaignDripSchedulesDelayStartSchema
>
