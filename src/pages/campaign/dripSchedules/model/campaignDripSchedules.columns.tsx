import { CampaignDripSchedulesDto } from "@/entities/campaignDripSchedule"
import { ActionButton } from "@/shared/ui/ActionButton"
import { GridRenderCellParams } from "@mui/x-data-grid"

interface GetColumnsProps {
  onEdit: (id: CampaignDripSchedulesDto) => void
}

export const getColumns = ({ onEdit }: GetColumnsProps) => {
  return [
    {
      field: "campaign_id",
      headerName: "Campaign ID",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "message_template.template_type",
      headerName: "Template Type",
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        const value = params?.row?.template?.channel || ""
        return <div className="capitalize">{value}</div>
      },
    },
    {
      field: "message_template.template_name",
      headerName: "Name",
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        const value = params?.row?.template?.template_name || ""
        return <div title={value}>{value}</div>
      },
    },
    {
      field: "days_after_event",
      headerName: "Interval After Event",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <>
          <ActionButton
            isIcon
            typeAction="edit"
            onClick={() => onEdit(params.row)}
            title="Edit"
          />
        </>
      ),
    },
  ]
}
