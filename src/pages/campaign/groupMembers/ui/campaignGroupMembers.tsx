import { Typography } from "@mui/material"
import { useQueryClient } from "@tanstack/react-query"
import { useMemo, useState } from "react"
import { useParams } from "react-router-dom"
import { toast } from "react-toastify"

import { useGetCampaignGroupById } from "@/entities/campaignGroup"
import {
  useAddCampaignGroupMember,
  useCampaignGroupMembersTableData,
  useDeleteCampaignGroupMember,
  useGetCampaignGroupMembers,
} from "@/entities/campaignGroupMember"
import { useGetCampaignMembers } from "@/entities/campaignMember"

import { ConfirmDialog, Table } from "@/shared/ui"
import { ActionButton } from "@/shared/ui/ActionButton"
import { AddMemberDialog } from "./AddMemberDialog"

const CampaignGroup = () => {
  const { groupId, campaignId } = useParams<{
    groupId: string
    campaignId: string
  }>()
  const queryClient = useQueryClient()

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [selectedMemberId, setSelectedMemberId] = useState<number | null>(null)

  const { data: campaignGroup, isLoading: isLoadingCampaignGroup } =
    useGetCampaignGroupById(Number(groupId))

  const { data: members, isLoading: membersIsLoading } = useGetCampaignMembers(
    Number(campaignId),
    {
      page: 1,
      per_page: 1000,
    }
  )

  const { data: groupMembers, isLoading: isLoadingGroupMembers } =
    useGetCampaignGroupMembers(Number(groupId))

  const { mutate: deleteMember, isPending: isDeletingMember } =
    useDeleteCampaignGroupMember({
      onSuccess: () => {
        toast.success("Member deleted successfully")
        setDeleteDialogOpen(false)
        queryClient.invalidateQueries({
          queryKey: ["useGetCampaignGroupMembers", groupId],
        })
      },
      onError: () => {
        toast.error("Failed to delete member")
        setDeleteDialogOpen(false)
      },
    })

  const { mutate: addMember, isPending: isAddingMember } =
    useAddCampaignGroupMember({
      onSuccess: () => {
        toast.success("Member added successfully")
        queryClient.invalidateQueries({
          queryKey: ["useGetCampaignGroupMembers", groupId],
        })
      },
      onError: (error) => {
        if (error.response?.data?.message) {
          toast.error(error.response.data.message)
        } else {
          toast.error(error.message)
        }
      },
    })

  const preparedMembers = useMemo(() => {
    return groupMembers?.map((member) => {
      return {
        id: member.campaign_user_id,
        groupId: member.campaign_group_id,
        name: `${member.campaign_user.first_name} ${member.campaign_user.last_name}`,
      }
    })
  }, [groupMembers])

  const handleDeleteClick = (id: number) => {
    setDeleteDialogOpen(true)
    setSelectedMemberId(id)
  }

  const handleDelete = () => {
    if (selectedMemberId) {
      deleteMember({ groupId: Number(groupId), memberId: selectedMemberId })

      setSelectedMemberId(null)
      setDeleteDialogOpen(false)
    }
  }

  const handleAddClick = () => {
    setAddDialogOpen(true)
  }

  const handleAddMemberSubmit = (userIds: number[]) => {
    addMember({
      groupId: Number(groupId),
      memberIds: userIds,
    })
    setAddDialogOpen(false)
  }

  const { columns, rows } = useCampaignGroupMembersTableData({
    onDelete: handleDeleteClick,
    campaignGroupMembers: preparedMembers ?? [],
  })

  const selectedMemberName = useMemo(() => {
    return (
      preparedMembers?.find((member) => member.id === selectedMemberId)?.name ||
      ""
    )
  }, [preparedMembers, selectedMemberId])

  const isLoading =
    isLoadingGroupMembers ||
    isDeletingMember ||
    isAddingMember ||
    isLoadingCampaignGroup

  return (
    <>
      <div className="flex justify-between mb-4">
        <Typography className="!text-xl font-semibold">
          {campaignGroup?.group_name} members
        </Typography>
        <ActionButton
          typeAction="create"
          onClick={handleAddClick}
          variant={"contained"}
          fullWidth={false}
        >
          Add Member
        </ActionButton>
      </div>
      <Table
        columns={columns}
        rows={rows}
        rowCount={rows?.length || 0}
        loading={isLoading}
        page={0}
        pageSize={25}
      />
      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Member"
        content={`Are you sure you want to delete ${selectedMemberName} from the group?`}
      />
      <AddMemberDialog
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
        onSubmit={handleAddMemberSubmit}
        members={members}
        isLoading={membersIsLoading}
      />
    </>
  )
}

export default CampaignGroup
