import { useEffect, useState } from "react"

import { CampaignMemberDto } from "@/entities/campaignMember"
import { Button } from "@/shared/ui"
import { Autocomplete, Option } from "@/shared/ui/autocomplete"
import { Dialog, DialogContent, DialogTitle } from "@mui/material"

interface AddMemberDialogProps {
  open: boolean
  onClose: () => void
  onSubmit: (userIds: number[]) => void
  members?: CampaignMemberDto
  isLoading?: boolean
}

export const AddMemberDialog = ({
  open,
  onClose,
  onSubmit,
  members,
  isLoading,
}: AddMemberDialogProps) => {
  const [selectedUserIds, setSelectedUserIds] = useState<Option[] | null>([])

  useEffect(() => {
    if (open) {
      setSelectedUserIds([])
    }
  }, [open])

  const userOptions =
    members?.data
      ?.filter((user) => user.campaign_role_id === 4 && user.user_id)
      .map((user) => ({
        value: user.campaign_user_id,
        label: `${user.first_name} ${user.last_name}`,
      }))
      .sort((a, b) => a.label.localeCompare(b.label)) || []

  const handleSubmit = () => {
    if (selectedUserIds && Array.isArray(selectedUserIds)) {
      onSubmit(selectedUserIds.map((option) => Number(option.value)))
    }
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add Member</DialogTitle>
      <DialogContent>
        <div className="mt-4">
          <Autocomplete
            name="userId"
            label="Select Users"
            options={userOptions}
            disabled={isLoading}
            multiple
            value={selectedUserIds as any}
            onChange={(value) => {
              setSelectedUserIds(value as Option[])
            }}
          />
        </div>
      </DialogContent>
      <DialogContent className="flex gap-2">
        <Button onClick={onClose} variant="outlined" className="flex-1">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={
            selectedUserIds === null ||
            selectedUserIds.length === 0 ||
            isLoading
          }
          className="flex-1"
        >
          Add
        </Button>
      </DialogContent>
    </Dialog>
  )
}
