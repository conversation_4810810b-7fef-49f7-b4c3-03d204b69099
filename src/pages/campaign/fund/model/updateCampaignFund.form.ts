import { UpdateCampaignFundApiDto } from "@/entities/campaignFund"
import { yupResolver } from "@hookform/resolvers/yup"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import {
  UpdateCampaignFundFormData,
  updateCampaignFundSchema,
} from "./updateCampaignFund.schema"

export const useUpdateCampaignFundForm = (
  initialValues?: UpdateCampaignFundApiDto
) => {
  const form = useForm<UpdateCampaignFundFormData>({
    resolver: yupResolver(updateCampaignFundSchema),
    defaultValues: {},
  })

  useEffect(() => {
    if (!form.formState.isDirty && initialValues) {
      const {
        recipient_name,
        mailing_address_1,
        mailing_address_2,
        city,
        state,
        zip,
        memo,
        notes,
        org_contact_email,
        org_contact_phone,
        check_number,
      } = initialValues

      form.reset({
        mailing_address_1: mailing_address_1 ?? "",
        mailing_address_2: mailing_address_2,
        org_contact_email: org_contact_email ?? "",
        org_contact_phone: org_contact_phone ?? "",
        recipient_name: recipient_name ?? "",
        city: city ?? "",
        state: state ?? "",
        zip: zip ?? "",
        memo: memo ?? "",
        notes: notes ?? "",
        check_number: check_number ?? "",
      })
    }
  }, [form, initialValues])

  return form
}
