import {
  emptyFieldMessage,
  emptyFieldRegExp,
  phoneRegExp,
} from "@/shared/config"
import * as yup from "yup"

export const updateCampaignFundSchema = yup.object().shape({
  recipient_name: yup
    .string()
    .required("Recipient name is required")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  city: yup
    .string()
    .required("City is required")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  mailing_address_1: yup
    .string()
    .required("Address 1 is required")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  mailing_address_2: yup.string().nullable(),
  state: yup
    .string()
    .required("State is required")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  zip: yup.string().required("ZIP code is required"),
  org_contact_email: yup
    .string()
    .nullable()
    .email("Please enter a valid email")
    .required("Email is required"),
  org_contact_phone: yup
    .string()
    .nullable()
    .matches(phoneRegExp, {
      message: "Please enter a valid US phone number",
      excludeEmptyString: true,
    })
    .required("Phone number is required"),
  memo: yup
    .string()
    .required("Memo is required")
    .max(255, "Memo must be less than 255 characters")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  notes: yup.string().nullable(),
  check_number: yup.string().nullable(),
})

export type UpdateCampaignFundFormData = yup.InferType<
  typeof updateCampaignFundSchema
>
