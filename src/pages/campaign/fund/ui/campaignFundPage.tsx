import { useGetCampaignById } from "@/entities/campaign"
import {
  CampaignFundStatus,
  useClearRequestCampaignFund,
  useCreateRequestCampaignFund,
  useFinalizeRequestCampaignFund,
  useGetCampaignFund,
  useUpdateCampaignFund,
} from "@/entities/campaignFund"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { formatCurrency } from "@/shared/lib/formatCurrency"
import { useGetCampaignId } from "@/shared/model"
import { asyncConfirm, Button, Toggle } from "@/shared/ui"
import HelpIcon from "@mui/icons-material/Help"
import { CircularProgress, Tooltip, Typography } from "@mui/material"
import React from "react"
import { useNavigate } from "react-router"
import { toast } from "react-toastify"

const CampaignFundPage: React.FC = () => {
  usePageTitle("Fund management")
  const navigate = useNavigate()
  const campaignId = useGetCampaignId(true)
  const { data: campaign } = useGetCampaignById(campaignId)
  const { data, isLoading } = useGetCampaignFund(campaignId)

  const {
    total_funds = 0,
    total_fees = 0,
    total_raised = 0,
    request_status = "",
    recipient_name = "",
    mailing_address_1 = "",
    mailing_address_2 = "",
    city = "",
    state = "",
    zip = "",
    org_contact_email = "",
    org_contact_phone = "",
    memo = "",
    notes = "",
    notification_enabled = false,
    check_number = "",
  } = data || {}

  const { mutate: updateNotification } = useUpdateCampaignFund({
    onSuccess: () => {
      toast.success("Notification updated")
    },
    onError: () => {
      toast.error("Failed to update notification")
    },
  })

  const { mutate: createRequest } = useCreateRequestCampaignFund({
    onSuccess: () => {
      toast.success("Request has been processed")
    },
    onError: () => {
      toast.error("Failed to send request")
    },
  })

  const { mutate: finalizeRequest } = useFinalizeRequestCampaignFund({
    onSuccess: () => {
      toast.success("Request has been processed")
    },
    onError: () => {
      toast.error("Failed to process request")
    },
  })

  const onSendRequest = async () => {
    const agree = await asyncConfirm({
      title: "Request Funds",
      message:
        "Are you sure you want to request funds? This action cannot be undone.",
    })

    if (agree) {
      createRequest(campaignId)
    }
  }

  const onChangeNotification = (value: boolean) => {
    if (!data || !data?.id) {
      toast.error("Failed to update notification")
      return
    }

    const { id, ...rest } = data

    updateNotification({
      campaignId: campaignId,
      id,
      data: {
        ...rest,
        notification_enabled: value,
      },
    })
  }

  const onEdit = () => {
    navigate(PATH.withAuth.campaign.fund.update.url(campaignId))
  }

  const additionalAddress = `${city ? city + "," : ""} ${state ? state + "," : ""} ${zip || ""}`

  const isStatusPending = request_status === CampaignFundStatus.pending

  const onFinalize = async () => {
    const agree = await asyncConfirm({
      title: "Process",
      message:
        "Are you sure you want to change the status from “Requested” to “Processed”?",
    })

    if (!data || !data?.id) {
      toast.error("Failed to update notification")
      return
    }

    if (agree) {
      finalizeRequest(campaignId)
    }
  }

  const clearButtonCondition =
    request_status === CampaignFundStatus.processed ||
    request_status === CampaignFundStatus.cleared
  const clearButtonText =
    request_status === CampaignFundStatus.processed ? "Clear" : "Process"

  const { mutate: clearRequest } = useClearRequestCampaignFund({
    onSuccess: () => {
      toast.success(
        request_status === CampaignFundStatus.processed
          ? "Request has been cleared"
          : "Request has been processed"
      )
    },
    onError: () => {
      toast.error(
        request_status === CampaignFundStatus.processed
          ? "Failed to clear request"
          : "Failed to process request"
      )
    },
  })

  const onClear = async () => {
    const agree = await asyncConfirm({
      title: clearButtonText,
      message: `Are you sure you want to ${clearButtonText.toLowerCase()} the request?`,
    })

    if (!data || !data?.id) {
      toast.error("Failed to update notification")
      return
    }

    if (agree) {
      clearRequest(campaignId)
    }
  }

  return (
    <div className="w-full py-6">
      <div className="flex flex-wrap items-center mb-6 gap-4">
        <h2 className="text-2xl sm:text-3xl font-semibold mr-10">
          Fund Management
        </h2>
        <div className="flex flex-wrap items-center ml-auto gap-4">
          {request_status === CampaignFundStatus.requested && (
            <Button
              variant="contained"
              onClick={onFinalize}
              className="px-2 py-1 max-w-fit ml-4"
            >
              Process
            </Button>
          )}
          {clearButtonCondition && (
            <Button
              variant="contained"
              onClick={onClear}
              className="px-2 py-1 max-w-fit ml-4"
            >
              {clearButtonText}
            </Button>
          )}
          <Button
            variant="outlined"
            onClick={onEdit}
            className="px-2 py-1 max-w-fit ml-4"
          >
            Edit
          </Button>
          <Button
            variant="contained"
            disabled={
              campaign?.status?.toLowerCase() !== "suspended" ||
              !isStatusPending
            }
            onClick={onSendRequest}
            className="px-2 py-1 max-w-fit ml-4"
          >
            Request Funds
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg mx-auto ">
        {isLoading && (
          <div className="p-6 text-center">
            <CircularProgress />
          </div>
        )}
        {!isLoading && (
          <div className="flex flex-col md:flex-row gap-4">
            <div className="p-6 flex-grow w-2/3">
              <div className="space-y-4 text-base sm:text-lg">
                <div className="flex items-center">
                  <strong>Total Available Funds:</strong>
                  <span className="ml-2">
                    ${formatCurrency(total_funds, { maximumFractionDigits: 2 })}
                  </span>
                  <Tooltip
                    title={
                      <>
                        <Typography className="!text-white">
                          Total Raised: $
                          {formatCurrency(total_raised, {
                            maximumFractionDigits: 2,
                          })}
                        </Typography>
                        <Typography className="!text-white">
                          Total Fees: $
                          {formatCurrency(total_fees, {
                            maximumFractionDigits: 2,
                          })}
                        </Typography>
                        <Typography className="!text-white">
                          Total Profit: $
                          {formatCurrency(total_funds, {
                            maximumFractionDigits: 2,
                          })}
                        </Typography>
                      </>
                    }
                  >
                    <HelpIcon fontSize="small" className="ml-2" />
                  </Tooltip>
                </div>

                <div>
                  <strong>Check details</strong>
                </div>
                <div className="pl-2">
                  <span>Check Name:</span> <span>{recipient_name ?? "-"}</span>
                </div>
                <div className="pl-2">
                  <span>Mailing Address:</span>{" "}
                  <div className="pl-4">
                    {mailing_address_1 ? mailing_address_1 + "," : ""}{" "}
                    {additionalAddress ?? "-"}
                  </div>
                  {mailing_address_2 && (
                    <div className="pl-4">
                      {`${mailing_address_2}, ${additionalAddress}`}
                    </div>
                  )}
                </div>
                <div className="pl-2">
                  <span>Customer Contact</span>
                </div>
                <div className="pl-4">
                  <span>Email:</span> <span>{org_contact_email || "-"}</span>
                </div>
                <div className="pl-4">
                  <span>Phone:</span> <span>{org_contact_phone || "-"}</span>
                </div>
                <div>
                  <strong>Memo:</strong> <span>{memo ?? "-"}</span>
                </div>
              </div>
            </div>
            <div className="w-1/3">
              <div className="p-6 ">
                <div className="space-y-4 text-base sm:text-lg">
                  <div>
                    <strong>Current Status:</strong>{" "}
                    <span className="capitalize">{request_status}</span>
                  </div>
                  <div>
                    <strong>Check Number:</strong>{" "}
                    <span className="pl-2">{check_number ?? "-"}</span>
                  </div>
                  <div>
                    <strong>Check Request Notification:</strong>{" "}
                    <span className="pl-2">
                      <Toggle
                        name="notification"
                        defaultValue={notification_enabled}
                        onChange={onChangeNotification}
                      />
                    </span>
                  </div>
                  <div>
                    <strong>Notes (Finance Team):</strong>
                    <p>{notes ?? "-"}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CampaignFundPage
