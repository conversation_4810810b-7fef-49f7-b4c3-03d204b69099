import {
  UpdateCampaignFundApiDto,
  useGetCampaignFund,
  useUpdateCampaignFund,
} from "@/entities/campaignFund"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { useGetCampaignId } from "@/shared/model"
import { Button, Input, Wrapper } from "@/shared/ui"
import { CircularProgress } from "@mui/material"
import React, { useCallback } from "react"
import { useNavigate } from "react-router-dom"
import { toast } from "react-toastify"
import { useUpdateCampaignFundForm } from "../model/updateCampaignFund.form"

const CampaignFundEditPage: React.FC = () => {
  usePageTitle("Edit fund management")
  const navigate = useNavigate()
  const campaignId = useGetCampaignId(true)
  const { data, isLoading } = useGetCampaignFund(campaignId)

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
  } = useUpdateCampaignFundForm(data)

  const onBackClick = useCallback(() => {
    navigate(PATH.withAuth.campaign.fund.list.url(campaignId))
  }, [navigate, campaignId])

  const { mutate: update, isPending: isUpdatePending } = useUpdateCampaignFund({
    onSuccess: () => {
      toast.success("Fund updated successfully")
      onBackClick()
    },
    onError: () => {
      toast.error("Failed to update fund")
    },
  })

  const onSubmit = (
    values: Omit<UpdateCampaignFundApiDto, "notification_enabled">
  ) => {
    if (!data || !data?.id) {
      toast.error("Failed to update notification")
      return
    }

    const { id, ...rest } = data

    update({
      campaignId: campaignId,
      id: id,
      data: {
        ...values,
        notification_enabled: rest.notification_enabled,
        check_number: values.check_number ?? null,
      },
    })
  }

  return (
    <div className="w-full py-6">
      <div className="flex flex-wrap items-center mb-6 gap-4">
        <h2 className="text-2xl sm:text-3xl font-semibold mr-10">
          Campaign Fund Management Edit
        </h2>
      </div>

      <Wrapper isForm onBack={onBackClick} backTitle="Campaign Fund Management">
        {isLoading && (
          <div className="text-center">
            <CircularProgress />
          </div>
        )}
        {!isLoading && (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Input
              label="Check Name"
              {...register("recipient_name")}
              error={!!errors.recipient_name}
              helperText={errors.recipient_name?.message}
              variant="outlined"
              required
            />
            <Input
              label="Check Number"
              {...register("check_number")}
              error={!!errors.check_number}
              helperText={errors.check_number?.message}
              variant="outlined"
            />
            <Input
              label="Address 1"
              {...register("mailing_address_1")}
              error={!!errors.mailing_address_1}
              helperText={errors.mailing_address_1?.message}
              variant="outlined"
              required
            />
            <Input
              label="Address 2"
              {...register("mailing_address_2")}
              error={!!errors.mailing_address_2}
              helperText={errors.mailing_address_2?.message}
              variant="outlined"
            />
            <Input
              label="City"
              {...register("city")}
              error={!!errors.city}
              helperText={errors.city?.message}
              variant="outlined"
              required
            />
            <Input
              label="State"
              {...register("state")}
              error={!!errors.state}
              helperText={errors.state?.message}
              variant="outlined"
              required
            />
            <Input
              label="ZIP Code"
              type="number"
              {...register("zip")}
              error={!!errors.zip}
              helperText={errors.zip?.message}
              variant="outlined"
              required
            />
            <Input
              label="Email"
              {...register("org_contact_email")}
              error={!!errors.org_contact_email}
              helperText={errors.org_contact_email?.message}
              variant="outlined"
              required
            />
            <Input
              label="Phone"
              type="tel"
              {...register("org_contact_phone")}
              error={!!errors.org_contact_phone}
              helperText={errors.org_contact_phone?.message}
              variant="outlined"
              required
            />
            <Input
              label="Memo"
              multiline
              rows={3}
              {...register("memo")}
              error={!!errors.memo}
              helperText={errors.memo?.message}
              variant="outlined"
              required
            />
            <Input
              label="Notes (Finance Team)"
              multiline
              rows={3}
              {...register("notes")}
              error={!!errors.notes}
              helperText={errors.notes?.message}
              variant="outlined"
            />
            <Button
              type="submit"
              disabled={!isDirty}
              variant="contained"
              loading={isUpdatePending}
              fullWidth
            >
              Update Campaign
            </Button>
          </form>
        )}
      </Wrapper>
    </div>
  )
}

export default CampaignFundEditPage
