import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import {
  CreateCampaignFormData,
  updateDonationAmountsSchema,
} from "./updateDonationAmountsSchema.schema"

export const useUseUpdateDonationAmountsForm = (data?: number[]) => {
  const form = useForm<CreateCampaignFormData>({
    resolver: yupResolver(updateDonationAmountsSchema),
    defaultValues: {
      amounts: data ?? [],
    },
  })

  useEffect(() => {
    if (data && !form.formState.isDirty) {
      form.reset({
        amounts: data,
      })
    }
  }, [data, form])

  return form
}
