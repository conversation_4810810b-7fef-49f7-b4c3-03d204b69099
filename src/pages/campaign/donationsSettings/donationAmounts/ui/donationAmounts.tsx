import { useParams } from "react-router-dom"

import { usePageTitle } from "@/shared/lib"
import { useUseUpdateDonationAmountsForm } from "../model/updateDonationAmounts.form"
import { Button } from "@/shared/ui"
import { DonationDenominations } from "./DonationDenominations"
import {
  CampaignDenominationDto,
  useGetCampaignDenominations,
  useUpdateCampaignDenominations,
} from "@/entities/campaignDenomination"
import { useMemo } from "react"
import { CircularProgress } from "@mui/material"
import { toast } from "react-toastify"

const getAmounts = (data?: CampaignDenominationDto[]) =>
  data?.map((item) => Number(item.amount))

const DonationAmounts = () => {
  usePageTitle("Donation amounts")
  const { campaignId } = useParams<{ campaignId: string }>()

  const { data, isLoading } = useGetCampaignDenominations(Number(campaignId))

  const initialData = useMemo(() => getAmounts(data), [data])

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
  } = useUseUpdateDonationAmountsForm(initialData)

  const { mutate: updateDonationAmounts, isPending } =
    useUpdateCampaignDenominations({
      onSuccess: (_, val) => {
        toast.success("Donation amounts updated successfully")
        reset({ amounts: val.data.amounts })
      },
      onError: (error) => {
        if (error.response?.data?.message) {
          toast.error(error.response.data.message)
        } else {
          toast.error(error.message)
        }
      },
    })

  const error = errors.amounts

  const onSubmit = (data: { amounts: number[] }) => {
    console.log("data", data)
    updateDonationAmounts({
      campaignId: Number(campaignId),
      data,
    })
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="container mx-auto p-6 max-w-2xl bg-white"
    >
      {(isLoading && (
        <div className="flex items-center justify-center ">
          <CircularProgress size={48} />
        </div>
      )) || (
        <div className="space-y-4">
          <DonationDenominations
            control={control}
            error={!!error}
            helperText={error?.message}
            name="amounts"
          />
          <div className="mt-4">
            <Button
              type="submit"
              variant="contained"
              loading={isPending}
              fullWidth
              disabled={isPending}
            >
              Update
            </Button>
          </div>
        </div>
      )}
    </form>
  )
}

export default DonationAmounts
