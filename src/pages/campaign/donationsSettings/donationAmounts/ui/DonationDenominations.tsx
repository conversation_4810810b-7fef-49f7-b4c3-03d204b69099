import {
  Chip,
  TextField,
  Box,
  FormControl,
  FormLabel,
  FormHelperText,
} from "@mui/material"
import { Control, useController, useForm } from "react-hook-form"
import { useState } from "react"
import { Toggle } from "@/shared/ui/Toggle/Toggle"

interface DonationDenominationsProps {
  control: Control<any>
  name: string
  error?: boolean
  helperText?: string
  label?: string
}

export const DonationDenominations = ({
  control,
  name,
  error,
  helperText,
  label = "Donation Denominations",
}: DonationDenominationsProps) => {
  const [newAmount, setNewAmount] = useState("")
  const [inputError, setInputError] = useState("")
  const [sortAscending, setSortAscending] = useState(true)

  const { field } = useController({
    name,
    control,
    defaultValue: [],
  })

  const {} = useForm()

  const sortDenominations = (denominations: number[]) => {
    return denominations.toSorted((a, b) => (sortAscending ? a - b : b - a))
  }

  const handleDelete = (denominationToDelete: number) => {
    const updatedDenominations = (field.value as number[]).filter(
      (amount) => amount !== denominationToDelete
    )
    field.onChange(updatedDenominations)
  }

  const validateAmount = (amount: string): boolean => {
    const numericAmount = Number(amount)

    if (isNaN(numericAmount)) {
      setInputError("Please enter a valid number")
      return false
    }

    if (!Number.isInteger(numericAmount)) {
      setInputError("Please enter a whole number")
      return false
    }

    if (numericAmount <= 0) {
      setInputError("Amount must be greater than 0")
      return false
    }

    if ((field.value as number[]).includes(numericAmount)) {
      setInputError("This amount already exists")
      return false
    }

    if (inputError) {
      setInputError("")
    }

    return true
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setNewAmount(value)
    if (value) {
      validateAmount(value)
    } else {
      setInputError("")
    }
  }

  const onAddValue = () => {
    if (validateAmount(newAmount)) {
      const numericAmount = Number(newAmount)
      const updatedDenominations = sortDenominations([
        ...(field.value as number[]),
        numericAmount,
      ])
      field.onChange(updatedDenominations)
      setNewAmount("")
      setInputError("")
    }
  }

  const handleSortToggle = (ascending: boolean) => {
    setSortAscending(ascending)
    // Re-sort existing denominations with new order
    const sortedDenominations = sortDenominations(field.value as number[])
    field.onChange(sortedDenominations)
  }

  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault()
      onAddValue()
    }
  }

  const handleBlur = () => {
    if (newAmount) {
      onAddValue()
    }
  }

  const currentError = !!inputError || !!error
  const currentHelperText = inputError || helperText

  return (
    <FormControl error={currentError} fullWidth>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 1,
        }}
      >
        <FormLabel required>{label}</FormLabel>
        <Toggle
          name="sortOrder"
          label={sortAscending ? "Ascending" : "Descending"}
          defaultValue={sortAscending}
          onChange={handleSortToggle}
        />
      </Box>
      <Box
        sx={{
          border: "1px solid",
          borderColor: currentError ? "error.main" : "rgba(0, 0, 0, 0.23)",
          borderRadius: 1,
          p: 2,
        }}
      >
        <Box className="flex flex-wrap gap-2 mb-4">
          {sortDenominations(field.value as number[]).map((amount) => (
            <Chip
              key={amount}
              label={`$${amount}`}
              onDelete={() => handleDelete(amount)}
              color="primary"
              variant="outlined"
            />
          ))}
        </Box>
        <TextField
          value={newAmount}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          onBlur={handleBlur}
          placeholder="Add amount and press Enter"
          error={currentError}
          fullWidth
          variant="outlined"
          InputProps={{
            startAdornment: (
              <Box component="span" sx={{ mr: 1 }}>
                $
              </Box>
            ),
          }}
        />
      </Box>
      {currentHelperText && currentError && (
        <FormHelperText error>{currentHelperText}</FormHelperText>
      )}
    </FormControl>
  )
}
