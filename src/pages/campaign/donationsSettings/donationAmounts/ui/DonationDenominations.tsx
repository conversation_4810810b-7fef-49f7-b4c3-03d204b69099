import {
  Chip,
  TextField,
  Box,
  FormControl,
  FormLabel,
  FormHelperText,
} from "@mui/material"
import { Control, useController } from "react-hook-form"
import { useState } from "react"

interface DonationDenominationsProps {
  control: Control<any>
  name: string
  error?: boolean
  helperText?: string
  label?: string
}

export const DonationDenominations = ({
  control,
  name,
  error,
  helperText,
  label = "Donation Denominations",
}: DonationDenominationsProps) => {
  const [newAmount, setNewAmount] = useState("")
  const [inputError, setInputError] = useState("")

  const { field } = useController({
    name,
    control,
    defaultValue: [],
  })

  const handleDelete = (denominationToDelete: number) => {
    const updatedDenominations = (field.value as number[]).filter(
      (amount) => amount !== denominationToDelete
    )
    field.onChange(updatedDenominations)
  }

  const validateAmount = (amount: string): boolean => {
    const numericAmount = Number(amount)

    if (isNaN(numericAmount)) {
      setInputError("Please enter a valid number")
      return false
    }

    if (!Number.isInteger(numericAmount)) {
      setInputError("Please enter a whole number")
      return false
    }

    if (numericAmount <= 0) {
      setInputError("Amount must be greater than 0")
      return false
    }

    if ((field.value as number[]).includes(numericAmount)) {
      setInputError("This amount already exists")
      return false
    }

    if (inputError) {
      setInputError("")
    }

    return true
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setNewAmount(value)
    if (value) {
      validateAmount(value)
    } else {
      setInputError("")
    }
  }

  const onAddValue = () => {
    if (validateAmount(newAmount)) {
      const numericAmount = Number(newAmount)
      const updatedDenominations = [
        ...(field.value as number[]),
        numericAmount,
      ].sort((a, b) => a - b)
      field.onChange(updatedDenominations)
      setNewAmount("")
      setInputError("")
    }
  }

  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault()
      onAddValue()
    }
  }

  const handleBlur = () => {
    if (newAmount) {
      onAddValue()
    }
  }

  const currentError = !!inputError || !!error
  const currentHelperText = inputError || helperText

  return (
    <FormControl error={currentError} fullWidth>
      <FormLabel required sx={{ mb: 1 }}>
        {label}
      </FormLabel>
      <Box
        sx={{
          border: "1px solid",
          borderColor: currentError ? "error.main" : "rgba(0, 0, 0, 0.23)",
          borderRadius: 1,
          p: 2,
        }}
      >
        <Box className="flex flex-wrap gap-2 mb-4">
          {(field.value as number[]).map((amount) => (
            <Chip
              key={amount}
              label={`$${amount}`}
              onDelete={() => handleDelete(amount)}
              color="primary"
              variant="outlined"
            />
          ))}
        </Box>
        <TextField
          value={newAmount}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          onBlur={handleBlur}
          placeholder="Add amount and press Enter"
          error={currentError}
          fullWidth
          variant="outlined"
          InputProps={{
            startAdornment: (
              <Box component="span" sx={{ mr: 1 }}>
                $
              </Box>
            ),
          }}
        />
      </Box>
      {currentHelperText && currentError && (
        <FormHelperText error>{currentHelperText}</FormHelperText>
      )}
    </FormControl>
  )
}
