import { useEffect, useMemo } from "react"

import { yupResolver } from "@hookform/resolvers/yup"
import { useFieldArray, useForm } from "react-hook-form"
import { CampaignTipDto } from "@/entities/campaignTip"
import { TipAmountData, forgotTipAmountSchema } from "./tipAmount.schema"
import { formatTipLabel } from "./formatTipLabel"

export const useTipAmountForm = (
  data?: CampaignTipDto[],
  initialEnabled?: boolean
) => {
  const initialTips = useMemo(
    () =>
      data?.map((item) => ({
        label: formatTipLabel(item.option_name),
        optionId: item.id,
        value: Number(item.value),
      })),
    [data]
  )

  const form = useForm<TipAmountData>({
    resolver: yupResolver(forgotTipAmountSchema),
    defaultValues: {
      tips: initialTips || [],
      enabled: initialEnabled ?? true,
    },
  })

  useEffect(() => {
    if (data && !form.formState.isDirty) {
      form.reset({
        tips: initialTips,
        enabled: initialEnabled ?? true,
      })
    }
  }, [data, initialEnabled, form, initialTips])

  const { fields } = useFieldArray({
    control: form.control,
    name: "tips",
  })

  return {
    ...form,
    fields,
  }
}
