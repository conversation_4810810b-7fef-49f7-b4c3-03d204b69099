import * as yup from "yup"

export const forgotTipAmountSchema = yup.object().shape({
  tips: yup
    .array()
    .of(
      yup.object().shape({
        value: yup
          .number()
          .min(0)
          .label("Tip")
          .positive()
          .transform((value) =>
            isNaN(value) || value === null || value === "" ? undefined : value
          )
          .max(100)
          .required(),
        label: yup.string(),
        optionId: yup.number(),
      })
    )
    .test("unique-values", "Duplicate tip", function (tips) {
      if (!tips) return true

      const { createError, path } = this

      const seen = new Map<number, number[]>()

      tips.forEach((tip, index) => {
        if (!seen.has(tip.value)) {
          seen.set(tip.value, [])
        }
        seen.get(tip.value)?.push(index)
      })

      const duplicates = Array.from(seen.entries()).filter(
        ([, indexes]) => indexes.length > 1
      )

      if (duplicates.length === 0) {
        return true
      }

      const firstDupIndex = duplicates[0][1][1]

      return createError({
        path: `${path}[${firstDupIndex}].value`,
        message: `Duplicate value`,
      })
    })
    .min(1),
  enabled: yup.boolean(),
})

export type TipAmountData = yup.InferType<typeof forgotTipAmountSchema>
