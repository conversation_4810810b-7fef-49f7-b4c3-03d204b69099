import { useParams } from "react-router-dom"
import { toast } from "react-toastify"

import { usePageTitle } from "@/shared/lib"
import { Button, Input, Toggle } from "@/shared/ui"
import { useGetCampaignById } from "@/entities/campaign"
import {
  useGetCampaignTips,
  useUpdateCampaignTips,
} from "@/entities/campaignTip"
import { Box, CircularProgress } from "@mui/material"
import { TipAmountData } from "../model/tipAmount.schema"
import { useTipAmountForm } from "../model/tipAmount.form"

const TipAmount = () => {
  usePageTitle("Tip Amount")
  const { campaignId } = useParams<{ campaignId: string }>()

  const { data: campaignDetails } = useGetCampaignById(Number(campaignId))

  const isEnabled = campaignDetails?.tipping_enabled

  const { data, isLoading } = useGetCampaignTips(Number(campaignId))

  const {
    fields,
    handleSubmit,
    control,
    reset,
    register,
    formState: { errors, isDirty },
  } = useTipAmountForm(data, isEnabled)

  const { mutate: updateTips, isPending } = useUpdateCampaignTips({
    onSuccess: (_, val) => {
      toast.success("Tip amounts updated successfully")
      reset(val.data)
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const onSubmit = (data: TipAmountData) => {
    const payload = {
      campaignId: Number(campaignId),
      data: {
        enabled: data.enabled!,
        tips: data.tips!.map((item) => ({
          option_name: item.label!,
          value: item.value!,
        })),
      },
    }

    updateTips(payload)
  }

  return (
    <div className="max-w-2xl mx-auto bg-white p-6">
      {(isLoading && (
        <div className="text-center">
          <CircularProgress size={48} />
        </div>
      )) || (
        <>
          <Box className="mb-4">
            <Toggle
              label="Enable Tip Amounts"
              control={control}
              name="enabled"
            />
          </Box>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box>
              {fields.map((field, index) => (
                <Box key={field.id}>
                  <Input
                    {...register(`tips.${index}.value`)}
                    type="number"
                    label={field?.label || ""}
                    error={!!errors.tips?.[index]}
                    helperText={errors.tips?.[index]?.value?.message}
                    InputProps={{
                      endAdornment: (
                        <Box component="span" sx={{ ml: 1 }}>
                          %
                        </Box>
                      ),
                    }}
                    variant="outlined"
                  />
                </Box>
              ))}
              <Box className="mt-4">
                <Button
                  type="submit"
                  loading={isPending}
                  variant="contained"
                  disabled={!isDirty || isPending}
                >
                  Update
                </Button>
              </Box>
            </Box>
          </form>
        </>
      )}
    </div>
  )
}

export default TipAmount
