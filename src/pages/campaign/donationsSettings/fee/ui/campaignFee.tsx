import { useGetCampaign<PERSON>ee, useUpdateCampaignFee } from "@/entities/campaignFee"
import { usePageTitle } from "@/shared/lib"
import { handleServerErrors, queryKeys, useGetCampaignId } from "@/shared/model"
import { Button, Input } from "@/shared/ui"
import { CircularProgress, InputAdornment } from "@mui/material"
import { useQueryClient } from "@tanstack/react-query"
import React from "react"
import { FieldError } from "react-hook-form"
import { toast } from "react-toastify"
import { useUpdateCampaignFeeForm } from "../model/updateCampaignFee.form"

const items = [
  { title: "Bottom", key: "bottom_range" },
  { title: "Top", key: "top_range" },
  { title: "Value", key: "percentage" },
] as const

const adornmentSymbol = (index: number) => (index === 2 ? "%" : "$")

const renderInput = (args: {
  value: string
  error?: FieldError
  tierIndex: number
  onChange: (value: string) => void
  disabled?: boolean
  key: (typeof items)[number]["key"]
  symbol?: string
}) => {
  const { value, disabled, tierIndex, symbol, key, error, onChange } = args

  return (
    <Input
      type="number"
      variant="outlined"
      key={`tiers.${tierIndex}.${key}`}
      name={`tiers.${tierIndex}.${key}`}
      value={value ?? ""}
      error={!!error}
      helperText={error?.message}
      disabled={disabled}
      onChange={(e) => {
        onChange(e.target.value)
      }}
      slotProps={{
        input: {
          startAdornment: (
            <InputAdornment position="start">{symbol ?? ""}</InputAdornment>
          ),
        },
      }}
    />
  )
}

const CampaignFundPage: React.FC = () => {
  usePageTitle("Fee")
  const campaignId = useGetCampaignId(true)
  const { data, isLoading } = useGetCampaignFee(campaignId)
  const queryClient = useQueryClient()

  const {
    watch,
    handleSubmit,
    reset,
    getValues,
    setValue,
    setError,
    formState: { errors, isDirty },
  } = useUpdateCampaignFeeForm(data)

  const { mutate: update, isPending } = useUpdateCampaignFee({
    onSuccess: () => {
      toast.success("Fees updated successfully")
      reset(getValues())
      queryClient.invalidateQueries({
        queryKey: [queryKeys.campaign.fee.get, campaignId],
      })
    },
    onError: (error) => {
      handleServerErrors({
        error,
        setError,
        message: "Failed to update fees",
      })
    },
  })

  const onSubmit = handleSubmit((formData) => {
    update({ campaignId, data: formData })
  })

  const fields = watch("tiers")

  return (
    <div className="w-full py-6">
      <div className="flex flex-wrap justify-flex-end items-center mb-6 gap-4">
        <div className="flex flex-wrap items-center ml-auto gap-4">
          <Button
            variant="outlined"
            onClick={onSubmit}
            className="px-2 py-1 max-w-fit ml-4"
            disabled={!isDirty}
            loading={isPending}
          >
            Update
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg mx-auto">
        {isLoading ? (
          <div className="p-6 text-center">
            <CircularProgress />
          </div>
        ) : (
          <div className="flex flex-col md:flex-row gap-4 p-6">
            <form onSubmit={onSubmit}>
              <div className="grid sm:grid-cols-3 gap-4">
                {fields.map((_, itemIndex) => {
                  const { title, key } = items[itemIndex]

                  return (
                    <div key={itemIndex}>
                      <label className="block mb-2 text-sm font-medium text-gray-900">
                        {title}
                      </label>
                      {items.map((_, tierIndex) => {
                        const notRender =
                          items.length - 1 <= tierIndex && key === "top_range"

                        if (notRender) {
                          return null
                        }

                        const onChange = (value: string) => {
                          const newValue = (
                            value === "" ? null : Number(value)
                          ) as number

                          setValue(`tiers.${tierIndex}.${key}`, newValue, {
                            shouldValidate: true,
                            shouldDirty: true,
                          })

                          if (
                            (tierIndex === 0 || tierIndex === 1) &&
                            key === "top_range"
                          ) {
                            setValue(
                              `tiers.${tierIndex + 1}.bottom_range`,
                              newValue,
                              {
                                shouldValidate: true,
                                shouldDirty: true,
                              }
                            )
                          }
                        }
                        const error = errors.tiers?.[tierIndex]?.[key]
                        const value = (fields[tierIndex][key] ??
                          null) as unknown as string

                        return renderInput({
                          value,
                          error,
                          onChange,
                          tierIndex,
                          symbol: adornmentSymbol(itemIndex),
                          disabled: itemIndex === 0 || notRender,
                          key,
                        })
                      })}
                    </div>
                  )
                })}
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  )
}

export default CampaignFundPage
