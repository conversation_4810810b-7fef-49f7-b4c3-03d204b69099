import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  UpdateCampaignFeeFormData,
  updateCampaignFeeSchema,
} from "./updateCampaignFee.schema"
import { CampaignFeeDto } from "@/entities/campaignFee"
import { useEffect } from "react"

const defaultValues = Array(3).fill({
  bottom_range: 0,
  top_range: 0,
  percentage: 0,
})

export const useUpdateCampaignFeeForm = (initialValues?: CampaignFeeDto[]) => {
  const form = useForm<UpdateCampaignFeeFormData>({
    resolver: yupResolver(updateCampaignFeeSchema),
    defaultValues: {
      tiers: defaultValues,
    },
  })

  useEffect(() => {
    if (!form.formState.isDirty && initialValues) {
      const preparedValues = initialValues.map((item) => ({
        bottom_range: item.bottom_range,
        top_range: item.top_range,
        percentage: item.percentage,
      }))

      form.reset({
        tiers: preparedValues,
      })
    }
  }, [form, initialValues])

  return form
}
