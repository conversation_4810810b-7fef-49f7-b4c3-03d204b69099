import * as yup from "yup"

const transformValue = (value: any) => {
  const newValue = typeof value === "string" ? Number(value) : (value ?? null)
  return isNaN(newValue) ? null : newValue
}

export const updateCampaignFeeSchema = yup.object().shape({
  tiers: yup
    .array()
    .min(1, "Tier values is required")
    .of(
      yup
        .object()
        .shape({
          bottom_range: yup
            .number()
            .nullable()
            .transform(transformValue)
            .min(0)
            .label("Bottom Range")
            .required("Bottom range is required"),
          top_range: yup
            .number()
            .nullable()
            .min(0)
            .transform(transformValue)
            .label("Top Range"),
          percentage: yup
            .number()
            .nullable()
            .transform(transformValue)
            .min(0)
            .max(100)
            .label("Percentage")
            .required("Percentage is required"),
        })
        .test("tier-validation", function (tier, context) {
          const { path, parent } = context
          const index = parseInt(path.split("[")[1]?.split("]")[0] || "0")
          const isLastTier = index === parent.length - 1
          const { top_range = 0, bottom_range = 0 } = tier

          const topRange = top_range ?? 0
          const bottomRange = bottom_range ?? 0

          if (!isLastTier && (topRange === null || topRange === undefined)) {
            return this.createError({
              path: `${path}.top_range`,
              message:
                "Top range is required for all tiers except the last one",
            })
          }

          if (topRange < bottomRange && !isLastTier) {
            return this.createError({
              path: `${path}.top_range`,
              message: "Top range must be greater than bottom range",
            })
          }

          if (index > 0) {
            const previousTier = parent[index - 1]
            if (
              previousTier.top_range !== null &&
              bottomRange !== null &&
              bottomRange < previousTier.top_range
            ) {
              return this.createError({
                path: `${path}.bottom_range`,
                message: "Bottom range must not overlap with previous tier",
              })
            }
          }

          return true
        })
    )
    .test(
      "top-range-validation",
      "Top range is required for all tiers",
      function (tiers) {
        if (!tiers || tiers.length === 0) return true

        for (let i = 0; i < tiers.length - 1; i++) {
          const tier = tiers[i]
          if (tier.top_range === null || tier.top_range === undefined) {
            return this.createError({
              path: `tiers[${i}].top_range`,
              message:
                "Top range is required for all tiers except the last one",
            })
          }
        }

        return true
      }
    )
    .required("Tier values is required"),
})

export type UpdateCampaignFeeFormData = yup.InferType<
  typeof updateCampaignFeeSchema
>
