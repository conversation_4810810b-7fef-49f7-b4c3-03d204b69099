import { Outlet, useLocation, useNavigate, useParams } from "react-router-dom"
import { Tab, Tabs } from "@mui/material"

import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { useMemo } from "react"

const tabs = [
  {
    label: "Fee",
    path: PATH.withAuth.campaign.donationsSettings.fee.url,
  },
  {
    label: "Donation Amounts",
    path: PATH.withAuth.campaign.donationsSettings.donationAmounts.url,
  },
  {
    label: "Tip Amounts",
    path: PATH.withAuth.campaign.donationsSettings.topAmount.url,
  },
]

const CampaignDonationsSettings = () => {
  usePageTitle("Donation Settings")
  const { campaignId } = useParams<{ campaignId: string }>()
  const navigate = useNavigate()
  const location = useLocation()

  const mappedTabs = useMemo(
    () =>
      tabs.map(({ path, ...rest }) => ({
        ...rest,
        path: path(campaignId!),
      })),
    [campaignId]
  )

  const currentTab = useMemo(
    () =>
      mappedTabs.find((tab) => {
        return location.pathname.includes(tab.path)
      })?.path || mappedTabs[0].path,
    [location.pathname, mappedTabs]
  )

  const handleTabChange = (_: React.SyntheticEvent, newPath: string) => {
    navigate(newPath)
  }

  return (
    <>
      <Tabs
        value={currentTab}
        onChange={handleTabChange}
        variant="scrollable"
        scrollButtons="auto"
        allowScrollButtonsMobile
      >
        {mappedTabs.map((tab) => (
          <Tab key={tab.path} label={tab.label} value={tab.path} />
        ))}
      </Tabs>
      <div className="pt-6">
        <Outlet />
      </div>
    </>
  )
}

export default CampaignDonationsSettings
