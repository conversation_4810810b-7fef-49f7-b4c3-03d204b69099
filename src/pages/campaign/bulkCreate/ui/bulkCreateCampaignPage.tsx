import { useRef, useState } from "react"
import { useNavigate } from "react-router-dom"
import { Typography } from "@mui/material"
import { toast } from "react-toastify"

import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { DashboardLayout, ListLayout } from "@/shared/layouts"
import { Autocomplete, Button, Option } from "@/shared/ui"
import { useBulkCreateCampaign, useGetCampaigns } from "@/entities/campaign"

type FieldErrors = Record<string, string[]>
type RowErrors = FieldErrors[]
type BatchErrors = RowErrors[]
type UploadErrors = FieldErrors | BatchErrors

const BulkCreateCampaignPage = () => {
  usePageTitle("Bulk Create Campaign")
  const navigate = useNavigate()
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<Option | null>(null)
  const [uploadErrors, setUploadErrors] = useState<string[]>([])

  const { data: campaignsList, isLoading: isLoadingCampaigns } =
      useGetCampaigns({
        page: 1,
        per_page: 1000,
      })

  const campaignOptions =
      campaignsList?.data
          .map((campaign) => ({
            label: campaign.name,
            value: campaign.campaign_id,
          })) || []

  const { mutate: bulkCreate, isPending: isBulkCreating } = useBulkCreateCampaign({
    onSuccess: () => {
      toast.success("Bulk campaign upload successful")
      navigate(PATH.withAuth.campaign.list)
    },
    onError: (error) => {
      const apiResponse = error.response?.data as { success?: boolean, message?: string, errors?: UploadErrors }

      if (apiResponse?.errors) {
        const parsedErrors = parseUploadErrors(apiResponse.errors)
        setUploadErrors(parsedErrors)
        toast.error(apiResponse.message || "Validation errors in import")
      } else {
        toast.error(apiResponse?.message || "Bulk campaign upload failed")
      }
    },
  })

  const onBackClick = () => {
    navigate(PATH.withAuth.campaign.list)
  }

  const handleFileSelectionClick = () => fileInputRef.current?.click();

  const handleFileSelectionChanged = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadErrors([])
    }
  };

  const parseUploadErrors = (errors: UploadErrors): string[] => {
    if (!errors) return []

    if (!Array.isArray(errors)) {
      return Object.entries(errors).flatMap(([field, messages]) =>
          messages.map((msg) => `${field}: ${msg}`)
      )
    }

    return errors.flatMap((batch: RowErrors) =>
        batch.flatMap((errorObj: FieldErrors, rowIndex: number) =>
            Object.entries(errorObj).flatMap(([field, messages]) =>
                messages.map((msg) => `Row ${rowIndex + 1} - ${field}: ${msg}`)
            )
        )
    )
  }

  const onSubmit = () => {
    bulkCreate({
      campaignId: selectedCampaign?.value,
      file: selectedFile!,
    })
  }

  return (
      <DashboardLayout title="Bulk Create Campaign" isLoading={isBulkCreating}>
        <ListLayout title="Bulk Create Campaign" onBackClick={onBackClick}>
          <div className="w-full bg-white rounded p-6 mx-auto max-w-2xl">
            <form className="max-w-2xl mx-auto">
              <div className="flex gap-4 items-center mb-4">
                <div className="flex-1">
                  <Autocomplete
                      label="Campaign"
                      name="campaignId"
                      value={selectedCampaign}
                      onChange={(value) => {
                        if (!value || Array.isArray(value)) return
                        setSelectedCampaign(value)
                      }}
                      isLoading={isLoadingCampaigns}
                      options={campaignOptions}
                      disableClearable={false}
                  />
                </div>
              </div>
              <div className="mt-4 flex items-center gap-3">
                <input
                    type="file"
                    ref={fileInputRef}
                    style={{ display: "none" }}
                    accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    onChange={handleFileSelectionChanged}
                />
                <Button
                    variant="contained"
                    onClick={handleFileSelectionClick}
                    sx={{
                      width: "auto",
                      minWidth: "fit-content",
                    }}
                >
                  Choose File
                </Button>
                {selectedFile && (
                    <Typography
                        variant="body2"
                        noWrap
                        sx={{ overflow: "hidden", textOverflow: "ellipsis" }}
                    >
                      {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                    </Typography>
                )}
              </div>
              <div className="mt-4">
                <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    onClick={onSubmit}
                    disabled={!selectedFile}
                >
                  Upload to Server
                </Button>
              </div>
              {uploadErrors.length > 0 && (
                  <div className="mt-4 bg-red-50 border border-red-200 text-red-700 p-3 rounded">
                    <Typography variant="body2" fontWeight="bold" className="mb-2">
                      Validation Errors:
                    </Typography>
                    <div
                        style={{
                          maxHeight: "450px",
                          overflowY: "auto",
                        }}
                    >
                      <ul className="list-disc ml-5 pr-2">
                        {uploadErrors.map((err, idx) => (
                            <li key={idx}>{err}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
              )}
            </form>
          </div>
        </ListLayout>
      </DashboardLayout>
  )
}

export default BulkCreateCampaignPage
