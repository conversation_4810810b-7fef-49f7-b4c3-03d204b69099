import { useNavigate } from "react-router-dom"
import { useCallback, useMemo, useState } from "react"
import { ConfirmDialog } from "@/shared/ui/ConfirmDialog"
import { toast } from "react-toastify"

import { PATH } from "@/shared/config"
import { Table, Button, Input, Autocomplete } from "@/shared/ui"
import { usePageTitle } from "@/shared/lib"
import { DashboardLayout } from "@/shared/layouts"
import { ListLayout } from "@/shared/layouts"

import {
  useDeleteCampaign,
  useGetCampaigns,
  CampaignFilterDto,
  statusOptions
} from "@/entities/campaign"
import { useCampaignsListTableData } from "../model/campaignsList.hooks"
import { useCampaignStore, usePagination } from "@/shared/model"
import { useGetSalesPersons } from "@/entities/salesPerson"
import { useCampaignFilterForm } from "../model/campaignListFilter.form"
import { CampaignFilterForm } from "../model/campaignsListFilter.schema"

const CampaignsList = () => {
  usePageTitle("Campaigns")
  const navigate = useNavigate()
  const { setSelectedCampaign } = useCampaignStore()
  const { page, onPaginationModelChange, pageSize } = usePagination()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedCampaignId, setSelectedCampaignId] = useState<number | null>(
    null
  )

  const [filters, setFilters] = useState<CampaignFilterDto>({
    name: null,
    status_id: null,
    owner_id: null,
  })

  const { control, handleSubmit, reset, register } = useCampaignFilterForm()
  const { data: salesPersons, isLoading: isLoadingSalesPersons } = useGetSalesPersons()

  const salesPersonsOptions = useMemo(() => (
    salesPersons?.map((salesPerson) => ({
      value: salesPerson.user_id,
      label: `${salesPerson.first_name} ${salesPerson.last_name}`,
    })) ?? []
  ), [salesPersons])

  const queryParams = {
    page,
    per_page: pageSize,
    ...filters,
  }

  const {data: campaignsList, isLoading } = useGetCampaigns(queryParams)
  const { mutate: deleteCampaign, isPending: isDeleting } = useDeleteCampaign()

  const handleCreateCampaign = () => {
    navigate(PATH.withAuth.campaign.create)
  }

  const handleBulkCreateCampaign = () => {
    navigate(PATH.withAuth.campaign.bulkCreate)
  }


  const handleOpen = useCallback(
    (id: number) => {
      const selectedCampaign = campaignsList?.data!.find(
        (c) => c.campaign_id === id
      )

      setSelectedCampaign({
        campaign_user_id: null,
        campaign_id: selectedCampaign?.campaign_id as number,
        campaign_name: selectedCampaign?.name as string,
        campaign_role_id: null,
        campaign_role_name: null,
        campaign_group_id: null,
      })

      navigate(PATH.withAuth.campaign.details.url(id))
    },
    [navigate, campaignsList?.data, setSelectedCampaign]
  )

  const handleDeleteClick = (id: number) => {
    setSelectedCampaignId(id)
    setDeleteDialogOpen(true)
  }

  const handleDelete = () => {
    if (selectedCampaignId) {
      deleteCampaign(selectedCampaignId, {
        onSuccess: () => {
          toast.success("Campaign deleted successfully")
        },
        onError: () => {
          toast.error("Failed to delete campaign")
        },
      })
      setDeleteDialogOpen(false)
      setSelectedCampaignId(null)
    }
  }

  const onSubmit = (data: CampaignFilterForm) => {
    setFilters({
      name: data.name || null,
      status_id: data.status_id?.value ?? null,
      owner_id: data.owner_id?.value ?? null,
    })
  }

  const onReset = () => {
    reset()
    setFilters({
      name: null,
      status_id: null,
      owner_id: null,
    })
  }

  const selectedCampaign = campaignsList?.data?.find(
    (campaign) => campaign.campaign_id === selectedCampaignId
  )

  const actions = [
    {
      label: "Create Campaign",
      onClick: handleCreateCampaign,
    },
    {
      label: "Bulk Create Campaigns",
      onClick: handleBulkCreateCampaign
    }
  ]

  const { columns, rows } = useCampaignsListTableData({
    onOpen: handleOpen,
    onDelete: handleDeleteClick,
    campaigns: campaignsList?.data ?? [],
  })

  return (
    <DashboardLayout title="Campaigns">
      <ListLayout hideBackButton title="Campaigns" actions={actions}>
        <form
          className="flex w-full flex-col gap-6 mb-12 md:mb-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="flex flex-col md:flex-row md:flex-wrap md:gap-6 gap-4 w-full">
            <div className="w-full md:w-60">
              <Input
                {...register("name")}
                name="name"
                label="Campaign Name"
                variant="standard"
                placeholder="Search by name..."
              />
            </div>

            <div className="w-full md:w-48">
              <Autocomplete
                name="status_id"
                label="Status"
                options={statusOptions}
                control={control}
                fieldVariant="standard"
              />
            </div>

            <div className="w-full md:w-48">
              <Autocomplete
                name="owner_id"
                label="Owner"
                options={salesPersonsOptions}
                control={control}
                isLoading={isLoadingSalesPersons}
                fieldVariant="standard"
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-5 w-full md:w-auto py-4">
              <Button
                variant="contained"
                color="primary"
                type="submit"
                size="medium"
                className="w-full sm:w-auto"
              >
                Apply
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={onReset}
                size="medium"
                className="w-full sm:w-auto"
              >
                Reset
              </Button>
            </div>
          </div>
        </form>

        <Table
          columns={columns}
          rows={rows}
          rowCount={campaignsList?.meta?.total || 0}
          loading={isLoading || isDeleting}
          page={
            campaignsList?.meta?.current_page
              ? campaignsList.meta.current_page - 1
              : 0
          }
          pageSize={pageSize}
          onPaginationModelChange={onPaginationModelChange}
          height={1000}
          onRowClick={(params) => handleOpen(params.row.id)}
        />
        <ConfirmDialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          onConfirm={handleDelete}
          title="Delete Campaign"
          content={`Are you sure you want to delete ${selectedCampaign?.name} campaign?`}
        />
      </ListLayout>
    </DashboardLayout>
  )
}

export default CampaignsList