import * as yup from "yup"

export const campaignFilterSchema = yup.object().shape({
  name: yup.string().default(""),
  status_id: yup
    .object({
      value: yup.number().required(),
      label: yup.string().required(),
    })
    .nullable()
    .notRequired(),
  owner_id: yup
    .object({
      value: yup.number().required(),
      label: yup.string().required(),
    })
    .nullable()
    .notRequired(),
})

export type CampaignFilterForm = yup.InferType<typeof campaignFilterSchema>