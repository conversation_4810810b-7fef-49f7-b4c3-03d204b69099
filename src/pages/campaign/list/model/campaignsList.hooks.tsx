import { useMemo } from "react"
import { getColumns } from "./campaignsList.columns"
import { CampaignDto } from "@/entities/campaign"

interface UseCampaignsListTableDataProps {
  onOpen: (id: number) => void
  onDelete: (id: number) => void
  campaigns: CampaignDto[]
}

export const useCampaignsListTableData = ({
  onOpen,
  onDelete,
  campaigns,
}: UseCampaignsListTableDataProps) => {
  const columns = useMemo(
    () => getColumns({ onOpen, onDelete }),
    [onOpen, onDelete]
  )

  const rows = useMemo(
    () =>
      campaigns.map((campaign) => ({
        ...campaign,
        id: campaign.campaign_id,
      })),
    [campaigns]
  )

  return {
    columns,
    rows,
  }
}
