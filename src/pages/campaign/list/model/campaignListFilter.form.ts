import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import { campaignFilterSchema, CampaignFilterForm } from "./campaignsListFilter.schema"

interface UseCampaignFilterFormProps {
  defaultValues?: Partial<CampaignFilterForm>
}

export const useCampaignFilterForm = ({
  defaultValues
}: UseCampaignFilterFormProps = {}) => {
  return useForm<CampaignFilterForm>({
    resolver: yupResolver(campaignFilterSchema),
    defaultValues: {
      name: "",
      status_id: null,
      owner_id: null,
      ...defaultValues,
    },
  })
}