import { IconButton } from "@/shared/ui"
import DeleteIcon from "@mui/icons-material/Delete"
import OpenInNewIcon from "@mui/icons-material/OpenInNew"
import { GridRenderCellParams } from "@mui/x-data-grid"
import { format } from "date-fns"
interface GetColumnsProps {
  onOpen: (id: number) => void
  onDelete: (id: number) => void
}

export const getColumns = ({ onOpen, onDelete }: GetColumnsProps) => {
  return [
    {
      field: "id",
      headerName: "ID",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "donations_raised",
      headerName: "Total Donations",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "fundraising_goal",
      headerName: "Goal",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "campaign_start_date",
      headerName: "Start Date",
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) =>
        params.row.campaign_start_date
          ? format(new Date(params.row.campaign_start_date), "MM/dd/yyyy")
          : "",
    },
    {
      field: "campaign_end_date",
      headerName: "End Date",
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) =>
        params.row.campaign_end_date
          ? format(new Date(params.row.campaign_end_date), "MM/dd/yyyy")
          : "",
    },
    {
      field: "campaign_owner",
      headerName: "Campaign Owner",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <>
          <IconButton onClick={() => onOpen(params.row.id)} title="View">
            <OpenInNewIcon />
          </IconButton>
          <IconButton
            onClick={(e) => {
              e.stopPropagation()
              onDelete(params.row.id)
            }}
            title="Delete"
          >
            <DeleteIcon />
          </IconButton>
        </>
      ),
    },
  ]
}
