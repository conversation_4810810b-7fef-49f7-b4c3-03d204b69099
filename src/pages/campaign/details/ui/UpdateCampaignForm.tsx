import React from "react"
import { toast } from "react-toastify"

import {
  CampaignDto,
  UpdateCampaignDto,
  useUpdateCampaign,
} from "@/entities/campaign"
import { useGetCampaignData } from "@/features/getCampaignSelectors"
import { getLocalDateString } from "@/shared/lib/date"
import { Autocomplete } from "@/shared/ui/autocomplete"
import { Button } from "@/shared/ui/Button"
import { Checkbox } from "@/shared/ui/Checkbox"
import { ColorPicker } from "@/shared/ui/ColorPicker"
import { DatePickerField } from "@/shared/ui/DatePickerField"
import { Input } from "@/shared/ui/Input"
import { RichTextEditor } from "@/shared/ui/richTextEditor"
import { Controller } from "react-hook-form"
import { useUpdateCampaignForm } from "../model/updateCampaign.form"
import { UpdateCampaignFormData } from "../model/updateCampaign.schema"
import { TooltipIcon } from "@/shared/ui/tooltipIcon"

interface UpdateCampaignFormProps {
  campaignId: number
  initialData: CampaignDto
  onUpdateSuccess?: () => void
}

export const UpdateCampaignForm: React.FC<UpdateCampaignFormProps> = ({
  campaignId,
  initialData,
  onUpdateSuccess,
}) => {
  const {
    levelsOptions,
    gendersOptions,
    sportsOptions,
    causesOptions,
    stateOptions,
    isLoading,
    salesPersonsOptions,
    isLoadingSalesPersons,
  } = useGetCampaignData()

  const { mutate: updateCampaign } = useUpdateCampaign({
    onSuccess: () => {
      toast.success("Campaign updated successfully")
      onUpdateSuccess?.()
    },
    onError: () => {
      toast.error("Failed to update campaign")
    },
  })

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    statusOptions,
  } = useUpdateCampaignForm(initialData)

  const onSubmit = (data: UpdateCampaignFormData) => {
    const updatePayload: Partial<UpdateCampaignDto> = {
      team_display_name: data.teamDisplayName,
      name: data.name,
      campaign_owner_id: Number(data.campaign_owner_id?.value),
      level_id: data.levelId?.value,
      gender_id: data.genderId?.value,
      sport_id: data.sportId?.value,
      cause_id: data.causeId?.value ?? null,
      status_id: data.statusId?.value,
      school_org_name: data.schoolOrgName,
      mascot: data.mascot,
      fundraising_goal: data.fundraisingGoal,
      roster_size: Number(data.rosterSize),
      auto_increase_goal: !!data.autoIncreaseGoal,
      primary_color: data.primaryColor,
      secondary_color: data.secondaryColor,
      donation_page_title: data.donation_page_title,
      city: data.city,
      state: data.state?.value,
      zip: data.zip,
      campaign_start_date: data.campaignStartDate
        ? getLocalDateString(data.campaignStartDate)
        : "",
      campaign_end_date: data.campaignEndDate
        ? getLocalDateString(data.campaignEndDate)
        : "",
      our_message_text: data.ourMessageText,
      show_leaderboard: !!data.show_leaderboard,
    }

    try {
      updateCampaign({ id: campaignId, campaign: updatePayload })
    } catch {
      toast.error("Failed to update campaign")
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Input
        label="Team Display Name"
        {...register("teamDisplayName")}
        error={!!errors.teamDisplayName}
        helperText={errors.teamDisplayName?.message}
        variant="outlined"
        required
      />
      <Input
        label="Campaign Name"
        {...register("name")}
        error={!!errors.name}
        helperText={errors.name?.message}
        variant="outlined"
        required
      />
      <Input
        label="Donation Page Title"
        {...register("donation_page_title")}
        error={!!errors.donation_page_title}
        helperText={errors.donation_page_title?.message}
        variant="outlined"
        required
      />

      <Autocomplete
        name="campaign_owner_id"
        label="Campaign Owner"
        options={salesPersonsOptions}
        control={control}
        error={!!errors?.campaign_owner_id}
        helperText={errors?.campaign_owner_id?.message}
        isLoading={isLoadingSalesPersons}
        required
      />

      <Autocomplete
        name="levelId"
        label="Level"
        options={levelsOptions}
        control={control}
        error={!!errors.levelId}
        helperText={errors.levelId?.message}
        isLoading={isLoading}
        required
      />

      <Autocomplete
        name="genderId"
        label="Gender"
        options={gendersOptions}
        control={control}
        error={!!errors.genderId}
        helperText={errors.genderId?.message}
        isLoading={isLoading}
        required
      />

      <Autocomplete
        name="sportId"
        label="Sport"
        options={sportsOptions}
        control={control}
        error={!!errors.sportId}
        helperText={errors.sportId?.message}
        isLoading={isLoading}
        required
      />

      <Autocomplete
        name="statusId"
        label="Status"
        options={statusOptions}
        control={control}
        error={!!errors.statusId}
        helperText={errors.statusId?.message}
        required
      />

      <Input
        label="School/Organization Name"
        {...register("schoolOrgName")}
        error={!!errors.schoolOrgName}
        helperText={errors.schoolOrgName?.message}
        variant="outlined"
        required
      />

      <Input
        label="Mascot"
        {...register("mascot")}
        error={!!errors.mascot}
        helperText={errors.mascot?.message}
        variant="outlined"
      />

      <Input
        label="Fundraising Goal"
        type="number"
        {...register("fundraisingGoal")}
        error={!!errors.fundraisingGoal}
        helperText={errors.fundraisingGoal?.message}
        variant="outlined"
        required
      />

      <Autocomplete
        name="causeId"
        label="Cause"
        options={causesOptions}
        control={control}
        error={!!errors.causeId}
        helperText={errors.causeId?.message}
        isLoading={isLoading}
      />

      <Input
        label="Roster Size"
        type="number"
        {...register("rosterSize")}
        error={!!errors.rosterSize}
        helperText={errors.rosterSize?.message}
        variant="outlined"
      />

      <Input
        label="City"
        {...register("city")}
        error={!!errors.city}
        helperText={errors.city?.message}
        variant="outlined"
        required
      />

      <Autocomplete
        name="state"
        label="State"
        options={stateOptions}
        control={control}
        error={!!errors.state}
        helperText={errors.state?.message}
        required
      />

      <Input
        label="ZIP Code"
        type="number"
        {...register("zip")}
        error={!!errors.zip}
        helperText={errors.zip?.message}
        variant="outlined"
        required
      />

      <div className="grid grid-cols-2 gap-4">
        <div className="col-span-1">
          <DatePickerField
            name="campaignStartDate"
            label="Campaign Start Date"
            control={control}
            error={errors?.campaignStartDate}
            required
          />
        </div>
        <div className="col-span-1 mb-6">
          <DatePickerField
            name="campaignEndDate"
            label="Campaign End Date"
            control={control}
            error={errors?.campaignEndDate}
            required
          />
        </div>
      </div>

      <div className="mb-4 flex items-center space-x-1">
        <Checkbox
          name="autoIncreaseGoal"
          label="Auto Increase Goal"
          control={control}
        />
        <TooltipIcon
          text="The goal increases by 25% when 90% of base donations (excluding tips) is reached. Checked daily."
        />
      </div>

      <div className="mb-6">
        <Checkbox
          name="show_leaderboard"
          label="Show Leaderboard"
          control={control}
        />
      </div>

      <div className="mb-4">
        <ColorPicker
          name="primaryColor"
          label="Primary Color"
          control={control}
          error={!!errors.primaryColor}
          required
          defaultValue={initialData.colors?.primary}
        />
      </div>

      <div className="mb-4">
        <ColorPicker
          name="secondaryColor"
          label="Secondary Color"
          control={control}
          error={!!errors.secondaryColor}
          required
          defaultValue={initialData.colors?.secondary}
        />
      </div>

      <div className="mb-4">
        <label className="block mb-2 text-sm font-medium">
          Our Message <span className="text-red-500">*</span>
          {errors.ourMessageText && (
            <span className="text-red-500 ml-1">
              * {errors.ourMessageText.message}
            </span>
          )}
        </label>
        <Controller
          name="ourMessageText"
          control={control}
          render={({ field }) => (
            <RichTextEditor
              initialContent={initialData.our_message_text}
              onChange={(content) => field.onChange(content)}
            />
          )}
        />
      </div>

      <Button type="submit" variant="contained" fullWidth>
        Update Campaign
      </Button>
    </form>
  )
}
