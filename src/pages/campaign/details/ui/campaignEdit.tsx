import { useGetCampaignById } from "@/entities/campaign"
import { PATH } from "@/shared/config"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/shared/ui"
import React from "react"
import { useNavigate, useParams } from "react-router-dom"
import { UpdateCampaignForm } from "./UpdateCampaignForm"

const CampaignEdit: React.FC = () => {
  const navigate = useNavigate()
  const { campaignId } = useParams<{ campaignId: string }>()

  const { data: campaignDetails, isLoading: isLoadingDetails } =
    useGetCampaignById(Number(campaignId))

  if (isLoadingDetails) {
    return <div>Loading campaign details...</div>
  }

  const handleUpdateSuccess = () => {
    navigate(PATH.withAuth.campaign.details.url(campaignId!))
  }

  const handleCampaignDonationsClick = () => {
    const guid = campaignDetails?.guid

    if (guid) {
      window.open(PATH.withoutAuth.donation.url({ guid }), "_blank")
    }
  }

  const onBackClick = () => {
    navigate(PATH.withAuth.campaign.details.url(campaignId!))
  }

  return (
    <div className="w-full py-6">
      <div className="flex flex-wrap items-center mb-6 gap-4">
        <h2 className="text-2xl sm:text-3xl font-semibold mr-10">
          Campaign Details Edit
        </h2>
        <div className="flex flex-wrap items-center ml-auto gap-4">
          <Button
            variant="contained"
            onClick={handleCampaignDonationsClick}
            className="px-2 py-1 max-w-fit ml-4"
          >
            Preview Donation
          </Button>
        </div>
      </div>

      <Wrapper isForm onBack={onBackClick} backTitle="Campaign Details">
        <UpdateCampaignForm
          campaignId={Number(campaignId)}
          initialData={campaignDetails!}
          onUpdateSuccess={handleUpdateSuccess}
        />
      </Wrapper>
    </div>
  )
}

export default CampaignEdit
