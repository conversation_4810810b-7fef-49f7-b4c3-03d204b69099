import { useGetCampaignById } from "@/entities/campaign"
import { PATH } from "@/shared/config"
import { Button } from "@/shared/ui"
import { ActionButton } from "@/shared/ui/ActionButton"
import React from "react"
import { useNavigate, useParams } from "react-router-dom"
import CampaignInfo from "./campaignInfo"
import CampaignQRCode from "./campaignQRCode"

const CampaignDetails: React.FC = () => {
  const navigate = useNavigate()
  const { campaignId } = useParams<{ campaignId: string }>()

  const { data: campaignDetails, isLoading: isLoadingDetails } =
    useGetCampaignById(Number(campaignId))

  if (isLoadingDetails) {
    return <div>Loading campaign details...</div>
  }

  const handleCampaignDonationsClick = () => {
    const guid = campaignDetails?.guid

    if (guid) {
      window.open(PATH.withoutAuth.donation.url({ guid }), "_blank")
    }
  }

  const pin = campaignDetails?.pin

  return (
    <div className="w-full py-6">
      <div className="flex flex-wrap items-center mb-6 gap-4">
        <h2 className="text-2xl sm:text-3xl font-semibold mr-10">
          Campaign Details
        </h2>
        <div className="flex flex-wrap items-center ml-auto gap-4">
          <ActionButton
            typeAction="edit"
            variant="outlined"
            onClick={() =>
              navigate(PATH.withAuth.campaign.edit.url(campaignId!))
            }
            className="px-2 py-1 max-w-fit ml-4"
          >
            Edit Campaign
          </ActionButton>
          <Button
            variant="contained"
            onClick={handleCampaignDonationsClick}
            className="px-2 py-1 max-w-fit ml-4"
          >
            Preview Donation
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg mx-auto ">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="p-6 flex-grow">
            <CampaignInfo info={campaignDetails} />
          </div>
          {!!pin && (
            <div className="border-l min-w-[200px] border-gray-300">
              <div className="p-6 ">
                <CampaignQRCode pin={pin} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CampaignDetails
