import { PATH } from "@/shared/config"
import React from "react"
import QRCode from "react-qr-code"

interface CampaignQRCodeProps {
  pin: string
  baseUrl?: string
}

const url = `${window.location.origin}${PATH.withoutAuth.welcome}`

const CampaignQRCode: React.FC<CampaignQRCodeProps> = ({
  pin,
  baseUrl = url,
}) => {
  const loginUrl = `${baseUrl}?pin=${pin}`

  return (
    <div className="flex flex-col items-center border rounded-lg p-4 h-fit">
      <h3 className="text-lg font-medium mb-2">Campaign QR Code</h3>
      <QRCode
        value={loginUrl}
        size={230}
        style={{
          maxWidth: "100%",
        }}
      />
    </div>
  )
}

export default CampaignQRCode
