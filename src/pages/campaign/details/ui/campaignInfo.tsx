import { format } from "date-fns"
import DOMPurify from "dompurify"
import React from "react"
import { TooltipIcon } from "@/shared/ui/tooltipIcon"

interface Props {
  info?: {
    team_display_name: string
    donation_page_title: string
    name: string
    pin: string
    level: string
    gender: string
    sport: string
    school_org_name: string
    mascot: string
    fundraising_goal: string
    cause: string
    roster_size: number
    auto_increase_goal: boolean
    colors: {
      primary: string
      secondary: string
    }
    status: string
    our_message_text: string
    city: string
    state: string
    zip: string
    campaign_start_date: string
    campaign_end_date: string
    campaign_owner_id: number | null
    campaign_owner: string
    show_leaderboard: boolean
  }
}

const CampaignInfo: React.FC<Props> = ({ info }) => {
  const sanitizedMessage = info?.our_message_text
    ? DOMPurify.sanitize(info?.our_message_text)
    : ""

  const parseDateFromYMD = (dateStr: string) => {
    const [year, month, day] = dateStr.split("-").map(Number)
    return new Date(year, month - 1, day)
  }

  return (
    <div className="space-y-4 text-base sm:text-lg">
      <div>
        <strong>Team Display Name:</strong>{" "}
        <span>{info?.team_display_name}</span>
      </div>
      <div>
        <strong>Campaign Name:</strong> <span>{info?.name}</span>
      </div>
      <div>
        <strong>Donation Page Title:</strong>{" "}
        <span>{info?.donation_page_title}</span>
      </div>
      <div>
        <strong>Campaign Owner:</strong> <span>{info?.campaign_owner}</span>
      </div>
      <div>
        <strong>PIN:</strong> <span>{info?.pin}</span>
      </div>
      <div>
        <strong>Status:</strong> <span>{info?.status}</span>
      </div>
      <div>
        <strong>Level:</strong> <span>{info?.level}</span>
      </div>
      <div>
        <strong>Gender:</strong> <span>{info?.gender}</span>
      </div>
      <div>
        <strong>Sport:</strong> <span>{info?.sport}</span>
      </div>
      <div>
        <strong>School/Organization:</strong>{" "}
        <span>{info?.school_org_name}</span>
      </div>
      <div>
        <strong>Mascot:</strong> <span>{info?.mascot}</span>
      </div>
      <div>
        <strong>Fundraising Goal:</strong> $
        <span>{info?.fundraising_goal}</span>
      </div>
      <div>
        <strong>Cause:</strong> <span>{info?.cause || "-"}</span>
      </div>
      <div>
        <strong>Roster Size:</strong> <span>{info?.roster_size || "-"}</span>
      </div>
      <div>
        <strong>Location:</strong>{" "}
        <span>
          {[info?.city, info?.state, info?.zip].filter(Boolean).join(", ") ||
            "-"}
        </span>
      </div>
      <div>
        <strong>Campaign Start Date:</strong>{" "}
        <span>
          {info?.campaign_start_date
            ? format(parseDateFromYMD(info?.campaign_start_date), "MM/dd/yyyy")
            : "-"}
        </span>
      </div>
      <div>
        <strong>Campaign End Date:</strong>{" "}
        <span>
          {info?.campaign_end_date
            ? format(parseDateFromYMD(info?.campaign_end_date), "MM/dd/yyyy")
            : "-"}
        </span>
      </div>
      <div>
        <strong>Auto Increase Goal:</strong>{" "}
        <span>{info?.auto_increase_goal ? "Yes" : "No"}</span>
        <TooltipIcon
          text="The goal increases by 25% when 90% of base donations (excluding tips) is reached. Checked daily."
          iconClassName="ml-3"
        />
      </div>
      <div>
        <strong>Show Leaderboard:</strong>{" "}
        <span>{info?.show_leaderboard ? "Yes" : "No"}</span>
      </div>
      <div>
        <strong>Primary Color:</strong>
        <div
          className="w-24 h-8 align-middle inline-block ml-2"
          style={{
            backgroundColor: info?.colors?.primary,
          }}
        />
      </div>
      <div>
        <strong>Secondary Color:</strong>
        <div
          className="w-24 h-8 inline-block align-middle ml-2"
          style={{
            backgroundColor: info?.colors?.secondary,
          }}
        />
      </div>
      <div>
        <strong>Our Message Text:</strong>{" "}
        <div
          className="max-w-[600px] break-words"
          dangerouslySetInnerHTML={{ __html: sanitizedMessage }}
        />
      </div>
    </div>
  )
}

export default CampaignInfo
