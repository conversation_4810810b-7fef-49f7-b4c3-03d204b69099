import { CampaignDto, statusOptions } from "@/entities/campaign"
import { useGetCampaignData } from "@/features/getCampaignSelectors"
import { yupResolver } from "@hookform/resolvers/yup"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import {
  UpdateCampaignFormData,
  updateCampaignSchema,
} from "./updateCampaign.schema"

export const useUpdateCampaignForm = (initCampaignData?: CampaignDto) => {
  const { setValue, ...form } = useForm<UpdateCampaignFormData>({
    resolver: yupResolver(updateCampaignSchema),
    defaultValues: getDefaultValues(
      initCampaignData!,
      [],
      [],
      [],
      [],
      [],
      [],
      []
    ),
  })

  const {
    levelsOptions,
    gendersOptions,
    sportsOptions,
    causesOptions,
    isLoading,
    salesPersonsOptions,
    stateOptions,
  } = useGetCampaignData()

  useEffect(() => {
    if (isLoading) return

    const {
      levelId,
      genderId,
      sportId,
      causeId,
      statusId,
      campaign_owner_id,
      state,
    } = getDefaultValues(
      initCampaignData!,
      levelsOptions,
      gendersOptions,
      sportsOptions,
      causesOptions,
      statusOptions,
      salesPersonsOptions,
      stateOptions
    )

    setValue("levelId", levelId)
    setValue("genderId", genderId)
    setValue("sportId", sportId)
    setValue("causeId", causeId)
    setValue("statusId", statusId)
    setValue("campaign_owner_id", campaign_owner_id)
    setValue("state", state)
  }, [
    initCampaignData,
    setValue,
    isLoading,
    levelsOptions,
    gendersOptions,
    sportsOptions,
    causesOptions,
  ])

  return {
    setValue,
    statusOptions,
    ...form,
  }
}

const parseDateString = (dateStr?: string) => {
  if (!dateStr) return new Date()

  const [year, month, day] = dateStr.split('-').map(Number)

  return new Date(year, month - 1, day, 12, 0, 0);
}

export const getDefaultValues = (
  campaign: CampaignDto,
  levelsOptions: { value: number; label: string }[],
  gendersOptions: { value: number; label: string }[],
  sportsOptions: { value: number; label: string }[],
  causesOptions: { value: number; label: string }[],
  statusOptions: { value: number; label: string }[],
  salesPersonsOptions: { value: number; label: string }[],
  stateOptions: { value: string; label: string }[]
): UpdateCampaignFormData => {
  return {
    teamDisplayName: campaign.team_display_name,
    name: campaign.name,
    levelId: levelsOptions.find((option) => option.label === campaign.level)!,
    genderId: gendersOptions.find(
      (option) => option.label === campaign.gender
    )!,
    sportId: sportsOptions.find((option) => option.label === campaign.sport)!,
    causeId: causesOptions.find((option) => option.label === campaign.cause),
    statusId: statusOptions.find((option) => option.label === campaign.status)!,
    campaign_owner_id: salesPersonsOptions.find(
      (option) => option.value === campaign.campaign_owner_id
    )!,
    schoolOrgName: campaign.school_org_name,
    donation_page_title: campaign.donation_page_title,
    mascot: campaign.mascot,
    fundraisingGoal: parseInt(campaign.fundraising_goal),
    rosterSize: campaign.roster_size,
    autoIncreaseGoal: campaign.auto_increase_goal,
    primaryColor: campaign.colors?.primary,
    secondaryColor: campaign.colors?.secondary,
    city: campaign.city,
    state: stateOptions.find((option) => option.label === campaign.state)!,
    zip: campaign.zip,
    campaignStartDate: parseDateString(campaign.campaign_start_date),
    campaignEndDate: parseDateString(campaign.campaign_end_date),
    ourMessageText: campaign.our_message_text,
    show_leaderboard: campaign.show_leaderboard,
  }
}
