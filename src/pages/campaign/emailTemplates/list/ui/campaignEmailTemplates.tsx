import { useNavigate, useParams } from "react-router"
import { usePageTitle } from "@/shared/lib"
import { useGetCampaignMessageTemplates } from "@/entities/campaignMessageTemplate"
import { MessageTemplates } from "@/widgets/campaignMessageTemplate"
import { campaignMessageTemplateApi } from "@/entities/campaignMessageTemplate"
import { PATH } from "@/shared/config"
import { useCallback } from "react"

const type = "email"

const CampaignEmailTemplates = () => {
  usePageTitle("Email Templates")
  const { campaignId } = useParams<{ campaignId: string }>()
  const navigate = useNavigate()
  const { data, isLoading: isLoadingTemplates } =
    useGetCampaignMessageTemplates(Number(campaignId), type)

  const handleEdit = (id: number) => {
    navigate(PATH.withAuth.campaign.emailTemplates.details.url(campaignId!, id))
  }

  const onGetDetails = useCallback(
    async (id: number) => {
      const data = await campaignMessageTemplateApi.getById(
        Number(campaignId),
        id
      )
      return data?.[0]
    },
    [campaignId]
  )

  return (
    <MessageTemplates
      getDetails={onGetDetails}
      handleEdit={handleEdit}
      data={data ?? []}
      isLoading={isLoadingTemplates}
    />
  )
}

export default CampaignEmailTemplates
