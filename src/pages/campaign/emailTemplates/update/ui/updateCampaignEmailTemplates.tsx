import { useNavigate, useParams } from "react-router"
import { usePageTitle } from "@/shared/lib"
import { CampaignMessageTemplateDetails } from "@/widgets/campaignMessageTemplate"
import { PATH } from "@/shared/config"

const EmailTemplateDetails = () => {
  usePageTitle("Email Template")
  const navigate = useNavigate()

  const { campaignId, templateId } = useParams<{
    campaignId: string
    templateId: string
  }>()

  const onBack = () => {
    navigate(PATH.withAuth.campaign.emailTemplates.list.url(campaignId!))
  }
  return (
    <CampaignMessageTemplateDetails
      type="email"
      campaignId={campaignId!}
      templateId={templateId!}
      onBack={onBack}
      backTitle="Email templates"
    />
  )
}
export default EmailTemplateDetails
