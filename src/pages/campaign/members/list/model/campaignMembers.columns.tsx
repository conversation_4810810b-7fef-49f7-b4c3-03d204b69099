import {
  CampaignMemberData,
  CampaignMemberStatus,
} from "@/entities/campaignMember"
import { capitalizeString } from "@/shared/lib"
import { campaignRoleIdToCampaignRole } from "@/shared/model"
import { ActionButton } from "@/shared/ui/ActionButton"
import { But<PERSON> } from "@/shared/ui/Button"
import { Typography } from "@mui/material"
import { GridRenderCellParams } from "@mui/x-data-grid"
interface GetColumnsProps {
  onEdit: (id: number) => void
  onDelete: (id: number, inviteId: number) => void
  onResend: (member: CampaignMemberData) => void
  onView: (id: number) => void
}

export const getColumns = ({
  onEdit,
  onDelete,
  onResend,
  onView,
}: GetColumnsProps) => {
  return [
    {
      field: "first_name",
      headerName: "First Name",
      minWidth: 150,
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "last_name",
      headerName: "Last Name",
      minWidth: 150,
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "email",
      headerName: "Email",
      minWidth: 250,
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "campaign_role_id",
      headerName: "Role",
      flex: 1,
      minWidth: 100,
      sortable: false,
      filterable: false,
      valueGetter: (roleId: number) => {
        return campaignRoleIdToCampaignRole[roleId] || "Unknown Role"
      },
    },
    {
      field: "group.name",
      headerName: "Group",
      minWidth: 150,
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        const group = params.row.group;
        if (group && typeof group === 'object' && !Array.isArray(group)) {
          return group.name;
        }
        return 'No group';
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      minWidth: 150,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div className="flex items-center h-full gap-2">
            <Typography variant="body2">
              {capitalizeString(params.row.status)}
            </Typography>
            {(params.row.status === CampaignMemberStatus.PENDING ||
              params.row.status === CampaignMemberStatus.EXPIRED) && (
              <Button
                variant="outlined"
                color="primary"
                className="!w-20"
                onClick={() => onResend(params.row)}
              >
                Resend
              </Button>
            )}
          </div>
        )
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        const isDisabled =
          params.row.status === CampaignMemberStatus.SENT ||
          params.row.status === CampaignMemberStatus.EXPIRED ||
          params.row.status === CampaignMemberStatus.PENDING

        const isDeleteDisabled =
          params.row.status === CampaignMemberStatus.SENT ||
          params.row.status === CampaignMemberStatus.EXPIRED

        return (
          <>
            <ActionButton
              isIcon
              typeAction="edit"
              onClick={() => onEdit(params.row.id)}
              disabled={isDisabled}
              title="Edit"
            />
            <ActionButton
              isIcon
              typeAction="delete"
              onClick={() => onDelete(params.row.id, params.row.invite_id)}
              disabled={isDeleteDisabled}
              title="Delete"
            />
            <ActionButton
              isIcon
              typeAction="view"
              onClick={() => onView(params.row.campaign_user_id)}
              disabled={isDisabled}
              title="Preview Donation Page"
            />
          </>
        )
      },
    },
  ]
}
