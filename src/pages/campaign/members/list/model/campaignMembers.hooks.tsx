import { v4 as uuidv4 } from "uuid"

import { CampaignMemberData } from "@/entities/campaignMember"
import { useMemo } from "react"
import { getColumns } from "./campaignMembers.columns.tsx"

interface UseCampaignMembersTableDataProps {
  onEdit: (id: number) => void
  onDelete: (id: number, inviteId: number) => void
  onView: (id: number) => void
  campaignMembers: CampaignMemberData[]
  onResend: (member: CampaignMemberData) => void
}

export const useCampaignMembersTableData = ({
  onEdit,
  onDelete,
  onView,
  campaignMembers,
  onResend,
}: UseCampaignMembersTableDataProps) => {
  const columns = useMemo(
    () => getColumns({ onEdit, onDelete, onResend, onView }),
    [onEdit, onDelete, onResend, onView]
  )

  const rows = useMemo(() => {
    return (
      campaignMembers?.map((member) => ({
        ...member,
        id: member.user_id ? member.user_id : uuidv4(),
      })) ?? []
    )
  }, [campaignMembers])

  return {
    columns,
    rows,
  }
}
