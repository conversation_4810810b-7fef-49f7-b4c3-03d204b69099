import { useSendInvite } from "@/entities/invite"
import { useCallback, useEffect } from "react"
import { toast } from "react-toastify"

import { useGetCampaignRoles } from "@/entities/campaignRole"
import { Button } from "@/shared/ui/Button"
import { Dialog } from "@/shared/ui/Dialog"
import { Input } from "@/shared/ui/Input"
import { Select } from "@/shared/ui/Select"
import { useCampaignInviteForm } from "./inviteModal.form.ts"

import { useQueryClient } from "@tanstack/react-query"
import { CampaignInviteFormData } from "./inviteModal.schema.ts"

interface InviteModalProps {
  open: boolean
  onClose: () => void
  campaignId: string
  initialValues?: CampaignInviteFormData
  isResend?: boolean
}

export const InviteModal = ({
  open,
  onClose,
  campaignId,
  initialValues,
  isResend = false,
}: InviteModalProps) => {
  const queryClient = useQueryClient()
  const { register, handleSubmit, errors, reset, setValue, control } =
    useCampaignInviteForm(initialValues)
  const { data: campaignRoles, isLoading: isLoadingRoles } =
    useGetCampaignRoles()

  useEffect(() => {
    if (initialValues) {
      setValue("campaignRole", initialValues.campaignRole)
      setValue("email", initialValues.email)
    }
  }, [initialValues, setValue])

  const handleCloseAndReset = useCallback(() => {
    reset()
    onClose()
  }, [reset, onClose])

  const { mutate: createInvite, isPending: isCreating } = useSendInvite({
    onSuccess: () => {
      // HACK: invalidate queries after 1 second to avoid showing "Sent" status
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["getCampaignMembers"] })
      }, 2000)
      toast.success(
        isResend
          ? "Invitation resent successfully"
          : "Invitation sent successfully"
      )
      handleCloseAndReset()
    },
    onError: (error) => {
      toast.error(error.response?.data?.message)
      handleCloseAndReset()
    },
  })

  const roleOptions =
    campaignRoles?.map((role) => ({
      label: role.name,
      value: role.campaign_role_id,
    })) || []

  const onSubmit = (formValues: CampaignInviteFormData) => {
    createInvite({
      campaign_id: parseInt(campaignId),
      campaign_role_id: parseInt(formValues.campaignRole),
      email: formValues.email,
    })
  }

  const isLoading = isLoadingRoles || isCreating

  return (
    <Dialog
      open={open}
      onClose={handleCloseAndReset}
      title="Invite Member"
      actions={
        <>
          <Button
            onClick={handleCloseAndReset}
            variant="outlined"
            fullWidth={false}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            fullWidth={false}
            disabled={isLoading}
          >
            {isResend ? "Resend Invitation" : "Send Invitation"}
          </Button>
        </>
      }
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="mb-2">
          <Select
            label="Campaign Role"
            name="campaignRole"
            control={control}
            options={roleOptions}
            error={!!errors.campaignRole}
            helperText={errors.campaignRole?.message}
            fullWidth
          />
        </div>
        <div className="mb-2">
          <Input
            label="Email"
            variant="outlined"
            autoComplete="email"
            {...register("email")}
            error={!!errors.email}
            helperText={errors.email?.message}
            fullWidth
            disabled={isResend}
          />
        </div>
      </form>
    </Dialog>
  )
}
