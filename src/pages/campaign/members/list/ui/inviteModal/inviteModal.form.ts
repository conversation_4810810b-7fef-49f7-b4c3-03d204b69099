import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"

import {
  CampaignInviteFormData,
  campaignInviteSchema,
} from "./inviteModal.schema.ts"

export const useCampaignInviteForm = (
  initialValues: CampaignInviteFormData = {
    campaignRole: "",
    email: "",
  }
) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    ...form
  } = useForm<CampaignInviteFormData>({
    resolver: yupResolver(campaignInviteSchema),
    defaultValues: initialValues,
  })

  return {
    register,
    handleSubmit,
    errors,
    setValue,
    ...form,
  }
}
