import { useState } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"

import { useGetCampaignById } from "@/entities/campaign"
import {
  CampaignMemberData,
  useDeleteCampaignMember,
  useGetCampaignMembers,
} from "@/entities/campaignMember"
import { useDeleteInvite } from "@/entities/invite"
import { PATH } from "@/shared/config"
import { usePagination } from "@/shared/model"
import { ConfirmDialog, Table } from "@/shared/ui"
import { ActionButton } from "@/shared/ui/ActionButton"
import { useCampaignMembersTableData } from "../model/campaignMembers.hooks.tsx"
import { CampaignInviteFormData } from "./inviteModal/inviteModal.schema.ts"
import { InviteModal } from "./inviteModal/inviteModal.tsx"

export const CampaignMembers = () => {
  const { campaignId } = useParams<{ campaignId: string }>()
  const navigate = useNavigate()

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedMember, setSelectedMember] =
    useState<CampaignMemberData | null>(null)
  const [inviteModalOpen, setInviteModalOpen] = useState(false)
  const [currentMember, setCurrentMember] = useState<
    CampaignInviteFormData | undefined
  >(undefined)
  const [resendInviteModalOpen, setResendInviteModalOpen] = useState(false)
  const [selectedInvite, setSelectedInvite] =
    useState<CampaignMemberData | null>(null)
  const { pageSize, page, onPaginationModelChange } = usePagination()

  const { data: campaignMembers, isLoading: isLoadingMembers } =
    useGetCampaignMembers(Number(campaignId), {
      page: page,
      per_page: pageSize,
    })

  const currentPage = campaignMembers?.current_page
    ? campaignMembers.current_page - 1
    : 0

  const totalPages = campaignMembers?.total

  const { data: campaignDetails } = useGetCampaignById(Number(campaignId))

  const { mutate: deleteMember, isPending: isDeleting } =
    useDeleteCampaignMember({
      onSuccess: () => {
        toast.success("Member deleted successfully")
      },
      onError: (error) => {
        toast.error(error.message)
      },
    })

  const { mutate: deleteInvite, isPending: isDeletingInvite } = useDeleteInvite(
    {
      onSuccess: () => {
        toast.success("Invite deleted successfully")
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }
  )

  const handleDelete = () => {
    if (selectedMember) {
      deleteMember({
        campaignId: Number(campaignId),
        userId: selectedMember.user_id,
      })
      setDeleteDialogOpen(false)
    }
    if (selectedInvite?.invite_id) {
      deleteInvite(selectedInvite.invite_id)
      setDeleteDialogOpen(false)
    }
  }

  const handleEdit = (id: number) => {
    navigate(PATH.withAuth.campaign.members.update.url(campaignId!, id))
  }

  const handleView = (id: number) => {
    const guid = campaignDetails?.guid
    const queryParams = new URLSearchParams()
    if (id) queryParams.set("campaign_user_id", id.toString())
    if (guid) {
      window.open(
        PATH.withoutAuth.donation.url({
          guid,
          queryParams: id ? queryParams : undefined,
        }),
        "_blank"
      )
    }
  }

  const handleDeleteClick = (id: number, inviteId: number) => {
    setDeleteDialogOpen(true)
    if (inviteId) {
      setSelectedInvite(
        campaignMembers?.data.find((member) => member.invite_id === inviteId) ??
          null
      )
      return
    }
    if (id) {
      setSelectedMember(
        campaignMembers?.data.find((member) => member.user_id === id) ?? null
      )
    }
  }

  const handleResendClick = (member: CampaignMemberData) => {
    setCurrentMember({
      campaignRole: member.campaign_role_id.toString(),
      email: member.email,
    })
    setResendInviteModalOpen(true)
  }

  const handleInviteClick = () => {
    setInviteModalOpen(true)
  }

  const { columns, rows } = useCampaignMembersTableData({
    onEdit: handleEdit,
    onDelete: handleDeleteClick,
    onResend: handleResendClick,
    onView: handleView,
    campaignMembers: campaignMembers?.data ?? [],
  })

  const isLoading = isDeleting || isLoadingMembers || isDeletingInvite

  return (
    <>
      <div className="flex justify-end mb-4">
        <ActionButton
          typeAction="create"
          onClick={handleInviteClick}
          fullWidth={false}
          variant="contained"
        >
          Invite
        </ActionButton>
      </div>
      <Table
        columns={columns}
        rows={rows}
        rowCount={totalPages || 0}
        loading={isLoading}
        onPaginationModelChange={onPaginationModelChange}
        page={currentPage}
        pageSize={pageSize}
      />
      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Member"
        content={`Are you sure you want to delete ${selectedMember?.first_name ?? ""} ${selectedMember?.last_name ?? ""} from this campaign?`}
      />
      <InviteModal
        open={inviteModalOpen}
        onClose={() => setInviteModalOpen(false)}
        campaignId={campaignId as string}
      />
      <InviteModal
        open={resendInviteModalOpen}
        onClose={() => setResendInviteModalOpen(false)}
        campaignId={campaignId as string}
        initialValues={currentMember}
        isResend={true}
      />
    </>
  )
}

export default CampaignMembers
