import {
  phoneRegExp,
  nameValida<PERSON>,
} from "@/shared/config"
import * as yup from "yup"

export const updateCampaignMemberSchema = yup.object().shape({
  email: yup.string().email("Invalid email").required("Email is required"),
  first_name: nameValidator.name("First name").required("First name is required"),
  last_name: nameValidator.name("Last name").required("Last name is required"),
  phone: yup
    .string()
    .matches(phoneRegExp, "Please enter a valid US phone number")
    .required("Phone number is required"),
  campaign_role_id: yup.number().required("Campaign Role must not be empty"),
  group_id: yup.number().nullable().optional(),
})

export type UpdateCampaignMemberFormData = yup.InferType<typeof updateCampaignMemberSchema>