import { useForm } from "react-hook-form"
import { useEffect, useMemo } from "react"
import { yupResolver } from "@hookform/resolvers/yup"
import { UpdateCampaignMemberDto } from "@/entities/campaignMember"
import {
  updateCampaignMemberSchema,
  UpdateCampaignMemberFormData
} from "./updateCampaignMember.schema"

interface UseUpdateCampaignMemberFormProps {
  initialData?: UpdateCampaignMemberDto
}

export const useUpdateCampaignMemberForm = ({
  initialData
}: UseUpdateCampaignMemberFormProps) => {
  const defaultValues: UpdateCampaignMemberFormData = useMemo(() => ({
    first_name: initialData?.first_name || "",
    last_name: initialData?.last_name || "",
    email: initialData?.email || "",
    phone: initialData?.phone || "",
    campaign_role_id: initialData?.campaign_role_id || 4,
    group_id: initialData?.group_id || null,
  }), [initialData])

  const form = useForm<UpdateCampaignMemberFormData>({
    resolver: yupResolver(updateCampaignMemberSchema),
    defaultValues,
    mode: "onChange",
  })

  useEffect(() => {
    if (!form.formState.isDirty && initialData) {
      form.reset(defaultValues)
    }
  }, [form, initialData, defaultValues])

  return form
}