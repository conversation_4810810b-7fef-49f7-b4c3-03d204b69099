import {
  UpdateCampaignMemberDto,
  useGetCampaignMemberById,
  useUpdateCampaignMember,
} from "@/entities/campaignMember"
import {
  useDeleteCampaignUserImage,
  useGetCampaignUserImagesByType,
  useUpdateCampaignUserImage,
} from "@/entities/campaignMoment"
import { useGetCampaignRoles } from "@/entities/campaignRole"
import { PATH } from "@/shared/config"
import { CampaignImageType, Roles, useUserStore } from "@/shared/model"
import { Guard, Select, Wrapper } from "@/shared/ui"
import { CampaignImage } from "@/widgets/campaignImage"
import {
  Box,
  Button,
  CircularProgress,
  TextField,
  Typography,
} from "@mui/material"
import { useMemo } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"
import { useUpdateCampaignMemberForm } from "../model/updateCampaignMember.form"
import { useGetCampaignGroupsByCampaignId } from "@/entities/campaignGroup"

export const UpdateCampaignMember = () => {
  const navigate = useNavigate()
  const { campaignId, userId } = useParams<{
    campaignId: string
    userId: string
  }>()

  const { data: member, isLoading: isLoadingMember } = useGetCampaignMemberById(
    Number(campaignId),
    Number(userId)
  )
  const { userInfo } = useUserStore()
  const role = userInfo?.role
  const { data: campaignRoles, isLoading: isLoadingRoles } =
    useGetCampaignRoles()
  const roleOptions =
    campaignRoles?.map((role) => ({
      label: role.name,
      value: role.campaign_role_id,
    })) || []

  const { data: campaignGroups, isLoading: isLoadingGroups } =
    useGetCampaignGroupsByCampaignId(parseInt(campaignId!))
  const groupsOptions =
    campaignGroups?.map((group) => ({
      label: group.group_name,
      value: group.id,
    })) || []

  const initialData = useMemo(
    () => ({
      first_name: member?.first_name || "",
      last_name: member?.last_name || "",
      email: member?.email || "",
      phone: member?.phone || "",
      campaign_role_id: member?.campaign_role_id || 0,
      status: member?.status || "",
      user_id: member?.user_id || "",
      campaign_user_id: member?.campaign_user_id || "",
      campaign_id: member?.campaign_id || "",
      group_id: member?.group?.id || null,
    }),
    [member]
  )

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty },
  } = useUpdateCampaignMemberForm({
    initialData,
  })

  const { mutate: updateMember, isPending: isUpdating } =
    useUpdateCampaignMember({
      onSuccess: () => {
        toast.success("Member updated successfully")
        navigate(PATH.withAuth.campaign.members.list.url(campaignId!))
      },
      onError: (error) => {
        if (error.response?.data?.message) {
          toast.error(error.response.data.message)
        } else {
          toast.error(error.message)
        }
      },
    })

  const actions = useMemo(() => {
    return {
      useGetImage: useGetCampaignUserImagesByType,
      useUpdateImage: useUpdateCampaignUserImage,
      useDeleteImage: useDeleteCampaignUserImage,
    }
  }, [role])

  const onSubmit = (data: UpdateCampaignMemberDto) => {
    updateMember({
      campaignId: Number(campaignId),
      userId: Number(userId),
      data,
    })
  }

  const handleCancel = () => {
    navigate(PATH.withAuth.campaign.members.list.url(campaignId!))
  }

  const isLoading = isLoadingMember || isUpdating || isLoadingRoles || isLoadingGroups

  if (isLoadingMember) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  const onBackClick = () => {
    navigate(PATH.withAuth.campaign.members.list.url(campaignId!))
  }

  return (
    <Wrapper backTitle="Edit Member" onBack={onBackClick} isForm>
      {member ? (
        <form onSubmit={handleSubmit(onSubmit)}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            <TextField
              label="First Name"
              fullWidth
              {...register("first_name")}
              error={!!errors.first_name}
              helperText={errors.first_name?.message}
              disabled={isLoading}
              slotProps={{
                inputLabel: {
                  shrink: true,
                },
              }}
            />
            <TextField
              label="Last Name"
              fullWidth
              {...register("last_name")}
              error={!!errors.last_name}
              helperText={errors.last_name?.message}
              disabled={isLoading}
              slotProps={{
                inputLabel: {
                  shrink: true,
                },
              }}
            />
            <TextField
              label="Email"
              fullWidth
              {...register("email")}
              error={!!errors.email}
              helperText={errors.email?.message}
              disabled={isLoading}
              slotProps={{
                inputLabel: {
                  shrink: true,
                },
              }}
            />
            <TextField
              label="Phone"
              fullWidth
              {...register("phone")}
              error={!!errors.phone}
              helperText={errors.phone?.message}
              disabled={isLoading}
              slotProps={{
                inputLabel: {
                  shrink: true,
                },
              }}
            />
            {(userInfo?.userRoleId === 1 || userInfo?.userRoleId === 2) && (
              <div className="mb-2">
                <Select
                  name="campaign_role_id"
                  label="Campaign Role"
                  options={roleOptions}
                  error={!!errors.campaign_role_id}
                  helperText={errors.campaign_role_id?.message}
                  fullWidth
                  value={watch("campaign_role_id").toString()}
                  onChange={(e) => {
                    setValue("campaign_role_id", Number(e.target.value), {
                      shouldValidate: true,
                      shouldDirty: true,
                    })
                  }}
                />
              </div>
            )}
            {watch('campaign_role_id') === 4 && (
              <Select
                name="group_id"
                label="Campaign Group"
                options={groupsOptions}
                value={watch('group_id')?.toString() ?? ''}
                onChange={(event) =>
                  setValue('group_id', parseInt(event.target.value, 10), {
                    shouldValidate: true,
                    shouldDirty: true,
                  })
                }
                error={!!errors.group_id}
                helperText={errors.group_id?.message}
              />
            )}
            <Guard roles={[Roles.SuperAdmin, Roles.Admin]}>
              <div className="mt-6">
                <CampaignImage
                  {...actions}
                  circularCrop
                  type={CampaignImageType.portrait}
                  campaignUserIdProp={Number(member?.campaign_user_id)}
                />
              </div>
            </Guard>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                mt: 2,
              }}
            >
              <Button
                onClick={handleCancel}
                variant="outlined"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={isLoading || !isDirty}
                sx={{ minWidth: 120 }}
              >
                {isUpdating ? "Saving..." : "Save Changes"}
              </Button>
            </Box>
          </Box>
        </form>
      ) : (
        <Typography textAlign="center" color="error">
          Member not found
        </Typography>
      )}
    </Wrapper>
  )
}

export default UpdateCampaignMember
