import { GridRenderCellParams } from "@mui/x-data-grid"

export const columns = [
  {
    field: "campaign_user",
    headerName: "Campaign user full name",
    flex: 1,
    sortable: false,
    filterable: false,
  },
  {
    field: "donor_name",
    headerName: "Invited donor name",
    flex: 1,
    sortable: false,
    filterable: false,
  },
  {
    field: "channel",
    headerName: "Message channel",
    flex: 1,
    sortable: false,
    filterable: false,
    renderCell: (params: GridRenderCellParams) => {
      const value = params?.row?.channel || ""
      return <div className="capitalize">{value}</div>
    },
  },
  {
    field: "template",
    headerName: "Template name",
    flex: 1,
    sortable: false,
    filterable: false,
  },
  {
    field: "send_date",
    headerName: "Send date",
    flex: 1,
    sortable: false,
    filterable: false,
  },
  {
    field: "scheduled_at",
    headerName: "Scheduled",
    flex: 1,
    sortable: false,
    filterable: false,
  },
  {
    field: "sent",
    headerName: "Send",
    sortable: false,
    filterable: false,
    renderCell: (params: GridRenderCellParams) => {
      const value = params?.row?.sent || ""
      return (
        <div className="capitalize">
          {value ? "Yes" : "No"} {value}
        </div>
      )
    },
  },
]
