import { useGetCampaignMessageLog } from "@/entities/campaignMessageLog"
import { usePageTitle } from "@/shared/lib"
import { useGetCampaignId, usePagination } from "@/shared/model"
import { Table } from "@/shared/ui"
import {
  MessageLogsFilterComponent,
  MessageLogsReportForm,
} from "@/widgets/messageLogsFilters"
import { format } from "date-fns"
import { useMemo, useState } from "react"
import { v4 as uuidv4 } from "uuid"
import { columns } from "../model/campaignMessageLog.columns"

const CampaignMessageLog = () => {
  usePageTitle("Message Log")
  const campaignId = useGetCampaignId(true)
  const today = new Date()
  const thirtyDaysAgo = new Date(new Date().setDate(today.getDate() - 30))

  const [messageLogsFilter, setMessageLogsFilter] =
    useState<MessageLogsReportForm>({
      start_date: thirtyDaysAgo,
      end_date: today,
      message_status: {
        label: "sent",
        value: "sent",
      },
    })

  const { pageSize, page, onPaginationModelChange } = usePagination()

  const { data, isLoading } = useGetCampaignMessageLog(campaignId, {
    page,
    per_page: pageSize,
    start_date: format(messageLogsFilter.start_date, "yyyy-MM-dd"),
    end_date: format(messageLogsFilter.end_date, "yyyy-MM-dd"),
    message_status: messageLogsFilter.message_status?.value ?? "",
  })

  const currentPage = data?.meta?.current_page ? data.meta.current_page - 1 : 0

  const rows = useMemo(
    () => data?.data?.map((item) => ({ ...item, id: uuidv4() })) || [],
    [data]
  )

  const onSubmit = (data: MessageLogsReportForm) => {
    setMessageLogsFilter({
      start_date: data.start_date,
      end_date: data.end_date,
      message_status: data.message_status,
    })
  }

  const onReset = () => {
    setMessageLogsFilter({
      start_date: thirtyDaysAgo,
      end_date: today,
      message_status: {
        label: "sent",
        value: "sent",
      },
    })
  }

  return (
    <>
      <MessageLogsFilterComponent
        messageLogsFilter={messageLogsFilter}
        onSubmit={onSubmit}
        onReset={onReset}
      />
      <Table
        columns={columns}
        loading={isLoading}
        rows={rows}
        rowCount={data?.meta?.total || 0}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
      />
    </>
  )
}

export default CampaignMessageLog
