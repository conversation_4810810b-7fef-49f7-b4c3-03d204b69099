import {
  useDeleteCampaignImage,
  useDeleteCampaignUserImage,
  useGetCampaignImagesByType,
  useGetCampaignUserImagesByType,
  useUpdateCampaignImage,
  useUpdateCampaignUserImage,
} from "@/entities/campaignMoment"
import { CampaignImageType, Roles, useUserStore } from "@/shared/model"
import { Guard } from "@/shared/ui"
import { CampaignImage } from "@/widgets/campaignImage"
import { Typography } from "@mui/material"
import React, { useMemo } from "react"
import ActionsShots from "./campaignActionsShots/campaignActionsShots"

const CampaignMoments: React.FC = () => {
  const { userInfo } = useUserStore()

  const role = userInfo?.role

  const actions = useMemo(() => {
    if (role === Roles.Admin || role === Roles.SuperAdmin) {
      return {
        useGetImage: useGetCampaignImagesByType,
        useUpdateImage: useUpdateCampaignImage,
        useDeleteImage: useDeleteCampaignImage,
      }
    }

    return {
      useGetImage: useGetCampaignUserImagesByType,
      useUpdateImage: useUpdateCampaignUserImage,
      useDeleteImage: useDeleteCampaignUserImage,
    }
  }, [role])

  if (!role) {
    return (
      <Typography className="text-center p-6">You are not logged in</Typography>
    )
  }

  return (
    <div className="w-full pt-2 pb-6">
      <Guard roles={[Roles.User]}>
        <div className="mt-6">
          <CampaignImage
            circularCrop
            {...actions}
            type={CampaignImageType.portrait}
          />
        </div>
      </Guard>
      <Guard roles={[Roles.Admin, Roles.SuperAdmin]}>
        <div className="mt-6">
          <CampaignImage
            circularCrop
            {...actions}
            type={CampaignImageType.logo}
          />
        </div>
      </Guard>
      <Guard roles={[Roles.Admin, Roles.SuperAdmin]}>
        <div className="mt-6">
          <CampaignImage {...actions} type={CampaignImageType.hero} />
        </div>
      </Guard>
      <Guard roles={[Roles.Admin, Roles.SuperAdmin]}>
        <ActionsShots {...actions} />
      </Guard>
    </div>
  )
}

export default CampaignMoments
