import { Dialog, Icon<PERSON>utton } from "@/shared/ui"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import DeleteIcon from "@mui/icons-material/Delete"
import cn from "classnames"
import React, { useState } from "react"

interface Props {
  id: number
  src: string
  alt?: string
  isDeleting?: boolean
  onDelete?: (id: number) => void
}

const ActionsShotsView: React.FC<Props> = ({
  src,
  isDeleting,
  onDelete,
  id,
  alt = "",
}) => {
  const [open, setOpen] = useState(false)

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id })

  const onOpen = () => {
    setOpen(true)
  }

  const onClose = () => {
    setOpen(false)
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete?.(id)
  }

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    border: isDragging ? "2px dashed #999" : "1px solid #ccc",
    borderRadius: "6px",
    cursor: "grab",
    opacity: isDragging ? 0.6 : 1,
    objectFit: "cover",
  }

  return (
    <div ref={setNodeRef} {...attributes} {...listeners} style={style}>
      <div className="relative min-h-[100px] group">
        <div onClick={onOpen} className="cursor-pointer">
          <img
            src={src}
            alt={alt}
            className="w-80 h-80"
            {...attributes}
            {...listeners}
          />
          {!!onDelete && (
            <div
              className={cn(
                "group-hover:opacity-100 transition-opacity duration-200 p-1 right-0 bottom-0 bg-white/75 absolute z-10 w-full flex justify-end",
                {
                  "opacity-0": !isDeleting,
                }
              )}
            >
              <IconButton
                loading={isDeleting}
                onClick={handleDelete}
                title="Delete"
              >
                <DeleteIcon />
              </IconButton>
            </div>
          )}
        </div>
      </div>
      <Dialog maxWidth="lg" title="Image" open={open} onClose={onClose}>
        <img className="max-h-[80vh] mx-auto" src={src} alt={alt} />
      </Dialog>
    </div>
  )
}

export default ActionsShotsView
