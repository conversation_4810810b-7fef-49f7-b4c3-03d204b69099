import {
  useDeleteCampaignImage,
  useDeleteCampaignUserImage,
  useGetCampaignImagesByType,
  useGetCampaignUserImagesByType,
  useReorderCampaignImages,
  useUpdateCampaignImage,
  useUpdateCampaignUserImage,
} from "@/entities/campaignMoment"
import { asyncConfirm, Button, UploadImage } from "@/shared/ui"
import { CircularProgress, Typography } from "@mui/material"
import React, { useCallback, useEffect, useState } from "react"
import { toast } from "react-toastify"

import {
  CampaignImageType,
  CampaignImageTypeById,
  Helpers,
  useCampaignStore,
  useGetCampaignId,
} from "@/shared/model"
import {
  closestCenter,
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core"
import {
  arrayMove,
  rectSortingStrategy,
  SortableContext,
} from "@dnd-kit/sortable"
import "./campaignActionsShots.css"
import ActionsShotsView from "./campaignActionsShotsView"

interface Image {
  id: number
  image: string
  position: number,
  imageId: number
}

interface Props {
  useGetImage:
    | typeof useGetCampaignUserImagesByType
    | typeof useGetCampaignImagesByType
  useUpdateImage:
    | typeof useUpdateCampaignUserImage
    | typeof useUpdateCampaignImage
  useDeleteImage:
    | typeof useDeleteCampaignUserImage
    | typeof useDeleteCampaignImage
  maxCountImage?: number
}

const ActionsShots: React.FC<Props> = ({
  useGetImage,
  useUpdateImage,
  useDeleteImage,
  maxCountImage = 10,
}) => {
  const [images, setImages] = useState<Image[]>([])
  const [initialOrder, setInitialOrder] = useState<number[]>([])
  const [hasOrderChanged, setHasOrderChanged] = useState(false)

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 5 } })
  )

  const [deleteId, setDeleteId] = useState<number | null>(null)
  const campaignId = useGetCampaignId()!
  const { selectedCampaign } = useCampaignStore()
  const campaignUserId = Number(selectedCampaign?.campaign_user_id)
  const type = CampaignImageType.action_shot

  const { data: actionsMoments, isLoading: isLoadingActions } = useGetImage(
    CampaignImageType.action_shot,
    Number(campaignId),
    campaignUserId
  )

  const { mutate: reorderImages, isPending: isReordering } =
    useReorderCampaignImages()

  useEffect(() => {
    const processedImages =
      actionsMoments?.map((item) => ({
        id: item.id,
        image: item?.image?.s3_url,
        position: item.position,
        imageId: item.image_id
      })) || []
    setImages(processedImages)
    setInitialOrder(processedImages.map((img) => img.id))
    setHasOrderChanged(false)
  }, [actionsMoments])

  const { mutate: updateActions, isPending: isUpdatingActions } =
    useUpdateImage({
      onSuccess: () => {
        toast.success("Action shot added successfully")
      },
      onError: () => {
        toast.error("Failed to update action shot")
      },
    })

  const { mutate: deleteImage, isPending: isDeleting } = useDeleteImage({
    onSuccess: () => {
      toast.success("Image was deleted successfully")
    },
    onError: () => {
      toast.error("Failed to delete image")
    },
  })

  const onUploadActions = (image: Blob) => {
    const data = Helpers.objectToFormData({
      image,
      image_type_id: CampaignImageTypeById[type],
    })

    updateActions({
      campaignId: Number(campaignId),
      campaignUserId,
      data,
      type,
    })
  }

  const onDelete = useCallback(
    async (imageId: number) => {
      const confirm = await asyncConfirm({
        title: "Delete image",
        message: "Are you sure you want to delete this image?",
      })

      if (!confirm) return

      setDeleteId(imageId)

      deleteImage({
        campaignId: Number(campaignId),
        campaignUserId,
        imageId,
        type,
      })
    },
    [campaignId, campaignUserId, type, deleteImage]
  )

  const handleDragEnd = (event: any) => {
    const { active, over } = event
    if (!over || active.id === over.id) return

    const oldIndex = images.findIndex((img) => img.id === active.id)
    const newIndex = images.findIndex((img) => img.id === over.id)

    const newOrder = arrayMove(images, oldIndex, newIndex).map(
      (img, index) => ({
        ...img,
        position: index,
      })
    )

    setImages(newOrder)
    const currentOrder = newOrder.map((img) => img.id)
    setHasOrderChanged(
      !currentOrder.every((id, index) => id === initialOrder[index])
    )
  }

  const handleReorder = () => {
    reorderImages(
      {
        campaignId: Number(campaignId),
        data: {
          images: images.map((img) => ({ id: img.id, position: img.position })),
        },
      },
      {
        onSuccess: () => {
          toast.success("Action shots reordered successfully")
          setInitialOrder(images.map((img) => img.id))
          setHasOrderChanged(false)
        },
        onError: () => {
          toast.error("Failed to reorder action shots")
        },
      }
    )
  }
  return (
    <>
      <div className="mt-6 mb-6 flex gap-4 flex-wrap items-center  justify-between">
        <Typography variant="h5">Moments (Action shots)</Typography>
        <div className="flex gap-4">
          <UploadImage
            renderPhoto={false}
            textButton="Upload new action shot"
            isUploading={isUpdatingActions}
            onSubmit={onUploadActions}
            disabled={images.length >= maxCountImage}
          />
          <div className="mt-4">
            <Button
              onClick={handleReorder}
              disabled={isReordering || !hasOrderChanged}
              fullWidth={false}
            >
              Save order
            </Button>
          </div>
        </div>
      </div>
      {(isLoadingActions && (
        <div className="flex items-center justify-center py-10 px-2">
          <CircularProgress size={48} />
        </div>
      )) ||
        (!images?.length && (
          <Typography align="center" variant="h5" className="py-10 px-2">
            No moments
          </Typography>
        )) || (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={images.map((img) => img.id)}
              strategy={rectSortingStrategy}
            >
              <div className="flex flex-wrap gap-4">
                {images.map((slide) => (
                  <ActionsShotsView
                    id={slide.id}
                    alt={slide.position.toString()}
                    src={slide.image}
                    isDeleting={deleteId === slide.imageId && isDeleting}
                    onDelete={() => onDelete(slide.imageId)}
                    key={slide.id}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
    </>
  )
}

export default ActionsShots
