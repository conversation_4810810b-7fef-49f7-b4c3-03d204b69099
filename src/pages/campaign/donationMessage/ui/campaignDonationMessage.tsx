import { Button } from "@/shared/ui/Button"
import { RichTextEditor } from "@/shared/ui/richTextEditor"
import { Typography } from "@mui/material"
import React, { useEffect, useState } from "react"
import { useDonationMessageApi } from "../model/useDonationMessageApi.hook"

const CampaignDonationMessage: React.FC = () => {
  const [content, setContent] = useState("")
  const { campaignDonationMessage, updateCampaignDonationMessage } =
    useDonationMessageApi()

  useEffect(() => {
    setContent(campaignDonationMessage?.message || "")
  }, [campaignDonationMessage])

  const handleChange = (content: string) => {
    setContent(content)
  }

  const handleSubmit = () => {
    updateCampaignDonationMessage({ message: content })
  }

  const isDisabled = content === campaignDonationMessage?.message

  return (
    <div className="w-full py-6">
      <div className="flex mb-6">
        <Typography variant="h2" className="!text-3xl !font-bold">
          Participant Donation Message
        </Typography>
      </div>
      <div className="w-full mb-6">
        <RichTextEditor
          initialContent={campaignDonationMessage?.message}
          onChange={handleChange}
        />
      </div>
      <div className="flex w-full justify-end">
        <Button
          type="submit"
          size="large"
          fullWidth={false}
          onClick={handleSubmit}
          disabled={isDisabled}
        >
          Save
        </Button>
      </div>
    </div>
  )
}

export default CampaignDonationMessage
