import {
  useGetCampaignDonationMessage,
  useUpdateCampaignDonationMessage,
} from "@/entities/campaignDonationMessage"
import { useGetUserCampaigns } from "@/entities/userCampaign"
import { useParams } from "react-router-dom"

export const useDonationMessageApi = () => {
  const campaignId = useParams().campaignId
  const { data: userCampaigns } = useGetUserCampaigns()

  const campaignUserId = userCampaigns?.find(
    (campaign) => campaign.campaign_id === Number(campaignId)
  )?.campaign_user_id

  const { data: campaignDonationMessage } = useGetCampaignDonationMessage(
    Number(campaignId),
    campaignUserId!,
    {
      enabled: Boolean(campaignId && campaignUserId),
    }
  )

  const { mutate: updateCampaignDonationMessage } =
    useUpdateCampaignDonationMessage(Number(campaignId), campaignUserId!)

  return {
    campaignDonationMessage,
    updateCampaignDonationMessage,
  }
}
