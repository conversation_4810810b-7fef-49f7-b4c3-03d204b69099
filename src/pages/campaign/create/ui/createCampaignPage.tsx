import { CreateCampaignDto, useCreateCampaign } from "@/entities/campaign"
import { useGetCampaignData } from "@/features/getCampaignSelectors"
import { PATH } from "@/shared/config"
import { DashboardLayout, ListLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib"
import { Autocomplete, Button, Checkbox, ColorPicker, Input } from "@/shared/ui"
import { DatePickerField } from "@/shared/ui/DatePickerField"
import { format } from "date-fns"
import { useNavigate } from "react-router-dom"
import { toast } from "react-toastify"
import { useCreateCampaignForm } from "../model/createCampaign.form"
import { CreateCampaignFormData } from "../model/createCampaign.schema"
import { TooltipIcon } from "@/shared/ui/tooltipIcon"

const CreateCampaignPage = () => {
  usePageTitle("Create Campaign")
  const navigate = useNavigate()
  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
  } = useCreateCampaignForm()

  const { mutate: createCampaign, isPending: isCreatingCampaign } =
    useCreateCampaign({
      onSuccess: () => {
        navigate(PATH.withAuth.campaign.list)
        toast.success("Campaign created successfully")
      },
      onError: () => {
        toast.error("Failed to create campaign")
      },
    })

  const onSubmit = (data: CreateCampaignFormData) => {
    const campaign: CreateCampaignDto = {
      name: data.name,
      level_id: parseInt(data.levelId.value),
      gender_id: parseInt(data.genderId.value),
      sport_id: parseInt(data.sportId.value),
      school_org_name: data.schoolOrgName,
      mascot: data.mascot,
      fundraising_goal: data.fundraisingGoal,
      auto_increase_goal: data.autoIncreaseGoal!,
      primary_color: data.primaryColor,
      secondary_color: data.secondaryColor,
      donation_page_title: data.donation_page_title,
      status_id: 2,
      city: data.city,
      state: data.state.value,
      zip: data.zip,
      campaign_start_date: format(data.campaignStartDate, "yyyy-MM-dd"),
      campaign_end_date: format(data.campaignEndDate, "yyyy-MM-dd"),
      campaign_owner_id: parseInt(data.campaign_owner_id.value),
      show_leaderboard: !!data.show_leaderboard,
    }

    if (data.causeId?.value) {
      campaign.cause_id = parseInt(data.causeId.value)
    }

    if (data.rosterSize !== undefined) {
      campaign.roster_size = data.rosterSize!
    }

    createCampaign(campaign)
  }

  const {
    levelsOptions,
    gendersOptions,
    sportsOptions,
    causesOptions,
    stateOptions,
    isLoadingLevels,
    isLoadingCauses,
    isLoadingGenders,
    isLoadingSports,
    salesPersonsOptions,
    isLoadingSalesPersons,
  } = useGetCampaignData()

  const onBackClick = () => {
    navigate(PATH.withAuth.campaign.list)
  }

  return (
    <DashboardLayout title="Create Campaign" isLoading={isCreatingCampaign}>
      <ListLayout title="Create Campaign" onBackClick={onBackClick}>
        <div className="w-full bg-white rounded p-6 mx-auto max-w-2xl">
          <form onSubmit={handleSubmit(onSubmit)} className="max-w-2xl mx-auto">
            <div className="space-y-4">
              <Input
                label="Campaign Name"
                {...register("name")}
                error={!!errors?.name}
                helperText={errors?.name?.message}
                variant="outlined"
                required
              />

              <Input
                label="Donation Page Title"
                {...register("donation_page_title")}
                error={!!errors?.donation_page_title}
                helperText={errors?.donation_page_title?.message}
                variant="outlined"
                required
              />

              <Autocomplete
                name="campaign_owner_id"
                label="Campaign Owner"
                options={salesPersonsOptions}
                control={control}
                error={!!errors?.campaign_owner_id}
                helperText={errors?.campaign_owner_id?.message}
                isLoading={isLoadingSalesPersons}
                required
              />

              <Autocomplete
                name="levelId"
                label="Level"
                options={levelsOptions}
                control={control}
                error={!!errors?.levelId}
                helperText={errors?.levelId?.message}
                isLoading={isLoadingLevels}
                required
              />

              <Autocomplete
                name="genderId"
                label="Gender"
                options={gendersOptions}
                control={control}
                error={!!errors?.genderId}
                helperText={errors?.genderId?.message}
                isLoading={isLoadingGenders}
                required
              />

              <Autocomplete
                name="sportId"
                label="Sport"
                options={sportsOptions}
                control={control}
                error={!!errors?.sportId}
                helperText={errors?.sportId?.message}
                isLoading={isLoadingSports}
                required
              />

              <Input
                label="School/Organization Name"
                {...register("schoolOrgName")}
                error={!!errors?.schoolOrgName}
                helperText={errors?.schoolOrgName?.message}
                variant="outlined"
                required
              />

              <Input
                label="Mascot"
                {...register("mascot")}
                error={!!errors?.mascot}
                helperText={errors?.mascot?.message}
                variant="outlined"
              />
              <Input
                label="City"
                {...register("city")}
                error={!!errors?.city}
                helperText={errors?.city?.message}
                variant="outlined"
                required
              />
              <Autocomplete
                name="state"
                label="State"
                options={stateOptions}
                control={control}
                error={!!errors.state}
                helperText={errors.state?.message}
                required
              />
              <Input
                label="ZIP Code"
                type="number"
                {...register("zip")}
                error={!!errors?.zip}
                helperText={errors?.zip?.message}
                variant="outlined"
                required
              />

              <div className="grid grid-cols-2 gap-4">
                <div className="col-span-1">
                  <DatePickerField
                    name="campaignStartDate"
                    label="Campaign Start Date"
                    control={control}
                    error={errors?.campaignStartDate}
                    required
                  />
                </div>
                <div className="col-span-1">
                  <DatePickerField
                    name="campaignEndDate"
                    label="Campaign End Date"
                    control={control}
                    error={errors?.campaignEndDate}
                    required
                  />
                </div>
              </div>

              <Input
                label="Fundraising Goal"
                type="number"
                {...register("fundraisingGoal")}
                error={!!errors?.fundraisingGoal}
                helperText={errors?.fundraisingGoal?.message}
                variant="outlined"
                required
              />

              <Autocomplete
                name="causeId"
                label="Cause"
                options={causesOptions}
                control={control}
                error={!!errors?.causeId}
                helperText={errors?.causeId?.message}
                isLoading={isLoadingCauses}
              />

              <Input
                label="Roster Size"
                type="number"
                {...register("rosterSize")}
                error={!!errors?.rosterSize}
                helperText={errors?.rosterSize?.message}
                variant="outlined"
              />

              <div className="mb-4 flex items-center space-x-1">
                <Checkbox
                  name="autoIncreaseGoal"
                  label="Auto Increase Goal"
                  control={control}
                />
                <TooltipIcon
                  text="The goal increases by 25% when 90% of base donations (excluding tips) is reached. Checked daily."
                />
              </div>
              <div className="mb-6">
                <Checkbox
                  name="show_leaderboard"
                  label="Show Leaderboard"
                  control={control}
                />
              </div>
              <div className="mb-4">
                <ColorPicker
                  name="primaryColor"
                  label="Primary Color"
                  control={control}
                  error={!!errors?.primaryColor}
                  required
                />
              </div>
              <div className="mb-4">
                <ColorPicker
                  name="secondaryColor"
                  label="Secondary Color"
                  control={control}
                  error={!!errors?.secondaryColor}
                  required
                />
              </div>
              <Button type="submit" variant="contained" fullWidth>
                Create Campaign
              </Button>
            </div>
          </form>
        </div>
      </ListLayout>
    </DashboardLayout>
  )
}

export default CreateCampaignPage
