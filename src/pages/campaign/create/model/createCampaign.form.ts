import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  CreateCampaignFormData,
  createCampaignSchema,
} from "./createCampaign.schema"

const today = new Date()
const thirtyDaysLater = new Date()
thirtyDaysLater.setDate(today.getDate() + 30)

export const useCreateCampaignForm = () => {
  return useForm<CreateCampaignFormData>({
    resolver: yupResolver(createCampaignSchema),
    defaultValues: {
      autoIncreaseGoal: false,
      rosterSize: null,
      campaignStartDate: today,
      campaignEndDate: thirtyDaysLater,
      campaign_owner_id: undefined,
      show_leaderboard: true,
    },
  })
}
