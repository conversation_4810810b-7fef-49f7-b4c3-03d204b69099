import { emptyFieldMessage, emptyFieldRegExp } from "@/shared/config"
import * as yup from "yup"

export const createCampaignSchema = yup.object().shape({
  name: yup
    .string()
    .required("Name is required")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  campaign_owner_id: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .required("Campaign owner is required"),
  donation_page_title: yup
    .string()
    .transform((value) => value?.trim())
    .min(2, "Donation page title must be at least 2 characters")
    .max(40, "Donation page title cannot exceed 40 characters")
    .required("Donation page title is required")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  levelId: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .required("Level is required"),
  genderId: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .required("Gender is required"),
  sportId: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .required("Sport is required"),
  schoolOrgName: yup
    .string()
    .required("School/Organization name is required")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  mascot: yup.string(),
  fundraisingGoal: yup
    .number()
    .typeError("Fundraising goal must be a number")
    .required("Fundraising goal is required")
    .positive("Fundraising goal must be positive"),
  causeId: yup
    .object()
    .shape({
      value: yup.string(),
      label: yup.string(),
    })
    .nullable()
    .optional(),
  rosterSize: yup
    .number()
    .transform((value, originalValue) => (originalValue === "" ? null : value))
    .typeError("Roster size must be a number")
    .positive("Roster size must be positive")
    .nullable()
    .optional(),
  autoIncreaseGoal: yup.boolean(),
  show_leaderboard: yup.boolean(),
  primaryColor: yup.string().required("Primary color is required"),
  secondaryColor: yup.string().required("Secondary color is required"),
  city: yup
    .string()
    .required("City is required")
    .matches(emptyFieldRegExp, emptyFieldMessage),
  state: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .required("State is required"),
  zip: yup.string().required("ZIP code is required"),
  campaignStartDate: yup.date().required("Start date is required"),
  campaignEndDate: yup
    .date()
    .required("End date is required")
    .min(yup.ref("campaignStartDate"), "End date must be after start date"),
})

export type CreateCampaignFormData = yup.InferType<typeof createCampaignSchema>
