import { useMemo } from "react"
import { getColumns } from "./donationInvite.columns"
import { DonationInviteDto } from "@/entities/donationInvite"
import { RelationshipDto } from "@/entities/relationship"

interface UseDonationInviteListTableDataProps {
  onDelete: (id: number) => void
  onEdit: (id: number) => void
  invites: DonationInviteDto[]
  relationships: RelationshipDto[]
}

export const useDonationInviteListTableData = ({
  onDelete,
  onEdit,
  invites,
  relationships,
}: UseDonationInviteListTableDataProps) => {
  const columns = useMemo(
    () => getColumns({ onDelete, onEdit }),
    [onDelete, onEdit]
  )

  const rows = useMemo(
    () =>
      invites.map((invite) => {
        const relationship = relationships.find(
          (rel) => rel.relationships_id === invite.relationship_id
        )

        return {
          ...invite,
          id: invite.id,
          relationship_name: relationship?.name || "Unknown",
        }
      }),
    [invites, relationships]
  )

  return {
    columns,
    rows,
  }
}
