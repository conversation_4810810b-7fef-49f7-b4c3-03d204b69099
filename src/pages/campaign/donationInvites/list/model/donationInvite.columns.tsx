import { ActionButton } from "@/shared/ui/ActionButton"
import { GridRenderCellParams } from "@mui/x-data-grid"

interface GetColumnsProps {
  onDelete: (id: number) => void
  onEdit: (id: number) => void
}

export const getColumns = ({ onDelete, onEdit }: GetColumnsProps) => {
  return [
    {
      field: "id",
      headerName: "ID",
      flex: 1,
      minWidth: 70,
      sortable: false,
      filterable: false,
    },
    {
      field: "who_invited_first_name",
      headerName: "Source",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        const {
          who_invited_first_name,
          who_invited_last_name,
          who_invited_role,
        } = params.row
        return (
          <span className="h-full">
            {`${who_invited_first_name} ${who_invited_last_name} (${who_invited_role})`}
          </span>
        )
      },
    },
    {
      field: "first_name",
      headerName: "First Name",
      flex: 1,
      minWidth: 200,
      sortable: false,
      filterable: false,
    },
    {
      field: "last_name",
      headerName: "Last Name",
      flex: 1,
      minWidth: 200,
      sortable: false,
      filterable: false,
    },
    {
      field: "relationship_name",
      headerName: "Relationship",
      flex: 1,
      minWidth: 150,
      sortable: false,
      filterable: false,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
    },
    {
      field: "phone",
      headerName: "Phone",
      flex: 1,
      minWidth: 130,
      sortable: false,
      filterable: false,
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <>
          <ActionButton
            isIcon
            typeAction="edit"
            onClick={() => onEdit(params.row.id)}
            title="Edit"
          />
          <ActionButton
            isIcon
            typeAction="delete"
            onClick={() => onDelete(params.row.id)}
            size="small"
            title="Delete"
          />
        </>
      ),
    },
  ]
}
