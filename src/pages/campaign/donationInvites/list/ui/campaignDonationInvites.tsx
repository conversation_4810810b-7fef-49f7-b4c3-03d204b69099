import { useMemo, useState } from "react"
import { Outlet, useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"

import {
  useDeleteDonationInvite,
  useGetDonationInvitesByCampaignId,
} from "@/entities/donationInvite"
import { useGetRelationships } from "@/entities/relationship"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { usePagination, useUserStore } from "@/shared/model"
import { Button, ConfirmDialog, Table } from "@/shared/ui"
import { useDonationInviteListTableData } from "../model/donationInvite.hooks"

const DonationInviteList = () => {
  usePageTitle("Donation Invites")
  const navigate = useNavigate()
  const { pageSize, page, onPaginationModelChange } = usePagination()

  const { campaignId } = useParams<{ campaignId: string }>()
  const numericCampaignId = Number(campaignId)
  const [inviteToDelete, setInviteToDelete] = useState<{
    id: number
    first_name: string
    last_name: string
  } | null>(null)
  const { userInfo } = useUserStore()

  const { data: invitesData, isLoading } = useGetDonationInvitesByCampaignId(
    numericCampaignId,
    {
      page,
      per_page: pageSize,
    }
  )

  const invites = useMemo(() => {
    return invitesData?.data ?? []
  }, [invitesData])

  const currentPage = invitesData?.meta.current_page
    ? invitesData.meta.current_page - 1
    : 0
  const totalPages = invitesData?.meta.total ? invitesData.meta.total : 0

  const { data: relationships, isLoading: isLoadingRelationships } =
    useGetRelationships()

  const { mutate: deleteInviteMutation, isPending: isDeleting } =
    useDeleteDonationInvite(numericCampaignId, {
      onSuccess: () => {
        toast.success("Invite deleted successfully")
        setInviteToDelete(null)
      },
      onError: () => {
        toast.error("Failed to delete invite")
      },
    })

  const handleCreateInvite = () => {
    navigate(PATH.withAuth.campaign.donationInvite.create.url(campaignId || ""))
  }

  const handleEditInvite = (inviteId: number) => {
    navigate(
      PATH.withAuth.campaign.donationInvite.update.url(campaignId!, inviteId)
    )
  }

  const handleDeleteInvite = () => {
    if (inviteToDelete) {
      deleteInviteMutation(inviteToDelete.id)
    }
  }

  const { columns, rows } = useDonationInviteListTableData({
    onDelete: (id: number) => {
      const invite = invites?.find((inv) => inv.id === id)
      if (invite) {
        setInviteToDelete({
          id: invite.id,
          first_name: invite.first_name,
          last_name: invite.last_name,
        })
      }
    },
    onEdit: handleEditInvite,
    invites: invites ?? [],
    relationships: relationships ?? [],
  })

  return (
    <>
      {userInfo?.userRoleId === 3 && (
        <div className="flex justify-end mb-4">
          <Button
            onClick={handleCreateInvite}
            fullWidth={false}
            variant="contained"
          >
            Invite
          </Button>
        </div>
      )}
      <Table
        columns={columns}
        rows={rows}
        rowCount={totalPages}
        loading={isLoading || isDeleting || isLoadingRelationships}
        height={1000}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
      />
      <ConfirmDialog
        open={!!inviteToDelete}
        onClose={() => setInviteToDelete(null)}
        onConfirm={handleDeleteInvite}
        title="Delete Invite"
        content={
          inviteToDelete
            ? `Are you sure you want to delete invite to ${inviteToDelete.first_name} ${inviteToDelete.last_name}?`
            : ""
        }
      />
      <Outlet />
    </>
  )
}

export default DonationInviteList
