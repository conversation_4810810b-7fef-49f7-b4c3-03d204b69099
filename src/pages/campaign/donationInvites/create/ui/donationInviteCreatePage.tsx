import { useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"

import {
  CreateDonationInviteDto,
  useAddDonationInvite,
} from "@/entities/donationInvite"
import { useGetRelationships } from "@/entities/relationship"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { useCampaignStore } from "@/shared/model"
import { Autocomplete, Button, Input, Wrapper } from "@/shared/ui"
import { useEffect } from "react"
import { useCreateDonationInviteForm } from "../model/createDonationInvite.form"
import { CreateDonationInviteFormData } from "../model/createDonationInvite.schema"

const DonationCreatePage = () => {
  usePageTitle("Create Campaign")
  const navigate = useNavigate()
  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    clearErrors,
    watch,
  } = useCreateDonationInviteForm()
  const { campaignId } = useParams<{ campaignId: string }>()
  const numericCampaignId = Number(campaignId)
  const { selectedCampaign } = useCampaignStore()

  const email = watch("email")
  const phone = watch("phone")

  useEffect(() => {
    if (email) {
      clearErrors("phone")
    }
    if (phone) {
      clearErrors("email")
    }
  }, [email, phone])

  const { mutate: createDonationInvite } = useAddDonationInvite(
    numericCampaignId,
    {
      onSuccess: () => {
        navigate(PATH.withAuth.campaign.donationInvite.list.url(campaignId!))

        toast.success("Donation invite created successfully")
      },
      onError: () => {
        toast.error("Failed to create donation invite")
      },
    }
  )

  const { data: relationships, isLoading: isRelationshipLoading } =
    useGetRelationships()

  const relationshipOptions =
    relationships?.map((relationship) => ({
      value: relationship.relationships_id,
      label: relationship.name,
    })) ?? []

  const onSubmit = (data: CreateDonationInviteFormData) => {
    const payload: CreateDonationInviteDto = {
      campaign_id: Number(campaignId),
      campaign_user_id: selectedCampaign?.campaign_user_id as number,
      relationship_id: Number(data.relationshipId.value),
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email ?? "",
      phone: data.phone ?? "",
      has_donated: false,
      invited_date: new Date().toISOString(),
    }
    createDonationInvite(payload)
  }

  const onBackClick = () => {
    navigate(PATH.withAuth.campaign.donationInvite.list.url(campaignId!))
  }

  return (
    <Wrapper backTitle="Donation Invites" onBack={onBackClick} isForm>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="container mx-auto p-6 max-w-2xl"
      >
        <div className="space-y-4">
          <Input
            label="First Name"
            {...register("first_name")}
            error={!!errors?.first_name}
            helperText={errors?.first_name?.message}
            variant="outlined"
            required
          />

          <Input
            label="Last Name"
            {...register("last_name")}
            error={!!errors?.last_name}
            helperText={errors?.last_name?.message}
            variant="outlined"
            required
          />

          <Input
            label="Phone"
            {...register("phone")}
            error={!!errors?.phone}
            helperText={errors?.phone?.message}
            variant="outlined"
            placeholder="(*************"
            type="phone"
          />

          <Input
            label="Email"
            {...register("email")}
            error={!!errors?.email}
            helperText={errors?.email?.message}
            variant="outlined"
            type="email"
          />

          <Autocomplete
            name="relationshipId"
            label="Relationship"
            options={relationshipOptions}
            control={control}
            error={!!errors?.relationshipId}
            helperText={errors?.relationshipId?.message}
            isLoading={isRelationshipLoading}
            required
          />

          <Button type="submit" variant="contained" fullWidth>
            Create Donation Invite
          </Button>
        </div>
      </form>
    </Wrapper>
  )
}

export default DonationCreatePage
