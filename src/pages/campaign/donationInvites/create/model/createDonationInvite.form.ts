import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import {
  CreateDonationInviteFormData,
  createDonationInviteSchema,
} from "./createDonationInvite.schema"

export const useCreateDonationInviteForm = () => {
  return useForm<CreateDonationInviteFormData>({
    resolver: yupResolver(createDonationInviteSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      relationshipId: undefined,
    },
  })
}
