import { DonationInviteDto } from "@/entities/donationInvite"
import { yupResolver } from "@hookform/resolvers/yup"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import {
  UpdateDonationInviteFormData,
  updateDonationInviteSchema,
} from "./updateDonationInvite.schema"

export const useUpdateDonationInviteForm = (
  donationInvite?: DonationInviteDto
) => {
  const methods = useForm<UpdateDonationInviteFormData>({
    resolver: yupResolver(updateDonationInviteSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      relationshipId: undefined,
    },
  })

  useEffect(() => {
    if (donationInvite) {
      methods.reset({
        first_name: donationInvite.first_name,
        last_name: donationInvite.last_name,
        email: donationInvite.email ?? "",
        phone: donationInvite.phone ?? "",
        relationshipId: undefined, // This will be set in the component
      })
    }
  }, [donationInvite, methods])

  return methods
}
