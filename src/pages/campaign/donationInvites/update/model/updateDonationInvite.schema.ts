import { emailOrPhoneValidator, nameValida<PERSON> } from "@/shared/config"
import * as yup from "yup"

export const updateDonationInviteSchema = yup.object().shape({
  first_name: nameValidator
    .name("First name")
    .required("First name is required"),
  last_name: nameValidator.name("Last name").required("Last name is required"),
  email: emailOrPhoneValidator.email(),
  phone: emailOrPhoneValidator.phone(),
  relationshipId: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .required("Relationship is required"),
})

export type UpdateDonationInviteFormData = yup.InferType<
  typeof updateDonationInviteSchema
>
