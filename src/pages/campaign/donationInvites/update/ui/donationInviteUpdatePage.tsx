import ArrowBackIcon from "@mui/icons-material/ArrowBack"
import { CircularProgress, Typography } from "@mui/material"
import { useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"

import {
  DonationInviteDto,
  useGetDonationInviteById,
  useUpdateDonationInvite,
} from "@/entities/donationInvite"
import { useGetRelationships } from "@/entities/relationship"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { Autocomplete, Button, Input } from "@/shared/ui"
import { useEffect } from "react"
import { useUpdateDonationInviteForm } from "../model/updateDonationInvite.form"
import { UpdateDonationInviteFormData } from "../model/updateDonationInvite.schema"

const DonationUpdatePage = () => {
  usePageTitle("Update Donation Invite")
  const navigate = useNavigate()
  const { campaignId, id } = useParams<{ campaignId: string; id: string }>()
  const numericCampaignId = Number(campaignId)
  const numericId = Number(id)

  const { data: donationInvite, isLoading: isLoadingInvite } =
    useGetDonationInviteById(numericCampaignId, numericId)

  const {
    control,
    register,
    handleSubmit,
    setValue,
    watch,
    clearErrors,
    formState: { errors, isDirty },
  } = useUpdateDonationInviteForm(donationInvite)

  const email = watch("email")
  const phone = watch("phone")

  useEffect(() => {
    if (email) {
      clearErrors("phone")
    }
    if (phone) {
      clearErrors("email")
    }
  }, [email, phone])

  const { mutate: updateDonationInvite, isPending: isUpdating } =
    useUpdateDonationInvite(numericCampaignId, {
      onSuccess: () => {
        navigate(PATH.withAuth.campaign.donationInvite.list.url(campaignId!))

        toast.success("Donation invite updated successfully")
      },
      onError: (error) => {
        toast.error(
          error.response?.data.message ?? "Failed to update donation invite"
        )
      },
    })

  const { data: relationships, isLoading: isRelationshipLoading } =
    useGetRelationships()

  const relationshipOptions =
    relationships?.map((relationship) => ({
      value: relationship.relationships_id,
      label: relationship.name,
    })) ?? []

  // Set the relationship value when data is loaded
  useEffect(() => {
    if (donationInvite && relationships) {
      const relationship = relationships.find(
        (r) => r.relationships_id === donationInvite.relationship_id
      )
      if (relationship) {
        setValue("relationshipId", {
          value: relationship.relationships_id.toString(),
          label: relationship.name,
        })
      }
    }
  }, [donationInvite, relationships, setValue])

  const onSubmit = (data: UpdateDonationInviteFormData) => {
    if (!donationInvite) return

    const payload: Partial<DonationInviteDto> = {
      relationship_id: Number(data.relationshipId.value),
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      phone: data.phone,
    }

    updateDonationInvite({
      id: donationInvite.id,
      data: payload,
    })
  }

  const onBackClick = () => {
    navigate(PATH.withAuth.campaign.donationInvite.list.url(campaignId!))
  }

  const isLoading = isLoadingInvite || isRelationshipLoading || isUpdating

  if (isLoadingInvite) {
    return (
      <div className="flex justify-center items-center h-screen">
        <CircularProgress />
      </div>
    )
  }

  if (!donationInvite) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Typography>Donation invite not found</Typography>
      </div>
    )
  }

  return (
    <div className="bg-gray-100">
      {isLoading && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}

      <div className="flex items-center justify-between p-4 bg-white">
        <div className="flex items-center gap-2 w-[300px]">
          <Button
            variant="text"
            onClick={onBackClick}
            className="min-w-0 !p-2 !w-[64px]"
          >
            <ArrowBackIcon />
          </Button>
          <Typography variant="h6" className="text-xl font-semibold">
            Update Donation Invite
          </Typography>
        </div>
      </div>

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="container mx-auto p-6 max-w-2xl"
      >
        <div className="space-y-4">
          <Input
            label="First Name"
            {...register("first_name")}
            error={!!errors?.first_name}
            helperText={errors?.first_name?.message}
            variant="outlined"
            required
          />

          <Input
            label="Last Name"
            {...register("last_name")}
            error={!!errors?.last_name}
            helperText={errors?.last_name?.message}
            variant="outlined"
            required
          />

          <Input
            label="Phone"
            {...register("phone")}
            error={!!errors?.phone}
            helperText={errors?.phone?.message}
            variant="outlined"
            placeholder="(*************"
            type="phone"
          />

          <Input
            label="Email"
            {...register("email")}
            error={!!errors?.email}
            helperText={errors?.email?.message}
            variant="outlined"
            type="email"
          />

          <Autocomplete
            name="relationshipId"
            label="Relationship"
            options={relationshipOptions}
            control={control}
            error={!!errors?.relationshipId}
            helperText={errors?.relationshipId?.message}
            isLoading={isRelationshipLoading}
            required
          />

          <Button
            type="submit"
            variant="contained"
            fullWidth
            disabled={!isDirty}
          >
            Update Donation Invite
          </Button>
        </div>
      </form>
    </div>
  )
}

export default DonationUpdatePage
