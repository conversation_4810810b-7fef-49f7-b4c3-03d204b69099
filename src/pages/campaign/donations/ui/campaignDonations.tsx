import { toast } from "react-toastify"
import { useGetCampaignSummary } from "@/entities/campaign"
import { useGetCampaignDonations, useDeleteCampaignDonation } from "@/entities/campaignDonation"
import { formatCurrency, usePageTitle } from "@/shared/lib"
import { Roles, usePagination, useUserStore } from "@/shared/model"
import { Table, ConfirmDialog } from "@/shared/ui"
import { CircularProgress, Paper, Typography } from "@mui/material"
import { useMemo, useCallback, useState } from "react"
import { useParams } from "react-router"
import { useCampaignDonationsTableData } from "../model/campaignDonations.hooks"

const CampaignDonations = () => {
  usePageTitle("Donations")
  const { userInfo } = useUserStore()
  const { campaignId } = useParams<{ campaignId: string }>()
  const { pageSize, page, onPaginationModelChange } = usePagination()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedDonationId, setSelectedDonationId] = useState<number | null>(null)

  const { data, isFetching } = useGetCampaignDonations(Number(campaignId), {
    page,
    per_page: pageSize,
  })

  const { data: campaignSummary, isLoading: isLoadingCampaignSummary } =
    useGetCampaignSummary(Number(campaignId))

  const { mutate: deleteDonation, isPending: isDeleting } = useDeleteCampaignDonation({
    onSuccess: () => {
      toast.success("Donation deleted successfully")
    },
    onError: (error) => {
      toast.error(error.message || "Failed to delete donation")
    },
  })

  const { raised, tips, total } = useMemo(() => {
    return {
      raised: campaignSummary?.total_raised ?? 0,
      tips: campaignSummary?.total_tips ?? 0,
      total:
        (campaignSummary?.total_raised ?? 0) +
        (campaignSummary?.total_tips ?? 0),
    }
  }, [campaignSummary])

  const role = userInfo?.role

  const handleDeleteClick = useCallback((donationId: number) => {
    setSelectedDonationId(donationId)
    setDeleteDialogOpen(true)
  }, [])

  const handleDelete = useCallback(() => {
    if (selectedDonationId && campaignId) {
      deleteDonation({
        campaignId: Number(campaignId),
        donationId: selectedDonationId
      })
      setSelectedDonationId(null)
      setDeleteDialogOpen(false)
    }
  }, [selectedDonationId, deleteDonation, campaignId])

  const handleCloseDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(false)
    setSelectedDonationId(null)
  }, [])

  const { columns, rows } = useCampaignDonationsTableData({
    onDelete: handleDeleteClick,
    campaignDonations: data?.data ?? [],
    showDetailedAmounts: role === Roles.Admin || role === Roles.SuperAdmin,
    showDeleteAction: role === Roles.SuperAdmin,
  })

  const currentPage = data?.meta?.current_page ? data.meta.current_page - 1 : 0
  const isLoading = isFetching || isDeleting

  return (
    <>
      <Paper className="flex flex-col gap-4 p-4 mb-4 w-full md:w-1/4">
        {isLoadingCampaignSummary ? (
          <CircularProgress size={20} className="ml-2" />
        ) : (
          <>
            <Typography>
              Donations:{" "}
              <span className="font-bold">
                $
                {formatCurrency(raised, {
                  maximumFractionDigits: 2,
                })}
              </span>
            </Typography>
            <Typography>
              Total Tips:{" "}
              <span className="font-bold">
                $
                {formatCurrency(tips, {
                  maximumFractionDigits: 2,
                })}
              </span>
            </Typography>
            <Typography>
              Total Donations:{" "}
              <span className="font-bold">
                $
                {formatCurrency(total, {
                  maximumFractionDigits: 2,
                })}
              </span>
            </Typography>
          </>
        )}
      </Paper>
      <Table
        columns={columns}
        rows={rows || []}
        rowCount={data?.meta?.total || 0}
        loading={isLoading}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
      />
      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleDelete}
        title="Delete Donation"
        content={`Are you sure you want to delete this donation?`}
      />
    </>
  )
}

export default CampaignDonations
