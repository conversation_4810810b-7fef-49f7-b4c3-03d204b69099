import { useMemo } from "react"
import { getColumns } from "./campaignDonations.columns"
import { CampaignDonationDto } from "@/entities/campaignDonation"
import { v4 as uuidv4 } from "uuid"

interface UseCampaignDonationsTableDataProps {
  onDelete: (id: number) => void
  campaignDonations: CampaignDonationDto[]
  showDetailedAmounts?: boolean
  showDeleteAction?: boolean
}

export const useCampaignDonationsTableData = ({
  onDelete,
  campaignDonations,
  showDetailedAmounts = false,
  showDeleteAction = false,
}: UseCampaignDonationsTableDataProps) => {
  const columns = useMemo(
    () => getColumns({ onDelete }, showDetailedAmounts, showDeleteAction),
    [onDelete, showDetailedAmounts, showDeleteAction]
  )

  const rows = useMemo(
    () => campaignDonations?.map((row) => ({ ...row, id: row.id || uuidv4() })),
    [campaignDonations]
  ) ?? []

  return {
    columns,
    rows,
  }
}