import { GridColDef, GridRenderCellParams, GridValidRowModel } from "@mui/x-data-grid"
import { format } from "date-fns"
import { ActionButton } from "@/shared/ui/ActionButton"

interface GetColumnsProps {
  onDelete: (id: number) => void
}

export const getColumns = (
  { onDelete }: GetColumnsProps,
  showDetailedAmounts: boolean = false,
  showDeleteAction: boolean = false
) => {
  let columns: GridColDef[] = [
    {
      field: "created_at",
      headerName: "Date",
      flex: 1,
      minWidth: 200,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams<GridValidRowModel>) => {
        return format(new Date(params.row.created_at), "MM/dd/yyyy hh:mm a")
      },
    },
    {
      field: "who_invited_first_name",
      headerName: "Source",
      flex: 1,
      sortable: false,
      filterable: false,
      minWidth: 220,
      renderCell: (params: GridRenderCellParams<GridValidRowModel>) => {
        return params.row.who_invited_first_name
          ? `${params.row.who_invited_first_name} ${params.row.who_invited_last_name} (${params.row.who_invited_role})`
          : ""
      },
    },
    {
      field: "donor_first_name",
      headerName: "First Name",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "donor_last_name",
      headerName: "Last Name",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "donor_email",
      headerName: "Email",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "donor_phone_number",
      headerName: "Phone Number",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "base_donation_amount",
      headerName: "Donation",
      flex: 1,
      sortable: false,
      filterable: false,
    },
  ]

  if (showDetailedAmounts) {
    columns = [
      ...columns,
      {
        field: "tip_amount",
        headerName: "Tip",
        flex: 1,
        sortable: false,
        filterable: false,
      },
      {
        field: "total_donation_amount",
        headerName: "Total Donation",
        flex: 1,
        sortable: false,
        filterable: false,
      },
    ]
  }

  if (showDeleteAction) {
    columns = [
      ...columns,
      {
        field: "actions",
        headerName: "Actions",
        width: 140,
        sortable: false,
        filterable: false,
        renderCell: (params: GridRenderCellParams) => (
          <ActionButton
            isIcon
            typeAction="delete"
            onClick={() => onDelete(params.row.id)}
            title="Delete"
          />
        ),
      },
    ]
  }

  return columns
}
