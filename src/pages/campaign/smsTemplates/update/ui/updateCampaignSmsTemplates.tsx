import { useNavigate, useParams } from "react-router"
import { usePageTitle } from "@/shared/lib"
import { CampaignMessageTemplateDetails } from "@/widgets/campaignMessageTemplate"
import { PATH } from "@/shared/config"

const UpdateCampaignSmsTemplates = () => {
  usePageTitle("Sms Template")
  const navigate = useNavigate()

  const { campaignId, templateId } = useParams<{
    campaignId: string
    templateId: string
  }>()
  const onBack = () => {
    navigate(PATH.withAuth.campaign.smsTemplates.list.url(campaignId!))
  }

  return (
    <CampaignMessageTemplateDetails
      campaignId={campaignId!}
      templateId={templateId!}
      type="sms"
      onBack={onBack}
      backTitle="Sms templates"
    />
  )
}
export default UpdateCampaignSmsTemplates
