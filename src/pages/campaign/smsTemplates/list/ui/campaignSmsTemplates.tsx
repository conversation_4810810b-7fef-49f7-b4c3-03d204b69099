import { useCallback } from "react"
import { useNavigate, useParams } from "react-router"
import { usePageTitle } from "@/shared/lib"
import { useGetCampaignMessageTemplates } from "@/entities/campaignMessageTemplate"
import { MessageTemplates } from "@/widgets/campaignMessageTemplate"
import { campaignMessageTemplateApi } from "@/entities/campaignMessageTemplate"
import { PATH } from "@/shared/config"

const type = "sms"

const CampaignSmsTemplates = () => {
  usePageTitle("Sms Templates")
  const { campaignId } = useParams<{ campaignId: string }>()
  const navigate = useNavigate()
  const { data, isLoading: isLoadingTemplates } =
    useGetCampaignMessageTemplates(Number(campaignId), type)

  const handleEdit = (id: number) => {
    navigate(PATH.withAuth.campaign.smsTemplates.details.url(campaignId!, id))
  }
  const onGetDetails = useCallback(
    async (id: number) => {
      const data = await campaignMessageTemplateApi.getById(
        Number(campaignId),
        id
      )
      return data?.[0]
    },
    [campaignId]
  )

  return (
    <MessageTemplates
      getDetails={onGetDetails}
      handleEdit={handleEdit}
      data={data ?? []}
      isLoading={isLoadingTemplates}
    />
  )
}

export default CampaignSmsTemplates
