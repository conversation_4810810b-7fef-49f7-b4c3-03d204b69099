import { ActionButton } from "@/shared/ui/ActionButton"
import { GridRenderCellParams } from "@mui/x-data-grid"

interface GetColumnsProps {
  onEdit: (id: number) => void
  onDelete: (id: number) => void
  onOpen: (id: number) => void
}

export const getColumns = ({ onEdit, onDelete, onOpen }: GetColumnsProps) => {
  return [
    {
      field: "id",
      headerName: "ID",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "group_name",
      headerName: "Name",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <>
          <ActionButton
            isIcon
            typeAction="details"
            onClick={() => onOpen(params.row.id)}
            title="View"
          />
          <ActionButton
            isIcon
            typeAction="edit"
            onClick={() => onEdit(params.row.id)}
            title="Edit"
          />
          <ActionButton
            isIcon
            typeAction="delete"
            onClick={() => onDelete(params.row.id)}
            title="Delete"
          />
        </>
      ),
    },
  ]
}
