import { useMemo } from "react"
import { getColumns } from "./campaignGroups.columns"
import { CampaignGroupDto } from "@/entities/campaignGroup"

interface UseCampaignGroupsTableDataProps {
  onEdit: (id: number) => void
  onDelete: (id: number) => void
  onOpen: (id: number) => void
  campaignGroups: CampaignGroupDto[]
}

export const useCampaignGroupsTableData = ({
  onEdit,
  onDelete,
  onOpen,
  campaignGroups,
}: UseCampaignGroupsTableDataProps) => {
  const columns = useMemo(
    () => getColumns({ onEdit, onDelete, onOpen }),
    [onEdit, onDelete, onOpen]
  )

  const rows =
    useMemo(
      () => campaignGroups?.map((row) => ({ ...row, id: row.id })),
      [campaignGroups]
    ) ?? []

  return {
    columns,
    rows,
  }
}
