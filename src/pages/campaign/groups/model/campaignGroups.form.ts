import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { CampaignGroupFormData, groupSchema } from "./campaignGroups.schema"

export const useGroupForm = (initialData?: CampaignGroupFormData) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CampaignGroupFormData>({
    resolver: yupResolver(groupSchema),
    defaultValues: initialData,
  })

  return {
    register,
    handleSubmit,
    errors,
    reset,
  }
}
