import { But<PERSON> } from "@/shared/ui/Button"
import { Input } from "@/shared/ui/Input"
import { useGroupForm } from "../model/campaignGroups.form"
import { CampaignGroupFormData } from "../model/campaignGroups.schema"

interface GroupFormProps {
  onClose: () => void
  onSubmit: (data: CampaignGroupFormData) => void
  initialData?: CampaignGroupFormData
  isLoading?: boolean
  isEdit?: boolean
}

export const GroupForm = ({
  onClose,
  onSubmit,
  initialData,
  isLoading = false,
  isEdit = false,
}: GroupFormProps) => {
  const { register, handleSubmit, errors } = useGroupForm(initialData)

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col gap-4 pt-2"
    >
      <Input
        {...register("group_name")}
        label="Group Name"
        error={!!errors.group_name}
        helperText={errors.group_name?.message}
        autoFocus
        disabled={isLoading}
      />
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outlined" onClick={onClose} disabled={isLoading}>
          Cancel
        </Button>
        <Button type="submit" variant="contained" isLoading={isLoading}>
          {isEdit ? "Update" : "Create"}
        </Button>
      </div>
    </form>
  )
}
