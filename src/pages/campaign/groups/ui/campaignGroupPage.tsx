import { toast } from "react-toastify"
import { useMemo, useState } from "react"
import { useNavigate, useParams } from "react-router-dom"

import { PATH } from "@/shared/config"
import { ConfirmDialog } from "@/shared/ui"
import { Dialog } from "@/shared/ui"
import { Table } from "@/shared/ui"
import { usePageTitle } from "@/shared/lib"
import { useCampaignGroupsTableData } from "../model/campaignGroups.hooks"
import { GroupForm } from "./campaignGroupForm"
import { useQueryClient } from "@tanstack/react-query"
import {
  useCreateCampaignGroup,
  useDeleteCampaignGroup,
  useGetCampaignGroupsByCampaignId,
  useUpdateCampaignGroup,
} from "@/entities/campaignGroup"
import { CampaignGroupFormData } from "../model/campaignGroups.schema"
import { ActionButton } from "@/shared/ui/ActionButton"

const CampaignGroups = () => {
  usePageTitle("Campaign Groups")
  const { campaignId } = useParams<{ campaignId: string }>()
  const navigate = useNavigate()

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [isCreate, setIsCreate] = useState(false)

  const queryClient = useQueryClient()
  const { data: campaignGroups, isLoading: isLoadingGroups } =
    useGetCampaignGroupsByCampaignId(parseInt(campaignId!))

  const { mutate: deleteGroup, isPending: isDeleting } = useDeleteCampaignGroup(
    {
      onSuccess: () => {
        toast.success("Group deleted successfully")
        queryClient.invalidateQueries({
          queryKey: ["getCampaignGroupsByCampaignId", campaignId],
        })
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }
  )

  const { mutate: createGroup, isPending: isCreating } = useCreateCampaignGroup(
    {
      onSuccess: () => {
        toast.success("Group created successfully")
        queryClient.invalidateQueries({
          queryKey: ["getCampaignGroupsByCampaignId", campaignId],
        })
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }
  )

  const { mutate: updateGroup, isPending: isUpdating } = useUpdateCampaignGroup(
    {
      onSuccess: () => {
        toast.success("Group updated successfully")
        queryClient.invalidateQueries({
          queryKey: ["getCampaignGroupsByCampaignId", campaignId],
        })
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }
  )

  const handleCreate = () => {
    setIsCreate(true)
    setEditDialogOpen(true)
  }

  const handleEdit = (id: number) => {
    setSelectedGroupId(id)
    setEditDialogOpen(true)
  }

  const handleDeleteClick = (id: number) => {
    setSelectedGroupId(id)
    setDeleteDialogOpen(true)
  }

  const handleDelete = () => {
    if (selectedGroupId) {
      deleteGroup(selectedGroupId)
      setSelectedGroupId(null)
      setDeleteDialogOpen(false)
    }
  }

  const handleOpen = (id: number) => {
    navigate(PATH.withAuth.campaign.group.url(campaignId!, id))
  }

  const handleCloseEditDialog = () => {
    setEditDialogOpen(false)
    setSelectedGroupId(null)
    setIsCreate(false)
  }

  const handleSubmitGroup = (data: CampaignGroupFormData) => {
    const groupData = {
      ...data,
      campaign_id: Number(campaignId),
    }

    if (selectedGroupId) {
      updateGroup({ groupId: selectedGroupId, group: groupData })
    } else {
      createGroup(groupData)
    }
    setSelectedGroupId(null)
    handleCloseEditDialog()
  }

  const { columns, rows } = useCampaignGroupsTableData({
    onEdit: handleEdit,
    onDelete: handleDeleteClick,
    onOpen: handleOpen,
    campaignGroups: campaignGroups ?? [],
  })

  const groupToEdit = useMemo(
    () => campaignGroups?.find((group) => group.id === selectedGroupId),
    [campaignGroups, selectedGroupId]
  )

  const isLoading = isLoadingGroups || isDeleting || isCreating || isUpdating

  return (
    <>
      <div className="flex justify-end mb-4">
        <ActionButton
          onClick={handleCreate}
          typeAction="create"
          variant="contained"
          fullWidth={false}
        >
          Create Group
        </ActionButton>
      </div>
      <Table
        columns={columns}
        rows={rows}
        rowCount={rows?.length || 0}
        loading={isLoading}
        page={0}
        pageSize={10}
        hideFooter
      />
      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Group"
        content={`Are you sure you want to delete ${groupToEdit?.group_name} group?`}
      />
      <Dialog
        open={editDialogOpen}
        onClose={handleCloseEditDialog}
        title={isCreate ? "Create Group" : "Edit Group"}
      >
        <GroupForm
          onClose={handleCloseEditDialog}
          onSubmit={handleSubmitGroup}
          initialData={groupToEdit ? groupToEdit : undefined}
          isEdit={!!groupToEdit}
        />
      </Dialog>
    </>
  )
}

export default CampaignGroups
