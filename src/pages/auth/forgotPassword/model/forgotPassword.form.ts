import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  ForgotPasswordFormData,
  forgotPasswordSchema,
} from "./forgotPassword.schema"

export const useForgotPasswordForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: yupResolver(forgotPasswordSchema),
  })

  return {
    register,
    handleSubmit,
    errors,
  }
}
