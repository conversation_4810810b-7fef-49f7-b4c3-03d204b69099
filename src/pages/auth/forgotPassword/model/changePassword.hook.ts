import { useMutation, UseMutationOptions } from "@tanstack/react-query"
import { ResetPasswordRequestDto } from "../api/dto/forgotPassword.dto"
import { resetPassword } from "../api"

export const useResetPasswordMutation = (
  options?: UseMutationOptions<void, Error, ResetPasswordRequestDto>
) => {
  return useMutation({
    mutationFn: (data: ResetPasswordRequestDto) => resetPassword(data),
    ...options,
  })
}
