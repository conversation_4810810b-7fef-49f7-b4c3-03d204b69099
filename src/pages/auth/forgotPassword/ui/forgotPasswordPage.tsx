import { <PERSON> } from "react-router-dom"
import { toast } from "react-toastify"

import { PATH } from "@/shared/config"
import { But<PERSON> } from "@/shared/ui"
import { Input } from "@/shared/ui"
import { usePageTitle } from "@/shared/lib"
import { AuthLayout } from "@/shared/layouts"
import { useResetPasswordMutation } from "../model/changePassword.hook"
import { useForgotPasswordForm } from "../model/forgotPassword.form"
import { ForgotPasswordFormData } from "../model/forgotPassword.schema"

const ForgotPasswordPage = () => {
  usePageTitle("Forgot Password")
  const { register, handleSubmit, errors } = useForgotPasswordForm()

  const { mutate: resetPassword, isPending } = useResetPasswordMutation({
    onSuccess: () => {
      toast.success("If the email is found, we have sent reset instructions.")
    },
    onError: () => {
      toast.error("Failed to reset password")
    },
  })

  const onSubmit = (data: ForgotPasswordFormData) => {
    resetPassword(data)
  }

  return (
    <AuthLayout title="Forgot Password" isLoading={isPending}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <Input
            {...register("email")}
            label="Email"
            variant="outlined"
            error={!!errors.email}
            helperText={errors.email?.message}
          />
        </div>

        <Button className="!mb-4" type="submit" variant="contained" fullWidth>
          Reset Password
        </Button>

        <div className="text-center text-sm">
          <Link
            to={PATH.withoutAuth.login}
            className="text-blue-600 hover:text-blue-800"
          >
            Back to Login
          </Link>
        </div>
      </form>
    </AuthLayout>
  )
}

export default ForgotPasswordPage
