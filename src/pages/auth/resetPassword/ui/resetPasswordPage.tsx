import { Link, useLocation, useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"

import { passwordHelperText, PATH } from "@/shared/config"
import { AuthLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib"
import { Button, Input } from "@/shared/ui"
import { useResetPasswordForm } from "../model/resetPassword.form"
import { useResetPasswordMutation } from "../model/resetPassword.hook"
import { ResetPasswordFormData } from "../model/resetPassword.schema"

const ChangePasswordPage = () => {
  usePageTitle("Reset Password")
  const { register, handleSubmit, errors } = useResetPasswordForm()

  const { token } = useParams()
  const location = useLocation()
  const navigate = useNavigate()

  const searchParams = new URLSearchParams(location.search)
  const email = searchParams.get("email")

  const { mutate: changePassword, isPending } = useResetPasswordMutation({
    onSuccess: () => {
      toast.success("Password changed successfully")
      navigate(PATH.withoutAuth.login)
    },
    onError: (error) => {
      if (error.response?.data?.message) {
        toast.error(error.response.data.message)
      } else {
        toast.error(error.message)
      }
    },
  })

  const onSubmit = (data: ResetPasswordFormData) => {
    const changePasswordData = {
      email: email as string,
      password: data.new_password,
      password_confirmation: data.confirm_new_password,
      token: token as string,
    }

    changePassword(changePasswordData)
  }

  return (
    <AuthLayout title="Reset Password" isLoading={isPending}>
      <form className="w-80" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <Input
            {...register("new_password")}
            label="New Password"
            type="password"
            passwordInput
            variant="outlined"
            error={!!errors.new_password}
            helperText={errors.new_password?.message || passwordHelperText}
          />
        </div>

        <div className="mb-4">
          <Input
            {...register("confirm_new_password")}
            label="Confirm New Password"
            type="password"
            passwordInput
            variant="outlined"
            error={!!errors.confirm_new_password}
            helperText={errors.confirm_new_password?.message}
          />
        </div>

        <Button className="!mb-4" type="submit" variant="contained" fullWidth>
          Reset Password
        </Button>

        <div className="text-center text-sm">
          <Link
            to={PATH.withoutAuth.login}
            className="text-blue-600 hover:text-blue-800"
          >
            Back to Login
          </Link>
        </div>
      </form>
    </AuthLayout>
  )
}

export default ChangePasswordPage
