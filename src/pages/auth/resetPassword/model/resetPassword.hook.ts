import { useMutation, UseMutationOptions } from "@tanstack/react-query"
import { resetPassword } from "../api/index"
import { ResetPasswordRequestDto } from "../api/dto/resetPassword.dto"
import { ApiError } from "@/shared/model"

export const useResetPasswordMutation = (
  options?: UseMutationOptions<void, ApiError, ResetPasswordRequestDto>
) => {
  return useMutation({
    mutationFn: (data: ResetPasswordRequestDto) => resetPassword(data),
    ...options,
  })
}
