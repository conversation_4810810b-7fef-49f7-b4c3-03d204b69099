import { passwordValidation } from "@/shared/config"
import * as yup from "yup"

export const resetPasswordSchema = yup.object().shape({
  new_password: passwordValidation.test(
    "not-same-as-current",
    "New password must be different from current password",
    function (value) {
      return value !== this.parent.current_password
    }
  ),
  confirm_new_password: yup
    .string()
    .oneOf([yup.ref("new_password")], "Passwords must match")
    .required("Confirm password is required"),
})

export type ResetPasswordFormData = yup.InferType<typeof resetPasswordSchema>
