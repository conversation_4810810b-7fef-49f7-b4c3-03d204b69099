import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  ResetPasswordFormData,
  resetPasswordSchema,
} from "./resetPassword.schema"

export const useResetPasswordForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: yupResolver(resetPasswordSchema),
  })

  return {
    register,
    handleSubmit,
    errors,
  }
}
