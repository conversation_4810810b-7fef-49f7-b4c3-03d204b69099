import { UserProvider } from "@/shared/model"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import "@testing-library/jest-dom"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom"
import { useSignUpMutation } from "./model/signUp.hook"
import SignUpPage from "./ui/signUpPage"

jest.mock("../../auth.hooks", () => ({
  useSignUpMutation: jest.fn(),
}))

const mockUserInfo = { email: "<EMAIL>" }

jest.mock("@/shared/store/user/UserContext", () => ({
  ...jest.requireActual("@/shared/store/user/UserContext"),
  useUser: () => ({
    userInfo: mockUserInfo,
    setUserInfo: jest.fn(),
  }),
}))

const mockUseSignUpMutation = useSignUpMutation as jest.Mock

describe("SignUpPage", () => {
  const queryClient = new QueryClient()
  const mutateMock = jest.fn()

  beforeEach(() => {
    mockUseSignUpMutation.mockReturnValue({
      mutate: mutateMock,
      isPending: false,
    })
    mockUserInfo.email = ""
  })

  const renderSignUpPage = () => {
    render(
      <QueryClientProvider client={queryClient}>
        <UserProvider>
          <BrowserRouter>
            <SignUpPage />
          </BrowserRouter>
        </UserProvider>
      </QueryClientProvider>
    )
  }

  it("renders signup form with all necessary elements", () => {
    renderSignUpPage()

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/phone/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
    expect(screen.getByTestId("signup-button")).toBeInTheDocument()
    expect(screen.getByText(/Already have an account/i)).toBeInTheDocument()
  })

  it("shows validation errors for empty fields", async () => {
    renderSignUpPage()

    const submitButton = screen.getByTestId("signup-button")
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/first name is required/i)).toBeInTheDocument()
      expect(screen.getByText(/last name is required/i)).toBeInTheDocument()
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(
        screen.getByText(/Please enter a valid US phone number/i)
      ).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()
      expect(
        screen.getByText(/confirm password is required/i)
      ).toBeInTheDocument()
    })
  })

  it("shows validation error for invalid email", async () => {
    renderSignUpPage()

    const emailInput = screen.getByLabelText(/email/i)
    fireEvent.change(emailInput, { target: { value: "invalid-email" } })

    const submitButton = screen.getByTestId("signup-button")
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/invalid email/i)).toBeInTheDocument()
    })
  })

  it("shows validation error for invalid phone number", async () => {
    renderSignUpPage()

    const phoneInput = screen.getByLabelText(/phone/i)
    fireEvent.change(phoneInput, { target: { value: "123" } })

    const submitButton = screen.getByTestId("signup-button")
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(
        screen.getByText(/please enter a valid us phone number/i)
      ).toBeInTheDocument()
    })
  })

  it("submits form with valid data", async () => {
    renderSignUpPage()

    const emailInput = screen.getByLabelText(/email/i)
    const firstNameInput = screen.getByLabelText(/first name/i)
    const lastNameInput = screen.getByLabelText(/last name/i)
    const phoneInput = screen.getByLabelText(/phone/i)
    const passwordInput = screen.getByLabelText(/^password$/i)
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i)

    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } })
    fireEvent.change(firstNameInput, { target: { value: "John" } })
    fireEvent.change(lastNameInput, { target: { value: "Doe" } })
    fireEvent.change(phoneInput, { target: { value: "(*************" } })
    fireEvent.change(passwordInput, { target: { value: "password123" } })
    fireEvent.change(confirmPasswordInput, { target: { value: "password123" } })

    const submitButton = screen.getByTestId("signup-button")
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mutateMock).toHaveBeenCalledWith(
        expect.objectContaining({
          email: "<EMAIL>",
          firstName: "John",
          lastName: "Doe",
          phone: "(*************",
          password: "password123",
          campaignId: 123,
          campaignRole: 123,
        }),
        expect.any(Object)
      )
    })
  })

  it("includes token in request when present in URL", async () => {
    mockUserInfo.email = "<EMAIL>"
    const token = "test-token"
    jest.spyOn(URLSearchParams.prototype, "get").mockReturnValue(token)

    renderSignUpPage()

    const firstNameInput = screen.getByLabelText(/first name/i)
    const lastNameInput = screen.getByLabelText(/last name/i)
    const phoneInput = screen.getByLabelText(/phone/i)
    const passwordInput = screen.getByLabelText(/^password$/i)
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i)

    fireEvent.change(firstNameInput, { target: { value: "John" } })
    fireEvent.change(lastNameInput, { target: { value: "Doe" } })
    fireEvent.change(phoneInput, { target: { value: "(*************" } })
    fireEvent.change(passwordInput, { target: { value: "password123" } })
    fireEvent.change(confirmPasswordInput, { target: { value: "password123" } })

    const submitButton = screen.getByTestId("signup-button")
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mutateMock).toHaveBeenCalledWith(
        expect.objectContaining({
          email: "<EMAIL>",
          firstName: "John",
          lastName: "Doe",
          phone: "(*************",
          password: "password123",
          token,
          campaignId: 123,
          campaignRole: 123,
        }),
        expect.any(Object)
      )
    })
  })

  it("includes pin in request when present in URL", async () => {
    const pin = "123456"
    jest.spyOn(URLSearchParams.prototype, "get").mockImplementation((param) => {
      if (param === "pin") return pin
      return null
    })

    renderSignUpPage()

    const emailInput = screen.getByLabelText(/email/i)
    const firstNameInput = screen.getByLabelText(/first name/i)
    const lastNameInput = screen.getByLabelText(/last name/i)
    const phoneInput = screen.getByLabelText(/phone/i)
    const passwordInput = screen.getByLabelText(/^password$/i)
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i)

    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } })
    fireEvent.change(firstNameInput, { target: { value: "John" } })
    fireEvent.change(lastNameInput, { target: { value: "Doe" } })
    fireEvent.change(phoneInput, { target: { value: "(*************" } })
    fireEvent.change(passwordInput, { target: { value: "password123" } })
    fireEvent.change(confirmPasswordInput, { target: { value: "password123" } })

    const submitButton = screen.getByTestId("signup-button")
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mutateMock).toHaveBeenCalledWith(
        expect.objectContaining({
          email: "<EMAIL>",
          firstName: "John",
          lastName: "Doe",
          phone: "(*************",
          password: "password123",
          pin,
          campaignId: 123,
          campaignRole: 123,
        }),
        expect.any(Object)
      )
    })
  })

  it("disables email input when user info is present", () => {
    mockUserInfo.email = "<EMAIL>"
    renderSignUpPage()

    const emailInput = screen.getByLabelText(/email/i)
    expect(emailInput).toBeDisabled()
  })
})
