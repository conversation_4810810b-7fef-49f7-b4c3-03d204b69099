export interface SignUpUserDto {
  id: number
  email: string
  first_name: string
  last_name: string
  role_id: number
  phone: string
  created_at: string
  email_verified_at: string
  updated_at: string
}

export interface SignUpRequestDto {
  email: string
  phone: string
  password: string
  password_confirmation: string
  first_name: string
  last_name: string
  invite_token?: string
  pin?: string
  is_pin: boolean
}

export interface SignUpResponseDto {
  token: string
  user: SignUpUserDto
}
