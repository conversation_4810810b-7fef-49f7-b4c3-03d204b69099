import { User } from "@/shared/model"
import { SignUpUserDto } from "../dto/signUp.dto"
import { roleIdToRole } from "@/entities/role"

export const mapUserDtoToUser = (userDto: SignUpUserDto): User => {
  return {
    id: userDto.id,
    firstName: userDto.first_name,
    lastName: userDto.last_name,
    email: userDto.email,
    phone: userDto.phone,
    userRoleId: userDto.role_id,
    role: roleIdToRole[userDto.role_id] as any,
  }
}
