import { API, apiUrls } from "@/shared/api"
import { SignUpRequestDto, SignUpResponseDto } from "./dto/signUp.dto"
import { mapUserDtoToUser } from "./mapper/signUp.mapper"

export const signUp = async (data: SignUpRequestDto) => {
  const {
    data: { user, token },
  } =  await API.post<SignUpResponseDto>(apiUrls.singUp.createAccount(), data)
  const userInfo = mapUserDtoToUser(user)

  return {
    user: userInfo,
    token,
  }
}