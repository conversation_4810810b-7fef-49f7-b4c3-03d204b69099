import { useEffect } from "react"
import { Link, useNavigate, useSearchParams } from "react-router-dom"
import { toast } from "react-toastify"

import { passwordHelperText, PATH } from "@/shared/config"
import { AuthLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib"
import { getInitialRouteByRole, User, useUserStore } from "@/shared/model"
import { Button, Checkbox, Input } from "@/shared/ui"

import { useSignUpForm } from "../model/signUp.form"
import { useAutoFillEmail, useSignUpMutation } from "../model/signUp.hook"
import { LINKS } from "@/shared/config"

const SignUpPage = () => {
  usePageTitle("Sign Up")
  const [searchParams] = useSearchParams()
  const token = searchParams.get("token")
  const pin = searchParams.get("pin")
  const preFilledEmail = useAutoFillEmail()
  const navigate = useNavigate()

  const { userInfo, setUserInfo } = useUserStore()
  const { register, control, handleSubmit, errors, setValue } = useSignUpForm({
    email: userInfo?.email || preFilledEmail,
  })
  const { mutate, isPending } = useSignUpMutation({
    onSuccess: (data: { token: string; user: User }) => {
      toast.success("Registration successful")
      setUserInfo(data.user)
      localStorage.setItem("token", data.token)
      navigate(getInitialRouteByRole(data.user.role))
    },
    onError: (error) => {
      if (error.response?.data?.errors) {
        toast.error(error.response?.data?.errors.email[0])
      } else {
        toast.error(error.message)
      }
    },
  })

  useEffect(() => {
    if (token && preFilledEmail) {
      setValue("email", preFilledEmail, {
        shouldValidate: true,
        shouldDirty: true,
      })
    }
  }, [token, preFilledEmail, setValue])

  const onSubmit = (data: {
    email: string
    firstName: string
    lastName: string
    phone: string
    password: string
    confirmPassword: string
  }) => {
    const payload = {
      first_name: data.firstName.trim(),
      last_name: data.lastName.trim(),
      password: data.password,
      password_confirmation: data.confirmPassword,
      phone: data.phone,
      email: data.email,
      is_pin: !!pin,
      ...(token && { invite_token: token }),
      ...(pin && { pin }),
    }

    mutate(payload)
  }

  // if we have a token or a pin, we need to pass them as query params to the login page
  const loginLink = token
    ? `${PATH.withoutAuth.login}?token=${token}`
    : pin
      ? `${PATH.withoutAuth.login}?pin=${pin}`
      : PATH.withoutAuth.login

  const consentText =
    "I consent to receive SMS text messages from Aktivate about fundraising updates, invitations, and donation activity. I confirm that any supporters I message have given me permission."

  return (
    <AuthLayout title="Create Account" isLoading={isPending}>
      <div className="w-120">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-2">
            <Input
              label={token ? "" : "Email"}
              variant="outlined"
              {...register("email")}
              error={!!errors.email}
              helperText={errors.email?.message}
              disabled={!!token}
              required
            />
          </div>
          <div className="mb-2">
            <Input
              label="First Name"
              variant="outlined"
              {...register("firstName")}
              error={!!errors.firstName}
              helperText={errors.firstName?.message}
              required
            />
          </div>
          <div className="mb-2">
            <Input
              label="Last Name"
              variant="outlined"
              {...register("lastName")}
              error={!!errors.lastName}
              helperText={errors.lastName?.message}
              required
            />
          </div>
          <div className="mb-2">
            <Input
              label="Phone"
              variant="outlined"
              placeholder="(*************"
              {...register("phone")}
              error={!!errors.phone}
              helperText={errors.phone?.message}
              required
            />
          </div>
          <div className="mb-2">
            <Input
              label="Password"
              type="password"
              passwordInput
              variant="outlined"
              {...register("password")}
              error={!!errors.password}
              helperText={errors.password?.message || passwordHelperText}
              required
            />
          </div>
          <div className="mb-2">
            <Input
              label="Confirm Password"
              type="password"
              passwordInput
              variant="outlined"
              {...register("confirmPassword")}
              error={!!errors.confirmPassword}
              helperText={errors.confirmPassword?.message}
              required
            />
          </div>
          <Checkbox
            name="terms"
            label={
              <>
                By signing up you agree to the{" "}
                <Link
                  to={LINKS.termsOfService}
                  className="text-blue-600 underline cursor-pointer hover:text-blue-800"
                  target="_blank"
                >
                  terms and conditions
                </Link>
                *
              </>
            }
            control={control}
            error={!!errors.terms}
            helperText={errors.terms?.message}
          />
          <Checkbox
            name="consent"
            label={consentText}
            control={control}
            error={!!errors.consent}
            helperText={errors.consent?.message}
          />
          <Button
            dataTestId="signup-button"
            type="submit"
            variant="contained"
            fullWidth
            className="!mb-4 !mt-4"
          >
            Create Account
          </Button>
          <div className="text-center">
            <Link to={loginLink} className="text-primary hover:underline">
              Already have an account? Login
            </Link>
          </div>
        </form>
      </div>
    </AuthLayout>
  )
}

export default SignUpPage
