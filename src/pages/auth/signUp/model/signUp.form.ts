import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { SignUpFormData, signUpSchema } from "./signUp.schema"

export const useSignUpForm = ({ email }: { email?: string }) => {
  const {
    control,
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<SignUpFormData>({
    resolver: yupResolver(signUpSchema),
    defaultValues: {
      email: email || '',
    },
  })

  return {
    control,
    register,
    handleSubmit,
    errors,
    setValue,
  }
}
