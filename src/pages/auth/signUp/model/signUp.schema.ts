import { nameValidator, passwordValidation, phoneRegExp } from "@/shared/config"
import * as yup from "yup"

export const signUpSchema = yup.object().shape({
  email: yup.string().email("Invalid email").required("Email is required"),
  firstName: nameValidator
    .name("First name")
    .required("First name is required"),
  lastName: nameValidator.name("Last name").required("Last name is required"),
  phone: yup
    .string()
    .matches(phoneRegExp, "Please enter a valid US phone number")
    .required("Phone number is required"),
  password: passwordValidation,
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "Passwords must match")
    .required("Confirm password is required"),
  terms: yup
    .boolean()
    .oneOf([true], "You must accept the terms and conditions"),
  consent: yup
    .boolean()
    .oneOf([true], "Please confirm your consent to continue"),
})

export type SignUpFormData = yup.InferType<typeof signUpSchema>
