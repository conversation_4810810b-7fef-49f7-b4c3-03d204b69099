import { useMutation } from "@tanstack/react-query"
import { signUp } from "../api"
import { useEffect, useState } from "react"
import { ApiError, User } from "@/shared/model"

interface UseSignUpOptions {
  onSuccess: (data: { token: string; user: User }) => void
  onError: (error: ApiError) => void
}

export const useSignUpMutation = (options?: UseSignUpOptions) => {
  return useMutation({
    mutationFn: signUp,
    ...options,
  })
}
export const useAutoFillEmail = () => {
  const [email, setEmail] = useState("")

  useEffect(() => {
    const storedEmail = localStorage.getItem("inviteEmail")

    if (storedEmail) {
      setEmail(storedEmail)
      localStorage.removeItem("inviteEmail")
    }
  }, [])

  return email
}
