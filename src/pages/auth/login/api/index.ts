import { API, apiUrls } from "@/shared/api"
import { LoginRequestDto, LoginResponseDto } from "./dto/login.dto"
import { mapUserDtoToUser } from "./mapper/login.mapper"

export const login = async (data: LoginRequestDto) => {
  const {
    data: { user, token },
  } = await API.post<LoginResponseDto>(apiUrls.login.login(), data)
  const userInfo = mapUserDtoToUser(user)

  return {
    user: userInfo,
    token,
  }
}

export const acceptByPin = async (data: LoginRequestDto) => {
  const {
    data: { user, token },
  } = await API.post<LoginResponseDto>(apiUrls.campaign.invite.byPin(), data)
  const userInfo = mapUserDtoToUser(user)

  return {
    user: userInfo,
    token,
  }
}
