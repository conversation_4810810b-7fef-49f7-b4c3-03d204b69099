import { User } from "@/shared/model"
import { LoginUserDto } from "../dto/login.dto"
import { roleIdToRole } from "@/entities/role"

export const mapUserDtoToUser = (userDto: LoginUserDto): User => {
  return {
    id: userDto.id,
    firstName: userDto.first_name,
    lastName: userDto.last_name,
    email: userDto.email,
    phone: userDto.phone,
    userRoleId: userDto.role_id,
    role: roleIdToRole[userDto.role_id] as any,
  }
}
