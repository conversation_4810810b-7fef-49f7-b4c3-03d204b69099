import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { LoginFormData, loginSchema } from "./login.schema"

export const useLoginForm = ({ email }: { email?: string }) => {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email,
    },
  })

  return {
    register,
    handleSubmit,
    errors,
    setValue,
    setError,
  }
}
