import { useMutation } from "@tanstack/react-query"
import { login, acceptByPin } from "../api"
import { LoginRequestDto } from "../api/dto/login.dto"
import { ApiError, User } from "@/shared/model"

export const useLoginMutation = (hasPin: boolean = false) => {
  return useMutation<
    { token: string; user: User },
    ApiError,
    LoginRequestDto
  >({
    mutationFn: (data: LoginRequestDto) => hasPin ? acceptByPin(data) : login(data),
  })
}
