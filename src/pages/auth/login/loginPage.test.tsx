import { UserProvider } from "@/shared/model"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import "@testing-library/jest-dom"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom"
import { useLoginMutation } from "./model/login.hook"
import LoginPage from "./ui/loginPage"

jest.mock("../../auth.hooks", () => ({
  useLoginMutation: jest.fn(),
}))

const mockUserInfo = { email: "<EMAIL>" }

jest.mock("@/shared/store/user/UserContext", () => ({
  ...jest.requireActual("@/shared/store/user/UserContext"),
  useUser: () => ({
    userInfo: mockUserInfo,
    setUserInfo: jest.fn(),
  }),
}))

const mockUseLoginMutation = useLoginMutation as jest.Mock

describe("LoginPage", () => {
  const queryClient = new QueryClient()
  const mutateMock = jest.fn()

  beforeEach(() => {
    mockUseLoginMutation.mockReturnValue({
      mutate: mutateMock,
      isPending: false,
    })
    // Reset mockUserInfo before each test
    mockUserInfo.email = ""
  })

  const renderLoginPage = () => {
    render(
      <QueryClientProvider client={queryClient}>
        <UserProvider>
          <BrowserRouter>
            <LoginPage />
          </BrowserRouter>
        </UserProvider>
      </QueryClientProvider>
    )
  }

  it("renders login form with all necessary elements", () => {
    renderLoginPage()

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole("button", { name: /login/i })).toBeInTheDocument()
    expect(screen.getByText(/forgot password\?/i)).toBeInTheDocument()
  })

  it("shows validation errors for empty fields", async () => {
    mockUserInfo.email = "" // Ensure email is empty for this test
    renderLoginPage()

    const submitButton = screen.getByRole("button", { name: /login/i })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()
    })
  })

  it("shows validation error for invalid email", async () => {
    renderLoginPage()

    const emailInput = screen.getByLabelText(/email/i)
    fireEvent.change(emailInput, { target: { value: "invalid-email" } })

    const submitButton = screen.getByRole("button", { name: /login/i })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/invalid email/i)).toBeInTheDocument()
    })
  })

  it("submits form with valid data", async () => {
    mockUserInfo.email = "" // Reset email for this test
    renderLoginPage()

    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole("button", { name: /login/i })

    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } })
    fireEvent.change(passwordInput, { target: { value: "password123" } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mutateMock).toHaveBeenCalledWith(
        {
          email: "<EMAIL>",
          password: "password123",
        },
        expect.any(Object)
      )
    })
  })

  it("includes token in request when present in URL", async () => {
    mockUserInfo.email = "<EMAIL>" // Set email for this test
    const token = "test-token"
    jest.spyOn(URLSearchParams.prototype, "get").mockReturnValue(token)

    renderLoginPage()

    const passwordInput = screen.getByLabelText(/password/i)
    fireEvent.change(passwordInput, { target: { value: "password123" } })

    const submitButton = screen.getByRole("button", { name: /login/i })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mutateMock).toHaveBeenCalledWith(
        expect.objectContaining({
          email: "<EMAIL>",
          password: "password123",
          token,
        }),
        expect.any(Object)
      )
    })
  })

  it("includes pin in request when present in URL", async () => {
    const pin = "123456"
    jest.spyOn(URLSearchParams.prototype, "get").mockImplementation((param) => {
      if (param === "pin") return pin
      return null
    })

    renderLoginPage()

    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)

    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } })
    fireEvent.change(passwordInput, { target: { value: "password123" } })

    const submitButton = screen.getByRole("button", { name: /login/i })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mutateMock).toHaveBeenCalledWith(
        expect.objectContaining({
          email: "<EMAIL>",
          password: "password123",
          pin,
        }),
        expect.any(Object)
      )
    })
  })

  it("hides forgot password link when token or pin is present", () => {
    const token = "test-token"
    jest.spyOn(URLSearchParams.prototype, "get").mockReturnValue(token)

    renderLoginPage()

    expect(screen.queryByText(/forgot password\?/i)).not.toBeInTheDocument()
  })
})
