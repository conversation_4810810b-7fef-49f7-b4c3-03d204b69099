import { <PERSON>, useNavi<PERSON>, useSearchParams } from "react-router-dom"
import { toast } from "react-toastify"

import { PATH } from "@/shared/config"
import { AuthLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib"
import {
  ApiError,
  getInitialRouteByRole,
  User,
  useUserStore,
} from "@/shared/model"
import { Button, Input } from "@/shared/ui"

import { useLoginForm } from "../model/login.form"
import { useLoginMutation } from "../model/login.hook"

const LoginPage = () => {
  usePageTitle("Login")
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const token = searchParams.get("token")
  const pin = searchParams.get("pin")
  const { userInfo, setUserInfo } = useUserStore()

  const { register, handleSubmit, errors, setError } = useLoginForm({
    email: userInfo?.email,
  })
  const { mutate, isPending } = useLoginMutation(!!pin)

  const onSubmit = (data: { email: string; password: string }) => {
    const payload = {
      ...data,
      ...(token && { token }),
      ...(pin && { pin }),
    }

    mutate(payload, {
      onError: (error: ApiError) => {
        const message = error.response?.data?.message || error.message
        const errorType = error.response?.data?.errors?.type
        toast.error(message)

        if (errorType === "user_already_joined") {
          navigate(PATH.withoutAuth.login)
        } else {
          setError("email", { message: "Invalid credentials" })
          setError("password", { message: "Invalid credentials" })
        }
      },
      onSuccess: (data: { token: string; user: User }) => {
        setUserInfo(data.user)
        localStorage.setItem("token", data.token)

        if (pin) {
          toast.success("Successfully joined campaign")
        }

        navigate(getInitialRouteByRole(data.user.role))
      },
    })
  }

  return (
    <AuthLayout title="Login" isLoading={isPending}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-2">
          <Input
            label="Email"
            variant="outlined"
            {...register("email")}
            error={!!errors.email}
            helperText={errors.email?.message}
            disabled={!!userInfo?.email}
          />
        </div>
        <div className="mb-4">
          <Input
            label="Password"
            type="password"
            variant="outlined"
            passwordInput
            {...register("password")}
            error={!!errors.password}
            helperText={errors.password?.message}
          />
        </div>
        <Button type="submit" variant="contained" fullWidth className="!mb-4">
          Login
        </Button>
        <div className="mb-4 flex justify-end text-sm">
          {!token && !pin && (
            <Link
              to={PATH.withoutAuth.forgotPassword}
              className="text-primary hover:underline"
            >
              Forgot password?
            </Link>
          )}
        </div>
      </form>
    </AuthLayout>
  )
}

export default LoginPage
