import { useState, useEffect } from "react"
import { Dialog } from "@/shared/ui/Dialog"
import { Button } from "@/shared/ui/Button"
import { Input } from "@/shared/ui/Input"

interface SportFormDialogProps {
  open: boolean
  onClose: () => void
  onSubmit: (name: string) => Promise<void>
  title: string
  initialValue?: string
  isEdit?: boolean
  isLoading?: boolean
}

export const SportFormDialog = ({
    open,
    onClose,
    onSubmit,
    title,
    initialValue = "",
    isEdit = false,
    isLoading = false,
  }: SportFormDialogProps) => {
  const [sportName, setSportName] = useState(initialValue)

  useEffect(() => {
    if (open) {
      setSportName(initialValue)
    }
  }, [open, initialValue])

  const handleClose = () => {
    setSportName("")
    onClose()
  }

  const handleSubmit = async () => {
    if (!sportName.trim()) return
    await onSubmit(sportName.trim())
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      title={title}
    >
      <div className="space-y-4">
        {isEdit && (
            <div>
              <strong>Warning:</strong> Changes will affect all existing campaigns that use this sport
            </div>
        )}

        <Input
          label="Sport Name"
          variant="outlined"
          value={sportName}
          onChange={(e) => setSportName(e.target.value)}
          placeholder="Enter sport name"
          fullWidth
          disabled={isLoading}
          autoFocus
        />

        <div className="flex justify-end gap-4">
          <Button
            onClick={handleClose}
            variant="outlined"
            fullWidth={false}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            fullWidth={false}
            disabled={isLoading || !sportName.trim()}
          >
            {isEdit ? "Update" : "Create"}
          </Button>
        </div>
      </div>
    </Dialog>
  )
}