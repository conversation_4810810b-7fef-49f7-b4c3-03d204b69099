import { toast } from "react-toastify"
import { useMemo, useState } from "react"
import { Table } from "@/shared/ui"
import { DashboardLayout } from "@/shared/layouts"
import { ActionButton } from "@/shared/ui/ActionButton"
import { asyncConfirm } from "@/shared/ui/AsyncConfirm"
import {
  useGetSports,
  useCreateSport,
  useUpdateSport,
  useDeleteSport
} from "@/entities/sport"
import { useSportsTableData } from "../model/sports.hooks"
import { SportFormDialog } from "./sportFormDialog"

const SportsPage = () => {
  const [formDialogOpen, setFormDialogOpen] = useState(false)
  const [selectedSportId, setSelectedSportId] = useState<number | null>(null)
  const [isEdit, setIsEdit] = useState(false)
  const { data: sports = [], isLoading: isSportsLoading } = useGetSports()

  const createSportMutation = useCreateSport({
    onSuccess: () => {
      toast.success("Sport created successfully")
      handleCloseFormDialog()
    },
    onError: (error) => {
      toast.error(String(error?.response?.data.errors))
    }
  })

  const updateSportMutation = useUpdateSport({
    onSuccess: () => {
      toast.success("Sport updated successfully")
      handleCloseFormDialog()
    },
    onError: (error) => {
      toast.error(String(error?.response?.data.errors))
    }
  })

  const deleteSportMutation = useDeleteSport({
    onSuccess: () => {
      toast.success("Sport deleted successfully")
    },
    onError: (error) => {
      toast.error(String(error?.response?.data.errors))
    }
  })

  const handleCreate = () => {
    setIsEdit(false)
    setSelectedSportId(null)
    setFormDialogOpen(true)
  }

  const handleEdit = (id: number) => {
    const sport = sports.find(s => s.sport_id === id)
    if (sport) {
      setSelectedSportId(id)
      setIsEdit(true)
      setFormDialogOpen(true)
    }
  }

  const handleDeleteClick = async (id: number) => {
    const sport = sports.find(s => s.sport_id === id)
    const confirmed = await asyncConfirm({
      title: "Delete Sport",
      message: `Are you sure you want to delete "${sport?.name}" sport? This action will affect all existing campaigns that use this sport.`
    })

    if (confirmed) {
      deleteSportMutation.mutate(id)
    }
  }

  const handleCloseFormDialog = () => {
    setFormDialogOpen(false)
    setSelectedSportId(null)
    setIsEdit(false)
  }

  const handleSubmitForm = async (name: string): Promise<void> => {
    if (isEdit && selectedSportId) {
      updateSportMutation.mutate({ sportId: selectedSportId, name: { name } })
    } else {
      createSportMutation.mutate({ name })
    }
  }

  const selectedSport = useMemo(
    () => sports?.find((sport) => sport.sport_id === selectedSportId),
    [sports, selectedSportId]
  )

  const { columns, rows } = useSportsTableData({
    onEdit: handleEdit,
    onDelete: handleDeleteClick,
    sports: sports ?? [],
  })

  const isLoading = createSportMutation.isPending ||
    updateSportMutation.isPending ||
    deleteSportMutation.isPending

  const tableLoading = isSportsLoading || isLoading
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  })

  const paginatedRows = useMemo(() => {
    const start = paginationModel.page * paginationModel.pageSize
    const end = start + paginationModel.pageSize
    return rows.slice(start, end)
  }, [rows, paginationModel])

  return (
    <DashboardLayout title="Sports">
      <div className="mt-10">
        <div className="flex justify-center">
          <div className="w-[90%] lg:w-1/2">
            <div className="flex justify-end mb-4">
              <ActionButton
                onClick={handleCreate}
                typeAction="create"
                variant="contained"
                fullWidth={false}
                disabled={isLoading}
              >
                Create Sport
              </ActionButton>
            </div>
            <Table
              columns={columns}
              rows={paginatedRows}
              rowCount={rows.length}
              loading={tableLoading}
              page={paginationModel.page}
              pageSize={paginationModel.pageSize}
              onPaginationModelChange={(model) => {
                setPaginationModel(model)
              }}
              pageSizeOptions={[10]}
            />
          </div>
        </div>

        <SportFormDialog
          open={formDialogOpen}
          onClose={handleCloseFormDialog}
          onSubmit={handleSubmitForm}
          title={isEdit ? "Edit Sport" : "Create Sport"}
          initialValue={selectedSport?.name || ""}
          isEdit={isEdit}
          isLoading={isEdit ? updateSportMutation.isPending : createSportMutation.isPending}
        />
      </div>
    </DashboardLayout>
  )
}

export default SportsPage