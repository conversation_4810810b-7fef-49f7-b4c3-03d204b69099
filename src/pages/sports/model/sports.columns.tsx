import { ActionButton } from "@/shared/ui/ActionButton"
import { GridRenderCellParams } from "@mui/x-data-grid"

interface GetColumnsProps {
  onEdit: (id: number) => void
  onDelete: (id: number) => void
}

export const getColumns = ({ onEdit, /*onDelete*/ }: GetColumnsProps) => {
  return [
    {
      field: "name",
      headerName: "Name",
      flex: 2,
      sortable: false,
      filterable: false,
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <>
          <ActionButton
            isIcon
            typeAction="edit"
            onClick={() => onEdit(params.row.sport_id)}
            title="Edit"
          />
          {/*<ActionButton
            isIcon
            typeAction="delete"
            onClick={() => onDelete(params.row.sport_id)}
            title="Delete"
          />*/}
        </>
      ),
    },
  ]
}