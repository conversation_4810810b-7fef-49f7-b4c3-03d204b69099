import { useMemo } from "react"
import { getColumns } from "./sports.columns"
import { Sport } from "@/entities/sport"

interface UseSportsTableDataProps {
  onEdit: (id: number) => void
  onDelete: (id: number) => void
  sports: Sport[]
}

export const useSportsTableData = ({
   onEdit,
   onDelete,
   sports,
 }: UseSportsTableDataProps) => {
  const columns = useMemo(
    () => getColumns({ onEdit, onDelete }),
    [onEdit, onDelete]
  )

  const rows = useMemo(
    () => sports?.map((sport) => ({
      ...sport,
      id: sport.sport_id
    })),
    [sports]
  ) ?? []

  return {
    columns,
    rows,
  }
}