import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { CreateAdFormData, createAdSchema } from "./createAd.schema"

export const useCreateAdForm = () => {
  return useForm<CreateAdFormData>({
    resolver: yup<PERSON><PERSON>olver(createAdSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      password: "",
      password_confirmation: "",
      role_id: { label: "Athletic Director", value: 4 },
      campaigns: [],
    },
  })
}
