import { Control } from "react-hook-form"
import { <PERSON>complete, Button } from "@/shared/ui"
import DeleteIcon from "@mui/icons-material/Delete"

interface Option {
  label: string
  value: number
}

interface CampaignRoleSelectProps {
  control: Control
  index: number
  campaignOptions: Option[]
  isLoadingCampaigns: boolean
  onRemove: () => void
  errors?: {
    id?: { message?: string }
    campaign_role_id?: { message?: string }
  }
}

export const CampaignRoleSelect = ({
  control,
  index,
  campaignOptions,
  isLoadingCampaigns,
  onRemove,
  errors,
}: CampaignRoleSelectProps) => {
  return (
    <div className="flex gap-4 items-center">
      <div className="flex-1">
        <Autocomplete
          label="Campaign"
          name={`campaigns.${index}.id`}
          control={control}
          isLoading={isLoadingCampaigns}
          options={campaignOptions}
          error={!!errors?.id}
          helperText={errors?.id?.message}
          disableClearable
          required
        />
      </div>
      <div className="flex-initial -mt-6 h-14 w-14">
        <Button
          type="button"
          variant="text"
          color="error"
          onClick={onRemove}
          className="p-3 h-14 w-14 flex items-center justify-center hover:bg-error-50"
        >
          <DeleteIcon fontSize="medium" />
        </Button>
      </div>
    </div>
  )
}
