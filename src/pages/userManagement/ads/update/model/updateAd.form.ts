import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import { UpdateAdFormData, adSchema } from "./updateAd.schema"
import { useEffect, useMemo } from "react"
import { roleIdToRole } from "@/entities/role"
import { AdDto } from "@/entities/userManagement/ad"

export const useUpdateAdForm = (initialData?: AdDto) => {
  const defaultValues = useMemo(
    () =>
      initialData
        ? {
            first_name: initialData.first_name,
            last_name: initialData.last_name,
            email: initialData.email,
            phone: initialData.phone,
            role_id: {
              label: roleIdToRole[initialData.role_id],
              value: initialData.role_id,
            },
            campaigns:
              initialData.campaigns?.map((campaign) => ({
                id: {
                  label: campaign.name,
                  value: campaign.id,
                },
              })) || [],
          }
        : {},
    [initialData]
  )

  const form = useForm<UpdateAdFormData>({
    resolver: yupResolver(adSchema),
    defaultValues: defaultValues || {},
  })

  useEffect(() => {
    if (!form.formState.isDirty && defaultValues) {
      form.reset(defaultValues, {
        keepValues: false,
        keepDefaultValues: true,
      })
    }
  }, [form, defaultValues])

  return form
}
