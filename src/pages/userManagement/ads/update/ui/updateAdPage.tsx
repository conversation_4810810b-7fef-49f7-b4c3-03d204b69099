import { useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { Button, Input, Autocomplete } from "@/shared/ui"
import { useUpdateAdForm } from "../model/updateAd.form"
import { UpdateAdFormData } from "../model/updateAd.schema"
import { ApiError } from "@/shared/model"
import { useGetCampaigns } from "@/entities/campaign"
import { CampaignRoleSelect } from "./CampaignRoleSelect"
import { useUpdateAd, useGetAd } from "@/entities/userManagement/ad"
import Form from "@/shared/ui/Form"

const UpdateAdPage = () => {
  usePageTitle("Update Athletic Director")
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()

  const { data: ad, isLoading: isLoadingAd } = useGetAd(Number(id))

  const {
    control,
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useUpdateAdForm(ad)

  const campaigns = watch("campaigns") || []

  const { data: campaignsList, isLoading: isLoadingCampaigns } =
    useGetCampaigns({
      page: 1,
      per_page: 1000,
    })

  const campaignOptions =
    campaignsList?.data
      // NOTE: can't add to same campaign more that once
      .filter(
        (campaign) =>
          !campaigns.some(
            (selectedCampaign) =>
              selectedCampaign.id.value === campaign.campaign_id
          )
      )
      .map((campaign) => ({
        label: campaign.name,
        value: campaign.campaign_id,
      })) || []

  const roleOptions = [
    {
      label: "Athletic Director",
      value: 4,
    },
  ]

  const { mutate: updateAd, isPending: isUpdatingAd } = useUpdateAd({
    onSuccess: () => {
      navigate(PATH.withAuth.userManagement.ad.list)
      toast.success("User updated successfully")
    },
    onError: (error: ApiError) => {
      if (Array.isArray(error.response?.data.errors.email)) {
        error.response?.data.errors.email.forEach((error) => toast.error(error))
      } else {
        toast.error("Failed to update user")
      }
    },
  })

  const onSubmit = (data: UpdateAdFormData) => {
    updateAd({
      id: Number(id),
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      phone: data.phone,
      role_id: data.role_id.value,
      campaigns:
        data.campaigns?.map((campaign) => ({
          id: campaign.id.value!,
        })) || [],
    })
  }

  const addNewCampaign = () => {
    const newCampaign = {
      id: {
        label: "",
        value: 0,
      },
    }
    setValue("campaigns", [...campaigns, newCampaign])
  }

  const removeCampaign = (index: number) => {
    setValue("campaigns", campaigns.toSpliced(index, 1))
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} isLoading={isLoadingAd}>
      <div className="space-y-4 flex flex-col gap-4">
        <Input
          label="First Name"
          {...register("first_name")}
          error={!!errors?.first_name}
          helperText={errors?.first_name?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Last Name"
          {...register("last_name")}
          error={!!errors?.last_name}
          helperText={errors?.last_name?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Email"
          type="email"
          {...register("email")}
          error={!!errors?.email}
          helperText={errors?.email?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Phone"
          {...register("phone")}
          error={!!errors?.phone}
          helperText={errors?.phone?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Autocomplete
          label="Role"
          name="role_id"
          disabled={true}
          control={control}
          options={roleOptions}
          error={!!errors.role_id}
          helperText={errors.role_id?.message}
        />

        <>
          {campaigns.map((_, index) => (
            <div key={`campaign-${index}`}>
              <div className="mb-10">
                <hr className="border-t border-gray-200" />
              </div>
              <CampaignRoleSelect
                control={control as any}
                index={index}
                campaignOptions={campaignOptions}
                isLoadingCampaigns={isLoadingCampaigns}
                onRemove={() => removeCampaign(index)}
                errors={{
                  id: errors.campaigns?.[index]?.id,
                }}
              />
            </div>
          ))}
        </>

        <Button
          type="button"
          variant="outlined"
          onClick={addNewCampaign}
          startIcon={
            <span className="text-xl leading-none relative -top-0.5">+</span>
          }
        >
          Add user to campaign
        </Button>

        <Button type="submit" variant="contained" disabled={isUpdatingAd}>
          Update Athletic Director
        </Button>
      </div>
    </Form>
  )
}

export default UpdateAdPage
