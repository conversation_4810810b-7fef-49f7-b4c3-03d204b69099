import { Button, Input, Table } from "@/shared/ui"
import { useState } from "react"

import { getColumns } from "../model/adList.columns"
import { usePagination } from "@/shared/model"
import { useNavigate } from "react-router-dom"
import { PATH } from "@/shared/config"
import { useGetAds, AdFiltersDTO } from "@/entities/userManagement/ad"
import { useForm } from "react-hook-form"

interface AdFilterForm {
  full_name: string
}

const UsersListPage = () => {
  const { page, onPaginationModelChange, pageSize } = usePagination()
  const [filters, setFilters] = useState<AdFiltersDTO>({
    full_name: "",
  })

  const { handleSubmit, reset, register } = useForm<AdFilterForm>({
    defaultValues: {
      full_name: "",
    },
  })

  const queryParams = {
    page,
    per_page: pageSize,
    ...filters,
  }

  const { data: userList, isLoading, error } = useGetAds(queryParams)

  const navigate = useNavigate()

  if (error) {
    return <div>Error loading users: {error.message}</div>
  }

  const onSubmit = (data: AdFilterForm) => {
    setFilters({
      full_name: data.full_name,
    })
  }

  const onReset = () => {
    reset()
    setFilters({
      full_name: "",
    })
  }

  return (
    <div className="space-y-4">
      <div className="flex w-full justify-between items-end gap-6 md:mb-1">
        <form
          className="flex flex-col md:flex-row md:flex-wrap md:gap-6 gap-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="w-full md:w-60">
            <Input
              {...register("full_name")}
              name="full_name"
              label="Full Name"
              variant="standard"
              placeholder="Search by name..."
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-5 py-5">
            <Button
              variant="contained"
              color="primary"
              type="submit"
              size="small"
              className="w-full sm:w-auto"
            >
              Apply
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={onReset}
              size="small"
              className="w-full sm:w-auto"
            >
              Reset
            </Button>
          </div>
        </form>
        <div className="py-5">
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate(PATH.withAuth.userManagement.ad.create)}
            size="small"
          >
            Create User
          </Button>
        </div>
      </div>
      <Table
        columns={getColumns({
          onEdit: (user) =>
            navigate(PATH.withAuth.userManagement.ad.update.url(user.id)),
        })}
        rows={userList?.data || []}
        rowCount={userList?.meta?.total || 0}
        loading={isLoading}
        page={userList?.meta?.current_page ? userList.meta.current_page - 1 : 0}
        pageSize={pageSize}
        onPaginationModelChange={onPaginationModelChange}
        height={1000}
      />
    </div>
  )
}

export default UsersListPage
