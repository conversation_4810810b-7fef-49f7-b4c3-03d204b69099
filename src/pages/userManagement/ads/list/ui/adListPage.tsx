import { Table } from "@/shared/ui"

import { getColumns } from "../model/adList.columns"
import { usePagination } from "@/shared/model"
import { useNavigate } from "react-router-dom"
import { PATH } from "@/shared/config"
import { useGetAds } from "@/entities/userManagement/ad"

const UsersListPage = () => {
  const { page, onPaginationModelChange, pageSize } = usePagination()
  const {
    data: userList,
    isLoading,
    error,
  } = useGetAds({
    page,
    per_page: pageSize,
  })

  const navigate = useNavigate()

  if (error) {
    return <div>Error loading users: {error.message}</div>
  }

  const actions = [
    {
      label: "Create Athletic Director",
      onClick: () => navigate(PATH.withAuth.userManagement.ad.create),
    },
  ]

  return (
    <Table
      columns={getColumns({
        onEdit: (user) =>
          navigate(PATH.withAuth.userManagement.ad.update.url(user.id)),
      })}
      rows={userList?.data || []}
      rowCount={userList?.meta?.total || 0}
      loading={isLoading}
      page={userList?.meta?.current_page ? userList.meta.current_page - 1 : 0}
      pageSize={pageSize}
      onPaginationModelChange={onPaginationModelChange}
      height={1000}
      actions={actions}
    />
  )
}

export default UsersListPage
