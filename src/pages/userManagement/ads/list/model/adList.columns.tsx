import { roleIdToRole } from "@/entities/role"
import { UserDto } from "@/entities/userManagement/user"
import { ActionButton } from "@/shared/ui/ActionButton"
import { GridRenderCellParams } from "@mui/x-data-grid"

interface GetColumnsProps {
  onEdit: (row: UserDto) => void
}

export const getColumns = ({ onEdit }: GetColumnsProps) => {
  return [
    {
      field: "id",
      headerName: "ID",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "first_name",
      headerName: "First Name",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "last_name",
      headerName: "Last Name",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "phone",
      headerName: "Phone",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "role_id",
      headerName: "Role",
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return <div>{roleIdToRole[params.row.role_id]}</div>
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <ActionButton
            isIcon
            typeAction="edit"
            onClick={() => onEdit(params.row)}
            title="Edit"
          />
        )
      },
    },
  ]
}
