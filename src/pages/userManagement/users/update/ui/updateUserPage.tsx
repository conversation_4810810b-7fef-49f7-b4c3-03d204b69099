import { useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { Button, Input, Autocomplete } from "@/shared/ui"
import { useUpdateUserForm } from "../model/updateUser.form"
import { UpdateUserFormData } from "../model/updateUser.schema"
import { useGetUser, useUpdateUser } from "@/entities/userManagement/user"
import { useGetCampaignRoles } from "@/entities/campaignRole"
import { ApiError, Roles, useUserStore } from "@/shared/model"
import { useGetCampaigns } from "@/entities/campaign"
import { CampaignRoleSelect } from "./CampaignRoleSelect"
import Form from "@/shared/ui/Form"

const UpdateUserPage = () => {
  usePageTitle("Update User")
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const { userInfo } = useUserStore()

  const { data: user, isLoading: isLoadingUser } = useGetUser(Number(id))

  const {
    control,
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useUpdateUserForm(user)

  const campaigns = watch("campaigns") || []

  const { data: campaignRoles, isLoading: isLoadingRoles } =
    useGetCampaignRoles()
  const { data: campaignsList, isLoading: isLoadingCampaigns } =
    useGetCampaigns({
      page: 1,
      per_page: 1000,
    })

  const campaignOptions =
    campaignsList?.data
      // NOTE: can't add to same campaign more that once
      .filter(
        (campaign) =>
          !campaigns.some(
            (selectedCampaign) =>
              selectedCampaign.id.value === campaign.campaign_id
          )
      )
      .map((campaign) => ({
        label: campaign.name,
        value: campaign.campaign_id,
      })) || []

  const campaignRoleOptions =
    campaignRoles?.map((role) => ({
      label: role.name,
      value: role.campaign_role_id,
    })) || []

  const roleOptions = Object.values(Roles)
    .map((role, index) => ({
      label: role,
      value: index + 1,
    }))
    .filter(({ value }) => userInfo && userInfo.userRoleId <= value)

  const { mutate: updateUser, isPending: isUpdatingUser } = useUpdateUser({
    onSuccess: () => {
      navigate(PATH.withAuth.userManagement.user.list)
      toast.success("User updated successfully")
    },
    onError: (error: ApiError) => {
      if (Array.isArray(error.response?.data.errors.email)) {
        error.response?.data.errors.email.forEach((error) => toast.error(error))
      } else {
        toast.error("Failed to update user")
      }
    },
  })

  const onSubmit = (data: UpdateUserFormData) => {
    updateUser({
      id: Number(id),
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      phone: data.phone,
      role_id: data.role_id.value,
      campaigns:
        data.campaigns?.map((campaign) => ({
          id: campaign.id.value!,
          campaign_role_id: campaign.campaign_role_id.value!,
        })) || [],
    })
  }

  const addNewCampaign = () => {
    const newCampaign = {
      id: {
        label: "",
        value: 0,
      },
      campaign_role_id: {
        label: "",
        value: 0,
      },
    }
    setValue("campaigns", [...campaigns, newCampaign])
  }

  const removeCampaign = (index: number) => {
    setValue("campaigns", campaigns.toSpliced(index, 1))
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} isLoading={isLoadingUser}>
      <div className="space-y-4 flex flex-col gap-4">
        <Input
          label="First Name"
          {...register("first_name")}
          error={!!errors?.first_name}
          helperText={errors?.first_name?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Last Name"
          {...register("last_name")}
          error={!!errors?.last_name}
          helperText={errors?.last_name?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Email"
          type="email"
          {...register("email")}
          error={!!errors?.email}
          helperText={errors?.email?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Phone"
          {...register("phone")}
          error={!!errors?.phone}
          helperText={errors?.phone?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Autocomplete
          label="Role"
          name="role_id"
          disabled={true}
          control={control}
          options={roleOptions}
          error={!!errors.role_id}
          helperText={errors.role_id?.message}
        />

        <>
          {campaigns.map((_, index) => (
            <div key={`campaign-${index}`}>
              <div className="mb-10">
                <hr className="border-t border-gray-200" />
              </div>
              <CampaignRoleSelect
                control={control}
                index={index}
                campaignOptions={campaignOptions}
                campaignRoleOptions={campaignRoleOptions}
                isLoadingCampaignRoles={isLoadingRoles}
                isLoadingCampaigns={isLoadingCampaigns}
                onRemove={() => removeCampaign(index)}
                errors={{
                  id: errors.campaigns?.[index]?.id,
                  campaign_role_id: errors.campaigns?.[index]?.campaign_role_id,
                }}
              />
            </div>
          ))}
        </>

        <Button
          type="button"
          variant="outlined"
          onClick={addNewCampaign}
          startIcon={
            <span className="text-xl leading-none relative -top-0.5">+</span>
          }
        >
          Add user to campaign
        </Button>

        <Button type="submit" variant="contained" disabled={isUpdatingUser}>
          Update User
        </Button>
      </div>
    </Form>
  )
}

export default UpdateUserPage
