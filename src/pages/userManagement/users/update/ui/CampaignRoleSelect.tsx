import { Control } from "react-hook-form"
import { Autocomplete, Button } from "@/shared/ui"
import { UpdateUserFormData } from "../model/updateUser.schema"
import DeleteIcon from "@mui/icons-material/Delete"

interface Option {
  label: string
  value: number
}

interface CampaignRoleSelectProps {
  control: Control<UpdateUserFormData>
  index: number
  campaignOptions: Option[]
  campaignRoleOptions: Option[]
  isLoadingCampaignRoles: boolean
  isLoadingCampaigns: boolean
  onRemove: () => void
  errors?: {
    id?: { message?: string }
    campaign_role_id?: { message?: string }
  }
}

export const CampaignRoleSelect = ({
  control,
  index,
  campaignOptions,
  campaignRoleOptions,
  isLoadingCampaignRoles,
  isLoadingCampaigns,
  onRemove,
  errors,
}: CampaignRoleSelectProps) => {
  return (
    <div className="flex gap-4 items-center">
      <div className="flex-1">
        <Autocomplete
          label="Campaign"
          name={`campaigns.${index}.id`}
          control={control}
          isLoading={isLoadingCampaigns}
          options={campaignOptions}
          error={!!errors?.id}
          helperText={errors?.id?.message}
          disableClearable
          required
        />
      </div>
      <div className="flex-1">
        <Autocomplete
          label="Campaign Role"
          name={`campaigns.${index}.campaign_role_id`}
          control={control}
          isLoading={isLoadingCampaignRoles}
          options={campaignRoleOptions}
          error={!!errors?.campaign_role_id}
          helperText={errors?.campaign_role_id?.message}
          disableClearable
          required
        />
      </div>
      <div className="flex-initial -mt-6 h-14 w-14">
        <Button
          type="button"
          variant="text"
          color="error"
          onClick={onRemove}
          className="p-3 h-14 w-14 flex items-center justify-center hover:bg-error-50"
        >
          <DeleteIcon fontSize="medium" />
        </Button>
      </div>
    </div>
  )
}
