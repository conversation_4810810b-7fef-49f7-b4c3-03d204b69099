import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import { UpdateUserFormData, userSchema } from "./updateUser.schema"
import { UserDto } from "@/entities/userManagement/user"
import { useEffect, useMemo } from "react"
import { roleIdToRole } from "@/entities/role"
import { campaignRoleIdToCampaignRole } from "@/shared/model"

export const useUpdateUserForm = (initialData?: UserDto) => {
  const defaultValues = useMemo(
    () =>
      initialData
        ? {
            first_name: initialData.first_name,
            last_name: initialData.last_name,
            email: initialData.email,
            phone: initialData.phone,
            role_id: initialData.role_id
              ? {
                  label: roleIdToRole[initialData.role_id],
                  value: initialData.role_id,
                }
              : undefined,
            is_campaign_owner: initialData.is_campaign_owner,
            campaigns:
              initialData.campaigns?.map((campaign) => ({
                id: {
                  label: campaign.name,
                  value: campaign.id,
                },
                campaign_role_id: {
                  label:
                    campaignRoleIdToCampaignRole[campaign.campaign_role_id],
                  value: campaign.campaign_role_id,
                },
              })) || [],
          }
        : {},
    [initialData]
  )

  const form = useForm<UpdateUserFormData>({
    resolver: yupResolver(userSchema),
    defaultValues: defaultValues || {},
  })

  useEffect(() => {
    if (!form.formState.isDirty && defaultValues) {
      form.reset(defaultValues, {
        keepValues: false,
        keepDefaultValues: true,
      })
    }
  }, [form, defaultValues])

  return form
}
