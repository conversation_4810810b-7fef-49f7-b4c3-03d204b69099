import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { CreateUserFormData, createUserSchema } from "./createUser.schema"

export const useCreateUserForm = () => {
  return useForm<CreateUserFormData>({
    resolver: yupResolver(createUserSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      password: "",
      password_confirmation: "",
      role_id: { label: "User", value: 3 },
      campaigns: [],
    },
  })
}
