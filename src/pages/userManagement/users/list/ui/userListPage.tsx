import { Table } from "@/shared/ui"

import { getColumns } from "../model/usersList.columns"
import { usePagination } from "@/shared/model"
import { useGetUsers } from "@/entities/userManagement/user"
import { useNavigate } from "react-router-dom"
import { PATH } from "@/shared/config"

const UsersListPage = () => {
  const { page, onPaginationModelChange, pageSize } = usePagination()
  const {
    data: userList,
    isLoading,
    error,
  } = useGetUsers({
    page,
    per_page: pageSize,
  })

  const navigate = useNavigate()

  if (error) {
    return <div>Error loading users: {error.message}</div>
  }

  const actions = [
    {
      label: "Create User",
      onClick: () => navigate(PATH.withAuth.userManagement.user.create),
    },
  ]

  return (
    <Table
      columns={getColumns({
        onEdit: (user) =>
          navigate(PATH.withAuth.userManagement.user.update.url(user.id)),
      })}
      rows={userList?.data || []}
      rowCount={userList?.meta?.total || 0}
      loading={isLoading}
      page={userList?.meta?.current_page ? userList.meta.current_page - 1 : 0}
      pageSize={pageSize}
      onPaginationModelChange={onPaginationModelChange}
      height={1000}
      actions={actions}
    />
  )
}

export default UsersListPage
