import { Table, Input, Autocomplete, Button } from "@/shared/ui"
import { useForm } from "react-hook-form"
import { useState } from "react"

import { getColumns } from "../model/usersList.columns"
import { usePagination } from "@/shared/model"
import { useGetUsers } from "@/entities/userManagement/user"
import { useNavigate } from "react-router-dom"
import { PATH } from "@/shared/config"
import { campaignRoleOptions } from "@/entities/campaign"
import { UsersFilterDto } from "@/entities/userManagement/user"

interface UserFilterForm {
  full_name: string
  campaign_role_id: { value: number; label: string } | null
}

const UsersListPage = () => {
  const { page, onPaginationModelChange, pageSize } = usePagination()
  const navigate = useNavigate()

  const [filters, setFilters] = useState<UsersFilterDto>({
    full_name: "",
    campaign_role_id: null,
  })

  const { control, handleSubmit, reset, register } = useForm<UserFilterForm>({
    defaultValues: {
      full_name: "",
      campaign_role_id: null,
    }
  })

  const queryParams = {
    page,
    per_page: pageSize,
    ...filters,
  }

  const {
    data: userList,
    isLoading,
    error,
  } = useGetUsers(queryParams)

  if (error) {
    return <div>Error loading users: {error.message}</div>
  }

  const onSubmit = (data: UserFilterForm) => {
    setFilters({
      full_name: data.full_name,
      campaign_role_id: data.campaign_role_id?.value ?? null,
    })
  }

  const onReset = () => {
    reset()
    setFilters({
      full_name: "",
      campaign_role_id: null,
    })
  }

  return (
    <div className="space-y-4">
      <div className="flex w-full justify-between items-end gap-6 md:mb-1">
        <form
          className="flex flex-col md:flex-row md:flex-wrap md:gap-6 gap-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="w-full md:w-60">
            <Input
              {...register("full_name")}
              name="full_name"
              label="Full Name"
              variant="standard"
              placeholder="Search by name..."
            />
          </div>
          <div className="w-full md:w-48">
            <Autocomplete
              name="campaign_role_id"
              label="Campaign Role"
              options={campaignRoleOptions}
              control={control}
              fieldVariant="standard"
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-5 py-5">
            <Button
              variant="contained"
              color="primary"
              type="submit"
              size="small"
              className="w-full sm:w-auto"
            >
              Apply
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={onReset}
              size="small"
              className="w-full sm:w-auto"
            >
              Reset
            </Button>
          </div>
        </form>
        <div className="py-5">
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate(PATH.withAuth.userManagement.user.create)}
            size="small"
          >
            Create User
          </Button>
        </div>
      </div>
      <Table
        columns={getColumns({
          onEdit: (user) =>
            navigate(PATH.withAuth.userManagement.user.update.url(user.id)),
        })}
        rows={userList?.data || []}
        rowCount={userList?.meta?.total || 0}
        loading={isLoading}
        page={userList?.meta?.current_page ? userList.meta.current_page - 1 : 0}
        pageSize={pageSize}
        onPaginationModelChange={onPaginationModelChange}
        height={1000}
      />
    </div>
  )
}

export default UsersListPage