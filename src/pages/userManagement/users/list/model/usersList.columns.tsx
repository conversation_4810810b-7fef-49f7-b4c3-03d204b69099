import { UserDto } from "@/entities/userManagement/user"
import { campaignRoleIdToCampaignRole } from "@/shared/model"
import { ActionButton } from "@/shared/ui/ActionButton"
import { GridRenderCellParams } from "@mui/x-data-grid"

interface GetColumnsProps {
  onEdit: (row: UserDto) => void
}

export const getColumns = ({ onEdit }: GetColumnsProps) => {
  return [
    {
      field: "id",
      headerName: "ID",
      flex: 1,
      sortable: false,
      filterable: false,
      minWidth: 60,
    },
    {
      field: "first_name",
      headerName: "First Name",
      flex: 1,
      sortable: false,
      filterable: false,
      minWidth: 100,
    },
    {
      field: "last_name",
      headerName: "Last Name",
      flex: 1,
      sortable: false,
      filterable: false,
      minWidth: 100,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
      sortable: false,
      filterable: false,
      minWidth: 200,
    },
    {
      field: "phone",
      headerName: "Phone",
      flex: 1,
      sortable: false,
      filterable: false,
      minWidth: 150,
    },
    {
      field: "role_id",
      headerName: "Role",
      flex: 1,
      sortable: false,
      filterable: false,
      minWidth: 250,
      renderCell: (params: GridRenderCellParams) => {
        const roles = params.row.campaign_role_ids
          .map((role: number) => campaignRoleIdToCampaignRole[role])
          .join(", ")
        return <div>{roles}</div>
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <ActionButton
            isIcon
            typeAction="edit"
            onClick={() => onEdit(params.row)}
            title="Edit"
          />
        )
      },
    },
  ]
}
