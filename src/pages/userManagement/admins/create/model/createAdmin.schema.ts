import { nameValidator, passwordValidation, phoneRegExp } from "@/shared/config"
import * as yup from "yup"

export const createAdminSchema = yup.object().shape({
  first_name: nameValidator
    .name("First name")
    .required("First name is required"),
  last_name: nameValidator.name("Last name").required("Last name is required"),
  email: yup
    .string()
    .email("Invalid email address")
    .required("Email is required"),
  phone: yup
    .string()
    .matches(phoneRegExp, "Please enter a valid US phone number")
    .required("Phone number is required"),
  password: passwordValidation,
  password_confirmation: yup
    .string()
    .oneOf([yup.ref("password")], "Passwords must match")
    .required("Confirm password is required"),
  role_id: yup
    .object()
    .shape({
      value: yup.number().required(),
      label: yup.string().required(),
    })
    .required("Role is required"),
  is_campaign_owner: yup.boolean().optional(),
  campaigns: yup.array().of(
    yup.object().shape({
      id: yup
        .object()
        .shape({
          label: yup.string().required("Campaign is required"),
          value: yup.number().required("Campaign is required"),
        })
        .required("Campaign is required"),
    })
  ),
})

export type CreateAdminFormData = yup.InferType<typeof createAdminSchema>
