import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { CreateAdminFormData, createAdminSchema } from "./createAdmin.schema"

export const useCreateAdminForm = () => {
  return useForm<CreateAdminFormData>({
    resolver: yupResolver(createAdminSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      password: "",
      password_confirmation: "",
      role_id: undefined,
      is_campaign_owner: false,
      campaigns: [],
    },
  })
}
