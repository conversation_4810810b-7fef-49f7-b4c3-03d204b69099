import { useGetCampaigns } from "@/entities/campaign"
import { useCreateAdmin } from "@/entities/userManagement/admin"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { ApiError, Roles, useUserStore } from "@/shared/model"
import { Autocomplete, Button, Checkbox, Input } from "@/shared/ui"
import { useNavigate } from "react-router-dom"
import { toast } from "react-toastify"
import { useCreateAdminForm } from "../model/createAdmin.form"
import { CreateAdminFormData } from "../model/createAdmin.schema"
import { CampaignOwnerSelect } from "./CampaignOwnerSelect"

const CreateAdminPage = () => {
  usePageTitle("Create Admin")
  const navigate = useNavigate()
  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useCreateAdminForm()

  const { userInfo } = useUserStore()
  const isCampaignOwner = watch("is_campaign_owner")
  const campaigns = watch("campaigns") || []

  const { data: campaignsList, isLoading: isLoadingCampaigns } =
    useGetCampaigns({
      page: 1,
      per_page: 1000,
    })

  const campaignOptions =
    campaignsList?.data
      // NOTE: can't add to same campaign more that once
      .filter(
        (campaign) =>
          !campaigns.some(
            (selectedCampaign) =>
              selectedCampaign.id.value === campaign.campaign_id
          )
      )
      .map((campaign) => ({
        label: campaign.name,
        value: campaign.campaign_id,
      })) || []

  const roleOptions = Object.values(Roles)
    .map((role, index) => ({
      label: role,
      value: index + 1,
    }))
    .filter(({ value }) => userInfo && userInfo.userRoleId <= value)
    .slice(0, -2)

  const { mutate: createAdmin, isPending: isCreatingUser } = useCreateAdmin({
    onSuccess: () => {
      navigate(PATH.withAuth.userManagement.admin.list)
      toast.success("User created successfully")
    },
    onError: (error: ApiError) => {
      if (Array.isArray(error.response?.data.errors.email)) {
        error.response?.data.errors.email.forEach((error) => toast.error(error))
      } else {
        toast.error("Failed to create user")
      }
    },
  })

  const onSubmit = (data: CreateAdminFormData) => {
    createAdmin({
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      phone: data.phone,
      password: data.password,
      password_confirmation: data.password_confirmation,
      role_id: data.role_id?.value,
      is_campaign_owner: data.is_campaign_owner,
      campaigns:
        data.campaigns?.map((campaign) => ({
          id: campaign.id.value,
          name: campaign.id.label,
          is_campaign_owner: true,
        })) || [],
    })
  }

  const removeCampaign = (index: number) => {
    setValue("campaigns", campaigns.toSpliced(index, 1))
  }

  const addNewCampaign = () => {
    const newCampaign = {
      id: {
        label: "",
        value: 0,
      },
      is_campaign_owner: true,
    }
    setValue("campaigns", [...campaigns, newCampaign])
  }

  return (
    <div className="w-full bg-white rounded p-6 mx-auto max-w-2xl">
      <form onSubmit={handleSubmit(onSubmit)} className="max-w-2xl mx-auto">
        <div className="space-y-4 flex flex-col gap-4">
          <Input
            label="First Name"
            {...register("first_name")}
            error={!!errors?.first_name}
            helperText={errors?.first_name?.message}
            variant="outlined"
            required
          />

          <Input
            label="Last Name"
            {...register("last_name")}
            error={!!errors?.last_name}
            helperText={errors?.last_name?.message}
            variant="outlined"
            required
          />

          <Input
            label="Email"
            type="email"
            {...register("email")}
            error={!!errors?.email}
            helperText={errors?.email?.message}
            variant="outlined"
            required
          />

          <Input
            label="Phone"
            {...register("phone")}
            error={!!errors?.phone}
            helperText={errors?.phone?.message}
            variant="outlined"
            required
          />

          <Input
            label="Password"
            type="password"
            {...register("password")}
            error={!!errors?.password}
            helperText={errors?.password?.message}
            variant="outlined"
            required
          />

          <Input
            label="Confirm Password"
            type="password"
            {...register("password_confirmation")}
            error={!!errors?.password_confirmation}
            helperText={errors?.password_confirmation?.message}
            variant="outlined"
            required
          />

          <Autocomplete
            label="Role"
            name="role_id"
            control={control}
            options={roleOptions}
            required
            error={!!errors.role_id}
            helperText={errors.role_id?.message}
          />

          <div className="-mt-5">
            <Checkbox
              label="Is Campaign Owner"
              name="is_campaign_owner"
              control={control}
              error={!!errors?.is_campaign_owner}
            />
          </div>

          {isCampaignOwner && (
            <>
              {campaigns.map((campaign, index) => (
                <div key={`campaign-${campaign.id.value}`}>
                  <div className="mb-10">
                    <hr className="border-t border-gray-200" />
                  </div>
                  <CampaignOwnerSelect
                    control={control as any}
                    index={index}
                    campaignOptions={campaignOptions}
                    isLoadingCampaigns={isLoadingCampaigns}
                    onRemove={() => removeCampaign(index)}
                    disabled={false}
                    errors={{
                      id: errors.campaigns?.[index]?.id,
                    }}
                  />
                </div>
              ))}
            </>
          )}

          {isCampaignOwner && (
            <Button
              type="button"
              variant="outlined"
              onClick={addNewCampaign}
              startIcon={
                <span className="text-xl leading-none relative -top-0.5">
                  +
                </span>
              }
            >
              Add admin as campaign owner to campaign
            </Button>
          )}

          <Button type="submit" variant="contained" disabled={isCreatingUser}>
            Create User
          </Button>
        </div>
      </form>
    </div>
  )
}

export default CreateAdminPage
