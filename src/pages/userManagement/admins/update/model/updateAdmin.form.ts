import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import { UpdateAdminFormData, adminSchema } from "./updateAdmin.schema"
import { AdminDto } from "@/entities/userManagement/admin"
import { useEffect, useMemo } from "react"
import { roleIdToRole } from "@/entities/role"

export const useUpdateAdminForm = (initialData?: AdminDto) => {
  const defaultValues = useMemo(
    () =>
      initialData
        ? {
            first_name: initialData.first_name,
            last_name: initialData.last_name,
            email: initialData.email,
            phone: initialData.phone,
            role_id: initialData.role_id
              ? {
                  label: roleIdToRole[initialData.role_id],
                  value: initialData.role_id,
                }
              : undefined,
            is_campaign_owner: initialData.is_campaign_owner,
            campaigns:
              initialData.campaigns?.map((campaign) => ({
                id: {
                  label: campaign.name,
                  value: campaign.id,
                },
                is_campaign_owner: campaign.is_campaign_owner,
                disabled: true,
              })) || [],
          }
        : {},
    [initialData]
  )

  const form = useForm<UpdateAdminFormData>({
    resolver: yupResolver(adminSchema),
    defaultValues: defaultValues || {},
  })

  useEffect(() => {
    if (!form.formState.isDirty && defaultValues) {
      form.reset(defaultValues, {
        keepValues: false,
        keepDefaultValues: true,
      })
    }
  }, [form, defaultValues])

  return form
}
