import { nameValida<PERSON>, phoneRegExp } from "@/shared/config"
import * as yup from "yup"

export const adminSchema = yup.object().shape({
  first_name: nameValidator
    .name("First name")
    .required("First name is required"),
  last_name: nameValidator.name("Last name").required("Last name is required"),
  email: yup
    .string()
    .email("Invalid email address")
    .required("Email is required"),
  phone: yup
    .string()
    .matches(phoneRegExp, "Please enter a valid US phone number")
    .required("Phone number is required"),
  role_id: yup
    .object()
    .shape({
      label: yup.string().required(),
      value: yup.number().required(),
    })
    .required(),
  is_campaign_owner: yup
    .boolean()
    .required("Can be campaign owner is required"),
  campaigns: yup.array().of(
    yup.object().shape({
      id: yup
        .object()
        .shape({
          label: yup.string().required("Campaign is required"),
          value: yup.number().required("Campaign is required"),
        })
        .required("Campaign is required"),
      disabled: yup.boolean(),
    })
  ),
})

export type UpdateAdminFormData = yup.InferType<typeof adminSchema>
