import { useNavigate, useParams } from "react-router-dom"
import { toast } from "react-toastify"
import { PATH } from "@/shared/config"
import { usePageTitle } from "@/shared/lib"
import { Button, Input, Autocomplete, Checkbox } from "@/shared/ui"
import { useUpdateAdminForm } from "../model/updateAdmin.form"
import { UpdateAdminFormData } from "../model/updateAdmin.schema"
import {
  AdminDto,
  useGetAdmin,
  useUpdateAdmin,
} from "@/entities/userManagement/admin"
import { ApiError, Roles, useUserStore } from "@/shared/model"
import { useGetCampaigns } from "@/entities/campaign"
import { CampaignOwnerSelect } from "./CampaignOwnerSelect"
import { Info as InfoIcon } from "@mui/icons-material"
import { Tooltip, IconButton } from "@mui/material"
import Form from "@/shared/ui/Form"

const UpdateAdminPage = () => {
  usePageTitle("Update Admin")
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const { userInfo } = useUserStore()

  const { data: admin, isLoading: isLoadingAdmin } = useGetAdmin(Number(id))

  const {
    control,
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useUpdateAdminForm(admin as AdminDto)

  const isCampaignOwner = watch("is_campaign_owner")
  const campaigns = watch("campaigns") || []

  const { data: campaignsList, isLoading: isLoadingCampaigns } =
    useGetCampaigns({
      page: 1,
      per_page: 1000,
    })

  const campaignOptions =
    campaignsList?.data
      // NOTE: can't add to same campaign more that once
      .filter(
        (campaign) =>
          !campaigns.some(
            (selectedCampaign) =>
              selectedCampaign.id.value === campaign.campaign_id
          )
      )
      .map((campaign) => ({
        label: campaign.name,
        value: campaign.campaign_id,
      })) || []

  const roleOptions = Object.values(Roles)
    .map((role, index) => ({
      label: role,
      value: index + 1,
    }))
    .filter(({ value }) => userInfo && userInfo.userRoleId <= value)

  const { mutate: updateUser, isPending: isUpdatingUser } = useUpdateAdmin({
    onSuccess: () => {
      navigate(PATH.withAuth.userManagement.admin.list)
      toast.success("User updated successfully")
    },
    onError: (error: ApiError) => {
      if (Array.isArray(error.response?.data.errors.email)) {
        error.response?.data.errors.email.forEach((error) => toast.error(error))
      } else {
        toast.error("Failed to update user")
      }
    },
  })

  const onSubmit = (data: UpdateAdminFormData) => {
    updateUser({
      id: Number(id),
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      phone: data.phone,
      role_id: data.role_id.value as 1 | 2,
      is_campaign_owner: data.is_campaign_owner ?? false,
      campaigns:
        data.campaigns?.map((campaign) => ({
          id: campaign.id.value,
          name: campaign.id.label,
          is_campaign_owner: true,
        })) || [],
    })
  }

  const addNewCampaign = () => {
    const newCampaign = {
      id: {
        label: "",
        value: 0,
      },
      is_campaign_owner: true,
    }
    setValue("campaigns", [...campaigns, newCampaign])
  }

  const removeCampaign = (index: number) => {
    setValue("campaigns", campaigns.toSpliced(index, 1))
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} isLoading={isLoadingAdmin}>
      <div className="space-y-4 flex flex-col gap-4">
        <Input
          label="First Name"
          {...register("first_name")}
          error={!!errors?.first_name}
          helperText={errors?.first_name?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Last Name"
          {...register("last_name")}
          error={!!errors?.last_name}
          helperText={errors?.last_name?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Email"
          type="email"
          {...register("email")}
          error={!!errors?.email}
          helperText={errors?.email?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Input
          label="Phone"
          {...register("phone")}
          error={!!errors?.phone}
          helperText={errors?.phone?.message}
          variant="outlined"
          required
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
        />

        <Autocomplete
          label="Role"
          name="role_id"
          disabled={true}
          control={control}
          options={roleOptions}
          error={!!errors.role_id}
          helperText={errors.role_id?.message}
        />

        <div className="flex items-center gap-2 -mt-4">
          <Checkbox
            name="is_campaign_owner"
            label="Is campaign owner"
            control={control}
            error={!!errors.is_campaign_owner}
            helperText={errors.is_campaign_owner?.message}
            disabled={campaigns.length > 0}
          />
          <Tooltip
            title="User will be added as sales person. You can assign them as campaign owner here or on the campaign page."
            placement="right"
            arrow
          >
            <IconButton size="small">
              <InfoIcon fontSize="small" color="action" />
            </IconButton>
          </Tooltip>
        </div>

        {isCampaignOwner && (
          <>
            {campaigns.map((campaign, index) => (
              <div key={`campaign-${campaign.id.value}`}>
                <div className="mb-10">
                  <hr className="border-t border-gray-200" />
                </div>
                <CampaignOwnerSelect
                  control={control as any}
                  index={index}
                  campaignOptions={campaignOptions}
                  isLoadingCampaigns={isLoadingCampaigns}
                  onRemove={() => removeCampaign(index)}
                  disabled={!!campaign.disabled}
                  errors={{
                    id: errors.campaigns?.[index]?.id,
                  }}
                />
              </div>
            ))}
          </>
        )}

        {isCampaignOwner && (
          <Button
            type="button"
            variant="outlined"
            onClick={addNewCampaign}
            startIcon={
              <span className="text-xl leading-none relative -top-0.5">+</span>
            }
          >
            Add admin as campaign owner to campaign
          </Button>
        )}

        <Button type="submit" variant="contained" disabled={isUpdatingUser}>
          Update User
        </Button>
      </div>
    </Form>
  )
}

export default UpdateAdminPage
