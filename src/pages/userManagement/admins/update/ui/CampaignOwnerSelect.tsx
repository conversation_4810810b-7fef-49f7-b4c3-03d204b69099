import { Control } from "react-hook-form"
import { Autocomplete, Button } from "@/shared/ui"
import DeleteIcon from "@mui/icons-material/Delete"

interface Option {
  label: string
  value: number
}

interface CampaignOwnerSelectProps {
  control: Control
  index: number
  campaignOptions: Option[]
  isLoadingCampaigns: boolean
  onRemove: () => void
  errors?: {
    id?: { message?: string }
    is_campaign_owner?: { message?: string }
  }
  disabled: boolean
}

export const CampaignOwnerSelect = ({
  control,
  index,
  campaignOptions,
  isLoadingCampaigns,
  onRemove,
  errors,
  disabled,
}: CampaignOwnerSelectProps) => {
  return (
    <div className="flex gap-4 items-center">
      <div className="flex-1">
        <Autocomplete
          label="Campaign"
          name={`campaigns.${index}.id`}
          control={control}
          isLoading={isLoadingCampaigns}
          options={campaignOptions}
          error={!!errors?.id}
          helperText={errors?.id?.message}
          disabled={disabled}
          disableClearable
        />
      </div>
      <div className="flex-initial -mt-6 h-14 w-14">
        <Button
          type="button"
          variant="text"
          color="error"
          onClick={onRemove}
          className="p-3 h-14 w-14 flex items-center justify-center hover:bg-error-50"
          disabled={disabled}
        >
          <DeleteIcon fontSize="medium" />
        </Button>
      </div>
    </div>
  )
}
