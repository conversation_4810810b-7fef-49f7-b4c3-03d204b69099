import * as yup from "yup"

export const welcomeSchema = yup
  .object()
  .shape({
    email: yup.string().email("Invalid email").required("Email is required"),
    token: yup.string(),
    pin: yup.string(),
  })
  .test("token-or-pin", "Either token or PIN is required", function (value) {
    if (!value.token && !value.pin) {
      const path = value.token === undefined ? "pin" : "token"
      return this.createError({
        path: path,
        message: `${path.charAt(0).toUpperCase() + path.slice(1)} is required`,
      })
    }
    return true
  })

export type InviteFormData = yup.InferType<typeof welcomeSchema>
