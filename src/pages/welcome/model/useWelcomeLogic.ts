import { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { toast } from "react-toastify"

import { PATH } from "@/shared/config"
import { useValidateInviteToken } from "@/entities/invite"
import { useGetCampaignByToken } from "@/entities/campaign"

interface WelcomeLogicProps {
  token: string | null
  pin: string | null
}

// TODO: check all this logic
export const useWelcomeLogic = ({ token, pin }: WelcomeLogicProps) => {
  const navigate = useNavigate()
  const {
    data: tokenCampaignData,
    error: tokenCampaignError,
    isLoading: isGettingCampaign,
  } = useGetCampaignByToken(token as string)

  const {
    mutate: handleTokenValidation,
    isPending: isValidateEmailTokenLoading,
  } = useValidateInviteToken(token as string, {
    onSuccess: (res: any) => {
      if (res.email) {
        localStorage.setItem("inviteEmail", res.email)
      }

      handleTokenRedirect(!!res.user)
    },
    onError: () => {
      toast.error("Invite token is invalid or has already been used")
      navigate(PATH.withoutAuth.login)
    },
  })

  useEffect(() => {
    if (tokenCampaignError) {
      handleError()
    }
  }, [tokenCampaignError])

  const handleTokenRedirect = (userExists: boolean) => {
    const params = `token=${token}`
    const path = userExists ? PATH.withoutAuth.login : PATH.withoutAuth.signup

    if (userExists) {
      toast.success("You were added to the campaign. Please login.")
    }

    navigate(`${path}?${params}`)
  }

  const handlePinRedirect = (isLogin: boolean) => {
    const params = `pin=${pin}`
    const path = isLogin ? PATH.withoutAuth.login : PATH.withoutAuth.signup
    navigate(`${path}?${params}`)
  }

  const handleError = () => {
    toast.error("Something went wrong")
  }

  if (!token && !pin) {
    toast.error("Token or pin is not provided in the URL")
  }

  return {
    handlePinRedirect,
    handleTokenValidation,
    isValidateEmailTokenLoading,
    tokenCampaignData,
    isGettingCampaign,
  }
}
