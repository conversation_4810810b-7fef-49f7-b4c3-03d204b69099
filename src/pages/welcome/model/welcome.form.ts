import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { InviteFormData, welcomeSchema } from "./welcome.schema.ts"

export const useInviteForm = (token?: string | null, pin?: string | null) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<InviteFormData>({
    resolver: yupResolver(welcomeSchema),
    defaultValues: {
      token: token || undefined,
      pin: pin || undefined,
    },
  })

  return {
    register,
    handleSubmit,
    errors,
    setValue,
  }
}
