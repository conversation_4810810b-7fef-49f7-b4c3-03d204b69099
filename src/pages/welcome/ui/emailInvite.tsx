import { useNavigate } from "react-router-dom"
import { toast } from "react-toastify"

import { Button } from "@/shared/ui/Button"
import { Text } from "@/shared/ui/Text"
import { Input } from "@/shared/ui/Input"
import { useGetCampaignByToken } from "@/entities/campaign"
import { useValidateInviteToken } from "@/entities/invite"
import { PATH } from "@/shared/config"
import { AuthLayout } from "@/shared/layouts/"

interface EmailInviteProps {
  token: string
}

export const EmailInvite = ({ token }: EmailInviteProps) => {
  const navigate = useNavigate()

  const { data: tokenCampaignData, isLoading: isGettingCampaign } =
    useGetCampaignByToken(token)

  const { mutate: validateToken, isPending: isValidateEmailTokenLoading } =
    useValidateInviteToken(token, {
      onSuccess: (res) => {
        if (res.email) {
          localStorage.setItem("inviteEmail", res.email)
        }

        const userExists = !!res.user

        const params = `token=${token}`
        const path = userExists
          ? PATH.withoutAuth.login
          : PATH.withoutAuth.signup

        if (userExists) {
          toast.success("You were added to the campaign. Please login.")
        }

        navigate(`${path}?${params}`)
      },
      onError: () => {
        toast.error("Invite token is invalid or has already been used")
        navigate(PATH.withoutAuth.login)
      },
    })

  const handleValidateToken = () => validateToken()

  if (!token) {
    toast.error("Token or pin is not provided in the URL")
  }

  return (
    <AuthLayout
      title="Welcome"
      isLoading={isValidateEmailTokenLoading || isGettingCampaign}
    >
      <div className="flex flex-col gap-4">
        <Text variant="h1" className="text-center !text-2xl mb-10">
          You are about to join the campaign {tokenCampaignData?.name}
        </Text>
        <Input
          label="Token"
          value={token || ""}
          disabled
          variant="outlined"
          className="mb-4"
        />
        <Button
          type="submit"
          variant="contained"
          fullWidth
          onClick={handleValidateToken}
        >
          Validate
        </Button>
      </div>
    </AuthLayout>
  )
}
