import { usePageTitle } from "@/shared/lib"
import { useSearchParams } from "react-router-dom"

import { EmailInvite } from "./emailInvite.tsx"
import { PinInvite } from "./pinInvite.tsx"

const WelcomePage = () => {
  usePageTitle("Welcome")
  const [searchParams] = useSearchParams()

  const token = searchParams.get("token")
  const pin = searchParams.get("pin")
  const isTokenInvite = !!token

  return isTokenInvite ? (
    <EmailInvite token={token} />
  ) : (
    <PinInvite pin={pin!} />
  )
}

export default WelcomePage
