import { Button } from "@/shared/ui/Button"
import { Text } from "@/shared/ui/Text"
import { useNavigate } from "react-router-dom"
import { toast } from "react-toastify"

import { useGetCampaignByPin } from "@/entities/campaign"
import { PATH } from "@/shared/config"
import { AuthLayout } from "@/shared/layouts/"
import { useEffect } from "react"

interface PinInviteProps {
  pin: string
}

export const PinInvite = ({ pin }: PinInviteProps) => {
  const {
    data: campaignData,
    isLoading: isGettingCampaign,
    error,
  } = useGetCampaignByPin(pin!, { retry: false })

  const navigate = useNavigate()

  useEffect(() => {
    if (error) {
      toast.error("Campaign was not found by pin")
      navigate(PATH.withoutAuth.login)
    }
  }, [error, navigate])

  const handleButtonClick = (isLogin: boolean) => {
    const params = `pin=${pin}`
    const path = isLogin ? PATH.withoutAuth.login : PATH.withoutAuth.signup
    navigate(`${path}?${params}`)
  }

  return (
    <AuthLayout title="Welcome" isLoading={isGettingCampaign}>
      <div className="mb-8">
        <Text variant="h2" className="text-center !text-base">
          Welcome to the Campaign {campaignData?.name}! To join either login or
          create an account
        </Text>
      </div>
      <div className="flex gap-4">
        <Button
          variant="contained"
          fullWidth
          onClick={() => handleButtonClick(true)}
        >
          Login
        </Button>
        <Button
          variant="contained"
          fullWidth
          onClick={() => handleButtonClick(false)}
        >
          Create Account
        </Button>
      </div>
    </AuthLayout>
  )
}
