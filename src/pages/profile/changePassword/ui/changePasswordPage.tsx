import { password<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PATH } from "@/shared/config"
import { DashboardLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib"
import { resetLocalStorageData, useUserStore } from "@/shared/model"
import { Button, Input } from "@/shared/ui"
import { Typo<PERSON> } from "@mui/material"
import { useNavigate } from "react-router-dom"
import { toast } from "react-toastify"
import { useChangePasswordForm } from "../model/changePassword.form"
import { useChangePasswordMutation } from "../model/changePassword.hook"
import { ChangePasswordFormData } from "../model/changePassword.schema"

const ChangePasswordPage = () => {
  usePageTitle("Change Password")
  const { register, handleSubmit, errors, reset } = useChangePasswordForm()
  const navigate = useNavigate()
  const { setUserInfo } = useUserStore()

  const { mutate: changePassword, isPending } = useChangePasswordMutation({
    onSuccess: () => {
      toast.success("Password changed successfully")
      reset()
      resetLocalStorageData()
      setUserInfo(null)
      navigate(PATH.withoutAuth.login)
    },
    onError: (error) => {
      if (error.response?.data?.errors?.current_password?.[0]) {
        toast.error("Wrong current password")
      } else {
        toast.error(error.message)
      }
    },
  })

  const onSubmit = (data: ChangePasswordFormData) => {
    const changePasswordData = {
      current_password: data.current_password,
      new_password: data.confirm_new_password,
    }

    changePassword(changePasswordData)
  }

  return (
    <DashboardLayout title="Change Password" isLoading={isPending}>
      <div className="bg-white p-6 rounded-lg shadow-lg mt-24 max-w-lg mx-auto">
        <div className="flex flex-col items-center ">
          <Typography variant="h4" style={{ marginBottom: "1rem" }}>
            Change Password
          </Typography>
          <Typography
            variant="h6"
            style={{ marginBottom: "1rem", padding: "1rem" }}
            className="text-center"
          >
            After changing your password, you will be logged out and redirected
            to the login page
          </Typography>
          <form className="w-80" onSubmit={handleSubmit(onSubmit)}>
            <div className="mb-2">
              <Input
                {...register("current_password")}
                label="Current Password"
                type="password"
                variant="outlined"
                passwordInput
                error={!!errors.current_password}
                helperText={errors.current_password?.message}
              />
            </div>
            <div className="mb-2">
              <Input
                {...register("new_password")}
                label="New Password"
                type="password"
                variant="outlined"
                passwordInput
                error={!!errors.new_password}
                helperText={errors.new_password?.message || passwordHelperText}
              />
            </div>

            <div className="mb-2">
              <Input
                {...register("confirm_new_password")}
                label="Confirm New Password"
                type="password"
                variant="outlined"
                passwordInput
                error={!!errors.confirm_new_password}
                helperText={errors.confirm_new_password?.message}
              />
            </div>

            <Button
              className="!mb-4 py-6"
              type="submit"
              variant="contained"
              fullWidth
            >
              Change Password
            </Button>
          </form>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default ChangePasswordPage
