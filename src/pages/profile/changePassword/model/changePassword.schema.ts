import { passwordValidation } from "@/shared/config"
import * as yup from "yup"

export const changePasswordSchema = yup.object().shape({
  current_password: yup.string().required("Password is required"),
  new_password: passwordValidation.test(
    "not-same-as-current",
    "New password must be different from current password",
    function (value) {
      return value !== this.parent.current_password
    }
  ),
  confirm_new_password: yup
    .string()
    .oneOf([yup.ref("new_password")], "Passwords must match")
    .required("Confirm password is required"),
})

export type ChangePasswordFormData = yup.InferType<typeof changePasswordSchema>
