import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  ChangePasswordFormData,
  changePasswordSchema,
} from "./changePassword.schema"

export const useChangePasswordForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ChangePasswordFormData>({
    resolver: yupResolver(changePasswordSchema),
  })

  return {
    register,
    handleSubmit,
    errors,
    reset
  }
}