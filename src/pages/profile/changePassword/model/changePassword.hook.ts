import { useMutation, UseMutationOptions } from "@tanstack/react-query"
import { changePassword } from "../api/index"
import { ChangePasswordRequestDto } from "../api/dto/changePassword.dto"
import { ApiError } from "@/shared/model"

export const useChangePasswordMutation = (
  options?: UseMutationOptions<void, ApiError, ChangePasswordRequestDto>
) => {
  return useMutation({
    mutationFn: (data: ChangePasswordRequestDto) => changePassword(data),
    ...options,
  })
}