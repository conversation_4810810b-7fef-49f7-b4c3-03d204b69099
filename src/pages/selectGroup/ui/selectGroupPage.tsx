import { useCallback, useEffect, useState, useMemo } from "react"
import { useNavigate } from "react-router"
import { toast } from "react-toastify"

import { useGetCampaignGroupsByCampaignId } from "@/entities/campaignGroup"
import { useSelfAddCampaignGroupMember } from "@/entities/campaignGroupMember"
import { usePageTitle } from "@/shared/lib"
import {
  CampaignRole,
  getInitialCampaignRouteByRole,
  useCampaignStore,
} from "@/shared/model"

import { DashboardLayout } from "@/shared/layouts"
import { CampaignGroupEmptyState } from "@/pages/selectGroup/ui/emptyState"
import { CampaignGroupsListState } from "@/pages/selectGroup/ui/listState"
import { PATH } from "@/shared/config"

const SelectGroupPage = () => {
  usePageTitle("Select Group")
  const navigate = useNavigate()
  const { selectedCampaign, setSelectedCampaign } = useCampaignStore()

  const [searchTerm, setSearchTerm] = useState("")
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null)

  const { data: campaignGroups, isLoading } = useGetCampaignGroupsByCampaignId(
    selectedCampaign?.campaign_id || 0
  )

  const { mutate: selfAddCampaignGroupMember, isPending: isAddingMember } =
    useSelfAddCampaignGroupMember({
      onSuccess: () => {
        toast.success("Successfully joined the group!")
        if (selectedCampaign) {
          const url = getInitialCampaignRouteByRole(
            selectedCampaign.campaign_role_name as CampaignRole
          )
          navigate(url(selectedCampaign.campaign_id))
        }
      },
      onError: (error) => {
        toast.error(
          error.response?.data.message ??
            "Failed to join the group. Please try again."
        )
      },
    })

  const filteredGroups = useMemo(() => {
    if (!campaignGroups) return []
    if (!searchTerm.trim())
      return campaignGroups.sort((a, b) =>
        a.group_name.localeCompare(b.group_name)
      )

    return campaignGroups
      .filter((group) =>
        group.group_name.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => a.group_name.localeCompare(b.group_name))
  }, [campaignGroups, searchTerm])

  const handleEnterGroup = useCallback(() => {
    if (
      !selectedCampaign ||
      !selectedGroupId ||
      !selectedCampaign.campaign_user_id
    )
      return

    selfAddCampaignGroupMember({
      campaignId: selectedCampaign.campaign_id,
      groupId: selectedGroupId,
      memberIds: [selectedCampaign.campaign_user_id],
    })
    setSelectedCampaign({
      ...selectedCampaign,
      campaign_group_id: selectedGroupId,
    })
  }, [
    selectedCampaign,
    selectedGroupId,
    selfAddCampaignGroupMember,
    setSelectedCampaign,
  ])

  const handleNoGroupsClick = useCallback(() => {
    if (!selectedCampaign) return

    const url = getInitialCampaignRouteByRole(
      selectedCampaign.campaign_role_name as CampaignRole
    )

    navigate(url(selectedCampaign.campaign_id))
  }, [selectedCampaign, navigate])

  useEffect(() => {
    if (isLoading || campaignGroups === undefined || isAddingMember) {
      return
    }

    // If there are no groups or already selected group auto navigate
    if (campaignGroups.length === 0 || selectedCampaign?.campaign_group_id) {
      handleNoGroupsClick()
      return
    }

    // Auto-select the only group and join it
    if (campaignGroups.length === 1) {
      setSelectedGroupId(campaignGroups[0].id)
      if (selectedCampaign?.campaign_user_id) {
        selfAddCampaignGroupMember({
          campaignId: selectedCampaign.campaign_id,
          groupId: campaignGroups[0].id,
          memberIds: [selectedCampaign.campaign_user_id],
        })
        setSelectedCampaign({
          ...selectedCampaign,
          campaign_group_id: campaignGroups[0].id,
        })
      }
    }
  }, [
    campaignGroups,
    isLoading,
    isAddingMember,
    handleNoGroupsClick,
    selectedCampaign,
    selfAddCampaignGroupMember,
    setSelectedCampaign,
  ])

  // Redirect to campaign selection if no campaign is selected
  useEffect(() => {
    if (!selectedCampaign) {
      navigate(PATH.withAuth.selectCampaign)
    }
  }, [selectedCampaign, navigate])

  if (!selectedCampaign) {
    return null
  }

  return (
    <DashboardLayout isLoading={isLoading || isAddingMember}>
      {(campaignGroups?.length && (
        <CampaignGroupsListState
          campaignGroups={filteredGroups}
          selectedGroupId={selectedGroupId}
          onGroupSelect={setSelectedGroupId}
          onEnterGroup={handleEnterGroup}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          campaignName={selectedCampaign.campaign_name}
          isLoading={isAddingMember}
        />
      )) || (
        <CampaignGroupEmptyState
          onContinue={handleNoGroupsClick}
          campaignName={selectedCampaign.campaign_name}
        />
      )}
    </DashboardLayout>
  )
}

export default SelectGroupPage
