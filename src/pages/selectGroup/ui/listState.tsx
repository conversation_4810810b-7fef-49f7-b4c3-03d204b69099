import { Typo<PERSON>, TextField, InputAdornment, Box } from "@mui/material"
import SearchIcon from "@mui/icons-material/Search"
import SimpleBar from "simplebar-react"
import "simplebar-react/dist/simplebar.min.css"

import { usePageTitle } from "@/shared/lib"
import { CampaignGroupDto } from "@/entities/campaignGroup"
import { Button } from "@/shared/ui"
import { Dispatch, SetStateAction } from "react"

interface CampaignGroupsListStateProps {
  campaignGroups: CampaignGroupDto[]
  selectedGroupId: number | null
  onGroupSelect: (groupId: number) => void
  onEnterGroup: () => void
  searchTerm: string
  onSearchChange: Dispatch<SetStateAction<string>>
  campaignName: string
  isLoading?: boolean
}

export const CampaignGroupsListState = ({
  campaignGroups,
  selectedGroupId,
  onGroupSelect,
  onEnterGroup,
  searchTerm,
  onSearchChange,
  isLoading = false,
}: CampaignGroupsListStateProps) => {
  usePageTitle("Select Group")

  return (
    <div className="flex min-w-screen items-center justify-center h-[calc(100vh-56px)] md:h-[calc(100vh-64px)] bg-gradient-to-br from-[#ec7b1a] to-[#FBBF24] relative before:absolute before:top-0 before:left-0 before:right-0 before:h-4 before:bg-gradient-to-b before:from-black/20 before:to-transparent before:pointer-events-none">
      <div className="bg-white p-8 rounded-2xl shadow-lg shadow-black/20 w-full max-w-md mx-4">
        <h2 className="text-2xl font-bold mb-2 text-center text-gray-800">
          Your Groups
        </h2>
        <Typography className="!mb-6 text-center !text-gray-500">
          Select a group to join
        </Typography>

        <TextField
          fullWidth
          placeholder="Search groups..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="!mb-6"
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon className="text-gray-400" />
                </InputAdornment>
              ),
            },
          }}
        />

        <SimpleBar
          autoHide={false}
          style={{ maxHeight: 280, marginBottom: 50 }}
        >
          <div className="space-y-3 mb-6 max-h-80">
            {campaignGroups.map((group) => (
              <Box
                key={group.id}
                onClick={() => onGroupSelect(group.id)}
                className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                  selectedGroupId === group.id
                    ? "border-orange-500 bg-orange-50"
                    : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                }`}
              >
                <Typography className="text-lg font-bold text-gray-800">
                  {group.group_name}
                </Typography>
              </Box>
            ))}
          </div>
        </SimpleBar>

        <Button
          fullWidth
          variant="contained"
          onClick={onEnterGroup}
          disabled={!selectedGroupId || isLoading}
          className="!py-3 !rounded-xl !text-lg !font-semibold"
          type="button"
          size="large"
        >
          {isLoading ? "Joining Group..." : "Enter Group"}
        </Button>
      </div>
    </div>
  )
}

export default CampaignGroupsListState
