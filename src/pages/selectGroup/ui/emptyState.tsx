import GroupIcon from "@mui/icons-material/Group"
import { Box, Typography } from "@mui/material"
import { Button } from "@/shared/ui"

export const CampaignGroupEmptyState = ({
  onContinue,
  campaignName,
}: {
  onContinue: () => void
  campaignName: string
}) => (
  <div className="flex min-w-screen items-center justify-center h-[calc(100vh-64px)] bg-gray-100 relative">
    <div className="bg-white p-8 rounded-2xl shadow-lg w-full max-w-md mx-4">
      <h2 className="text-2xl font-bold mb-2 text-center text-gray-800">
        Your Groups
      </h2>
      <Typography className="!mb-6 text-center text-gray-600">
        No groups found for <strong>{campaignName}</strong>
      </Typography>

      <Box className="flex flex-col items-center justify-center p-8">
        <GroupIcon className="text-gray-400 mb-4" sx={{ fontSize: 64 }} />
        <Typography variant="h6" className="mb-2 text-gray-700">
          No Groups Available
        </Typography>
        <Typography color="text.secondary" className="text-center mb-6">
          This campaign doesn't have any groups set up yet.
        </Typography>
        <Button
          variant="contained"
          onClick={onContinue}
          fullWidth
          className="!py-3 !rounded-xl !text-lg !font-semibold"
          sx={{
            backgroundColor: '#fd7e14',
            '&:hover': {
              backgroundColor: '#e8590c',
            },
          }}
        >
          Continue to Campaign
        </Button>
      </Box>
    </div>
  </div>
)

export default CampaignGroupEmptyState
