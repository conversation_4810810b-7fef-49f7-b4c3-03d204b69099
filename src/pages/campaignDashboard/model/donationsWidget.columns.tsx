import { GridRenderCellParams } from "@mui/x-data-grid"
import { format } from "date-fns"

type DonationMethod = "credit_card" | "other"

const DONATION_METHODS: Record<DonationMethod, string> = {
  credit_card: "Credit Card",
  other: "Other",
}

const formatDate = (date: string | null): string => {
  return date ? format(new Date(date), "MM/dd/yyyy hh:mm a") : ""
}

const formatMemberName = (row: any): string => {
  return row.who_invited_first_name
    ? `${row.who_invited_first_name} ${row.who_invited_last_name} (${row.who_invited_role})`
    : ""
}

const formatDonorName = (row: any): string => {
  return row.donor_first_name
    ? `${row.donor_first_name} ${row.donor_last_name}`
    : ""
}

const formatDonationMethod = (method: string | null): string => {
  return method ? DONATION_METHODS[method as DonationMethod] || "" : ""
}

export const getColumns = (isDetailedView: boolean) => {
  const baseColumns = [
    {
      field: "created_at",
      headerName: "Date",
      minWidth: 180,
      flex: 1,
      sortable: true,
      filterable: false,
      renderCell: (params: GridRenderCellParams) =>
        formatDate(params.row.created_at),
    },
    {
      field: "member",
      headerName: "Member",
      flex: 1,
      sortable: true,
      filterable: false,
      minWidth: 220,
      renderCell: (params: GridRenderCellParams) =>
        formatMemberName(params.row),
    },
    {
      field: "donor_first_name",
      headerName: "Donor",
      minWidth: 220,
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => formatDonorName(params.row),
    },
    {
      field: "base_donation_amount",
      headerName: "Amount",
      minWidth: 100,
      flex: 1,
      sortable: false,
      filterable: false,
    },
  ]

  const detailedColumns = [
    {
      field: "donation_method",
      headerName: "Donation Type",
      flex: 1,
      minWidth: 100,
      sortable: true,
      filterable: false,
      renderCell: (params: GridRenderCellParams) =>
        formatDonationMethod(params.row.donation_method),
    },
  ]

  return isDetailedView ? [...baseColumns, ...detailedColumns] : baseColumns
}
