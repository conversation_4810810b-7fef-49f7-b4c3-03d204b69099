import { yupResolver } from "@hookform/resolvers/yup"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { donationFilterSchema } from "./donationFilter.schema"

interface DonationFilterFormData {
  date_from?: Date
  date_to?: Date
  donation_method: {
    label?: string
    value?: string
  } | null
  member_name: {
    label?: string
    value?: string
  } | null
}

export const useDonationFilterForm = (
  defaultValues?: DonationFilterFormData
) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    getValues,
    setValue,
  } = useForm<DonationFilterFormData>({
    resolver: yupResolver(donationFilterSchema),
    defaultValues: {
      ...defaultValues,
    },
  })

  useEffect(() => {
    const { date_from, date_to } = getValues()

    if (!date_from && !date_to) {
      setValue("date_from", defaultValues?.date_from)
      setValue("date_to", defaultValues?.date_to)
    }
  }, [defaultValues, reset, getValues])

  return {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    getValues,
  }
}
