import {
  useExportCampaignDonations,
  useGetCampaignDonations,
} from "@/entities/campaignDonation"
import { getAllErrorMessages } from "@/shared/lib/getAllErrorMessages"
import { usePagination } from "@/shared/model"
import { GridSortModel } from "@mui/x-data-grid"
import { useEffect, useMemo } from "react"
import { toast } from "react-toastify"
import { v4 as uuidv4 } from "uuid"
import { getColumns } from "./donationsWidget.columns"

type UseTableDataProps = {
  campaignId: number
  isDetailedView: boolean
  sortModel: GridSortModel
  filterModel: {
    date_from: string | null
    date_to: string | null
    member_name: string | null
    donation_method: string | null
  }
}

export const useTableData = ({
  campaignId,
  isDetailedView,
  sortModel,
  filterModel,
}: UseTableDataProps) => {
  const { pageSize, page, onPaginationModelChange } = usePagination()

  const { data, isFetching, error } = useGetCampaignDonations(
    Number(campaignId),
    {
      page,
      per_page: pageSize,
      sort_by: sortModel[0]?.field,
      sort_order: sortModel[0]?.sort === "asc" ? "asc" : "desc",
      date_from: filterModel.date_from,
      date_to: filterModel.date_to,
      member_name: filterModel.member_name,
      donation_method: filterModel.donation_method,
    }
  )

  const { mutate: exportDonations } = useExportCampaignDonations()

  useEffect(() => {
    if (error) {
      const errorMessage = getAllErrorMessages(error)
      toast.error(errorMessage)
    }
  }, [error])

  const currentPage = data?.meta?.current_page ? data.meta.current_page - 1 : 0

  const totalPages = data?.meta?.total ? data.meta.total : 0

  const columns = useMemo(() => getColumns(isDetailedView), [isDetailedView])

  const rows = useMemo(
    () => data?.data?.map((item) => ({ ...item, id: uuidv4() })),
    [data]
  )

  return {
    columns,
    rows,
    isFetching,
    onPaginationModelChange,
    currentPage,
    totalPages,
    pageSize,
    exportDonations,
  }
}
