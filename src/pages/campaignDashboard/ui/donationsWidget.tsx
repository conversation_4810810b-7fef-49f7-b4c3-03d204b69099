import { useTableData } from "@/pages/campaignDashboard/model/useDonationData"
import { hexToRgba } from "@/shared/lib/hexToRgba"
import { Button, Dialog, Table } from "@/shared/ui"
import { Paper, Typography } from "@mui/material"
import { GridSortModel } from "@mui/x-data-grid"
import { useState } from "react"
import { DetailedDonationWidget } from "./detailedDonationWidget"

type DonationsWidgetProps = {
  campaignId: number
  colors: {
    primary: string
    secondary: string
  }
}

export const DonationsWidget = ({
  campaignId,
  colors,
}: DonationsWidgetProps) => {
  const [detailedView, setDetailedView] = useState(false)
  const [sortModel, setSortModel] = useState<GridSortModel>([
    {
      field: "created_at",
      sort: "desc",
    },
  ])
  const [filterModel, setFilterModel] = useState<{
    date_from: string | null
    date_to: string | null
    member_name: string | null
    donation_method: string | null
  }>({
    date_from: null,
    date_to: null,
    member_name: null,
    donation_method: null,
  })

  const {
    columns,
    rows,
    isFetching,
    onPaginationModelChange,
    currentPage,
    totalPages,
    pageSize,
    exportDonations,
  } = useTableData({
    campaignId,
    isDetailedView: detailedView,
    sortModel,
    filterModel,
  })

  const handleSortModelChange = (model: GridSortModel) => {
    setSortModel(model)
  }

  const handleOpenDetailedView = () => {
    setDetailedView(true)
  }

  const handleCloseDetailedView = () => {
    setFilterModel({
      date_from: null,
      date_to: null,
      member_name: null,
      donation_method: null,
    })
    setDetailedView(false)
  }

  const handleExport = () => {
    exportDonations({
      campaignId,
      params: {
        sort_by: sortModel[0]?.field,
        sort_order: sortModel[0]?.sort === "asc" ? "asc" : "desc",
        date_from: filterModel.date_from,
        date_to: filterModel.date_to,
        member_name: filterModel.member_name,
        donation_method: filterModel.donation_method,
      },
    })
  }

  const tableSx = {
    "& .MuiDataGrid-columnHeader": {
      backgroundColor: hexToRgba(colors.primary ?? "", 0.5),
    },
  }

  return (
    <Paper className="p-6 !shadow-md min-h-[515px]">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Typography
            className="!text-3xl !font-bold !tracking-tight"
            variant="h2"
          >
            Donations
          </Typography>
          <Button
            className="!w-30"
            variant="contained"
            onClick={handleOpenDetailedView}
          >
            Detailed View
          </Button>
        </div>
        <Table
          columns={columns}
          rows={rows || []}
          rowCount={totalPages}
          loading={isFetching}
          page={currentPage}
          onPaginationModelChange={onPaginationModelChange}
          pageSize={pageSize}
          boxClassName="h-[800px]"
          sx={tableSx}
          sortModel={sortModel}
          onSortModelChange={handleSortModelChange}
        />
      </div>
      <Dialog
        fullScreen
        title="Detailed Donations View"
        open={detailedView}
        onClose={handleCloseDetailedView}
      >
        <DetailedDonationWidget
          columns={columns}
          rows={rows || []}
          totalPages={totalPages}
          isFetching={isFetching}
          currentPage={currentPage}
          onPaginationModelChange={onPaginationModelChange}
          pageSize={pageSize}
          colors={colors}
          campaignId={campaignId}
          setFilterModel={setFilterModel}
          sortModel={sortModel}
          onSortModelChange={handleSortModelChange}
          handleExport={handleExport}
        />
      </Dialog>
    </Paper>
  )
}
