import {
  useGetCampaignByGuid,
  useGetCampaignById,
  useGetCampaignSummary,
} from "@/entities/campaign"
import { DashboardLayout } from "@/shared/layouts"
import { calculateDaysLeft } from "@/shared/lib/date"
import { calculateProgress } from "@/shared/lib/progress"
import { usePageTitle } from "@/shared/lib/usePageTitle"
import { DonationProgress } from "@/shared/ui/donationProgress"
import { LeaderBoard } from "@/widgets/leaderboard"
import { Typography } from "@mui/material"
import { useParams } from "react-router-dom"
import { DonationsWidget } from "./donationsWidget"
import CampaignSummaryWidget from "./campaignSummaryWidget"

const CoachDashboard = () => {
  usePageTitle("Coach Dashboard")

  const { campaignId } = useParams<{ campaignId: string }>()
  const { data, isLoading: isLoadingCampaignById } = useGetCampaignById(
    Number(campaignId)
  )

  const { data: campaign, isLoading: isLoadingCampaign } = useGetCampaignByGuid(
    data?.guid as string,
    !!data?.guid
  )

  const { data: campaignSummary } = useGetCampaignSummary(Number(campaignId))

  const { total_raised, total_fees, total_profit, check_request_status } =
    campaignSummary ?? {}

  const colors = { primary: "#ec7b1a", secondary: "#ad4120" }

  const campaignEndDate = data?.campaign_end_date
    ? new Date(data?.campaign_end_date)
    : null
  const daysLeft = calculateDaysLeft(campaignEndDate)
  const progressInfo = {
    raised: campaign?.donations_raised ?? 0,
    goal: Number(campaign?.fundraising_goal || 0),
    daysLeft: daysLeft,
    progress: calculateProgress(
      campaign?.donations_raised ?? 0,
      Number(campaign?.fundraising_goal || 0)
    ),
  }

  const isLoading = isLoadingCampaign || isLoadingCampaignById

  return (
    <DashboardLayout isLoading={isLoading}>
      <Typography
        className="!text-3xl !font-bold !tracking-tight p-4 w-full text-center"
        variant="h2"
      >
        {campaign?.name}
      </Typography>
      <DonationProgress
        progressInfo={progressInfo}
        colors={colors}
        isDashboard
      />
      <div className="grid grid-cols-1 md:grid-cols-2 mx-[30px] py-6 gap-4">
        <div>
          <CampaignSummaryWidget
            total_raised={total_raised}
            total_fees={total_fees}
            total_profit={total_profit}
            check_request_status={check_request_status}
          />

          <LeaderBoard
            isDashboard
            campaignId={Number(campaignId)}
            setLeaderboardPage={() => {}}
            setLeaderboardTab={() => {}}
          />
        </div>
        <DonationsWidget campaignId={Number(campaignId)} colors={colors} />
      </div>
    </DashboardLayout>
  )
}

export default CoachDashboard
