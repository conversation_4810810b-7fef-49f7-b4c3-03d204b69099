import { useGetCampaignDonationsFilters } from "@/entities/campaignDonation"
import { hexToRgba } from "@/shared/lib/hexToRgba"
import { Autocomplete, Button, Table } from "@/shared/ui"
import { DatePickerField } from "@/shared/ui/DatePickerField"
import {
  GridColDef,
  GridPaginationModel,
  GridSortModel,
} from "@mui/x-data-grid"
import { useDonationFilterForm } from "../model/donationFilter.form"
import { format } from "date-fns"

interface DetailedDonationWidgetProps {
  columns: GridColDef[]
  rows: any[]
  totalPages: number
  isFetching: boolean
  currentPage: number
  onPaginationModelChange: (params: GridPaginationModel) => void
  pageSize: number
  colors: {
    primary: string
    secondary: string
  }
  campaignId: number
  setFilterModel: (model: {
    date_from: string | null
    date_to: string | null
    member_name: string | null
    donation_method: string | null
  }) => void
  sortModel: GridSortModel
  onSortModelChange: (model: GridSortModel) => void
  handleExport: () => void
}

export const DetailedDonationWidget = ({
  columns,
  rows,
  totalPages,
  isFetching,
  currentPage,
  onPaginationModelChange,
  pageSize,
  colors,
  campaignId,
  setFilterModel,
  sortModel,
  onSortModelChange,
  handleExport,
}: DetailedDonationWidgetProps) => {
  const { data: filters, isLoading } =
    useGetCampaignDonationsFilters(campaignId)

  const donationMethods =
    filters?.donation_methods?.map((method) => ({
      label: method
        .split("_")
        .map((word) => word[0].toUpperCase() + word.slice(1))
        .join(" "),
      value: method,
    })) ?? []

  const members =
    filters?.members?.map((member) => ({
      label: member,
      value: member,
    })) ?? []

  const { control, handleSubmit, reset, getValues } = useDonationFilterForm({
    date_from: filters?.date_range?.min_date
      ? new Date(filters?.date_range?.min_date)
      : undefined,
    date_to: filters?.date_range?.max_date
      ? new Date(filters?.date_range?.max_date)
      : undefined,
    donation_method: null,
    member_name: null,
  })

  const handleReset = () => {
    reset()
    const { date_from, date_to, member_name, donation_method } = getValues()
    setFilterModel({
      date_from: date_from ? format(date_from, "yyyy-MM-dd") : null,
      date_to: date_to ? format(date_to, "yyyy-MM-dd") : null,
      member_name: member_name?.value ?? null,
      donation_method: donation_method?.value ?? null,
    })
  }

  const onSubmit = (data: any) => {
    setFilterModel({
      date_from: format(data.date_from, "yyyy-MM-dd"),
      date_to: format(data.date_to, "yyyy-MM-dd"),
      member_name: data.member_name?.value,
      donation_method: data.donation_method?.value,
    })
  }

  const handleExportClick = () => {
    handleExport()
  }

  const tableSx = {
    "& .MuiDataGrid-columnHeader": {
      backgroundColor: hexToRgba(colors.primary ?? "", 0.5),
    },
  }
  return (
    <>
      <form
        className="flex flex-wrap gap-4 mb-12 md:mb-4 justify-between"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex flex-wrap gap-4">
          <div className="w-full mb-4 md:mb-0 md:w-48">
            <DatePickerField
              name="date_from"
              label="Date From"
              control={control}
              fieldVariant="standard"
            />
          </div>
          <div className="w-full mb-4 md:mb-0 md:w-48">
            <DatePickerField
              name="date_to"
              label="Date To"
              control={control}
              fieldVariant="standard"
            />
          </div>

          <div className="w-full md:w-48">
            <Autocomplete
              name="member_name"
              label="Member Name"
              options={members}
              control={control}
              isLoading={isLoading}
              fieldVariant="standard"
            />
          </div>

          <div className="w-full md:w-48">
            <Autocomplete
              name="donation_method"
              label="Donation Method"
              options={donationMethods}
              control={control}
              isLoading={isLoading}
              fieldVariant="standard"
            />
          </div>
          <div className="flex flex-col md:flex-row w-full md:w-auto gap-4 py-5">
            <div className="w-full md:w-24 flex items-end">
              <Button
                variant="contained"
                color="primary"
                type="button"
                onClick={handleReset}
              >
                Reset
              </Button>
            </div>

            <div className="h-full w-full md:w-24 flex items-end">
              <Button variant="contained" color="primary" type="submit">
                Apply
              </Button>
            </div>
          </div>
        </div>

        <div className="h-full w-full md:w-24 flex items-end pt-5">
          <Button
            variant="contained"
            color="primary"
            type="button"
            onClick={handleExportClick}
          >
            Export
          </Button>
        </div>
      </form>
      <Table
        columns={columns}
        rows={rows || []}
        rowCount={totalPages}
        loading={isFetching}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
        boxClassName=""
        sx={tableSx}
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
      />
    </>
  )
}
