import { Typography, Box } from "@mui/material"

import { usePageTitle } from "@/shared/lib"
import { UserCampaignDto } from "@/entities/userCampaign"
import { Button } from "@/shared/ui"

export const UserCampaignsListState = ({
  userCampaigns,
  selectedCampaignId,
  onCampaignSelect,
  onEnterCampaign,
  isLoading = false,
}: {
  userCampaigns: UserCampaignDto[]
  selectedCampaignId: number | null
  onCampaignSelect: (campaignId: number) => void
  onEnterCampaign: () => void
  isLoading?: boolean
}) => {
  usePageTitle("Select Campaign")

  return (
    <div className="flex min-w-screen items-center justify-center h-[calc(100vh-56px)] md:h-[calc(100vh-64px)] bg-gradient-to-br from-[#ec7b1a] to-[#FBBF24] relative before:absolute before:top-0 before:left-0 before:right-0 before:h-4 before:bg-gradient-to-b before:from-black/20 before:to-transparent before:pointer-events-none">
      <div className="bg-white p-8 rounded-2xl shadow-lg shadow-black/20 w-full max-w-md mx-4">
        <h2 className="text-2xl font-bold mb-2 text-center text-gray-800">
          Your Campaigns
        </h2>
        <Typography className="!mb-6 text-center !text-gray-500">
          Select a campaign to login to
        </Typography>

        <div
          className="space-y-3 mb-6 max-h-90 overflow-y-auto scrollbar-thin"
          style={{
            scrollbarWidth: "thin",
            scrollbarColor: "#9CA3AF #F3F4F6",
          }}
        >
          {userCampaigns.map((campaign) => (
            <Box
              key={campaign.campaign_id}
              onClick={() => onCampaignSelect(campaign.campaign_id)}
              className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                selectedCampaignId === campaign.campaign_id
                  ? "border-orange-500 bg-orange-50"
                  : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
              }`}
            >
              <Typography className="text-lg font-bold text-gray-800 truncate">
                {campaign.campaign_name}
              </Typography>
              <Typography color="text.secondary" variant="body2">
                {campaign.campaign_role_name}
              </Typography>
            </Box>
          ))}
        </div>

        <Button
          fullWidth
          variant="contained"
          onClick={onEnterCampaign}
          disabled={!selectedCampaignId || isLoading}
          className="!py-3 !rounded-xl !text-lg !font-semibold"
          type="button"
          size="large"
        >
          {isLoading ? "Entering Campaign..." : "Enter Campaign"}
        </Button>
      </div>
    </div>
  )
}

export default UserCampaignsListState
