import { AuthLayout } from "@/shared/layouts"
import CampaignIcon from "@mui/icons-material/Campaign"
import { Box, Paper, Typography } from "@mui/material"

export const UserCampaignEmptyState = ({
  isLoading,
}: {
  isLoading: boolean
}) => (
  <AuthLayout disableHeader title="Your Campaigns" isLoading={isLoading}>
    <Typography className="!mb-6 text-center">
      Select a Campaign to log in to.
    </Typography>
    <Paper elevation={0} variant="outlined">
      <Box className="flex flex-col items-center justify-center p-8">
        <CampaignIcon className="text-gray-400 mb-4" sx={{ fontSize: 64 }} />
        <Typography variant="h6" className="mb-2 text-gray-700">
          No Active Campaigns
        </Typography>
        <Typography color="text.secondary" className="text-center">
          You don't have any active campaigns at the moment.
        </Typography>
      </Box>
    </Paper>
  </AuthLayout>
)
