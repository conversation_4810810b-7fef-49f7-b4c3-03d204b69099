import { useCallback, useEffect, useState } from "react"
import { useNavigate } from "react-router"

import { useGetUserCampaigns } from "@/entities/userCampaign"
import { usePageTitle } from "@/shared/lib"
import {
  CampaignRole,
  campaignRoleIdToCampaignRole,
  getInitialCampaignRouteByRole,
  useCampaignStore,
} from "@/shared/model"

import { PATH } from "@/shared/config"
import { DashboardLayout } from "@/shared/layouts"
import { UserCampaignEmptyState } from "./emptyState"
import { UserCampaignsListState } from "./listState"

const SelectCampaignPage = () => {
  usePageTitle("Select Campaign")
  const { data: userCampaigns, isLoading } = useGetUserCampaigns()
  const navigate = useNavigate()
  const { setSelectedCampaign } = useCampaignStore()

  const [selectedCampaignId, setSelectedCampaignId] = useState<number | null>(
    null
  )
  const [isEntering, setIsEntering] = useState(false)

  const handleCampaignSelect = useCallback((campaignId: number) => {
    setSelectedCampaignId(campaignId)
  }, [])

  const handleEnterCampaign = useCallback(() => {
    if (!selectedCampaignId || !userCampaigns) return

    const campaign = userCampaigns.find(
      (c) => c.campaign_id === selectedCampaignId
    )
    if (!campaign) return

    setIsEntering(true)
    setSelectedCampaign(campaign)

    if (
      !campaign.campaign_group_id &&
      campaignRoleIdToCampaignRole[campaign.campaign_role_id] ===
        CampaignRole.Participant
    ) {
      navigate(PATH.withAuth.selectGroup)
      return
    }

    const url = getInitialCampaignRouteByRole(
      campaign.campaign_role_name as CampaignRole
    )

    navigate(url(campaign.campaign_id))
  }, [selectedCampaignId, userCampaigns, setSelectedCampaign, navigate])

  useEffect(() => {
    setSelectedCampaign(null)
    if (userCampaigns?.length === 1) {
      setSelectedCampaignId(userCampaigns[0].campaign_id)
      handleEnterCampaign()
    }
  }, [userCampaigns, setSelectedCampaign, handleEnterCampaign])

  return (
    <DashboardLayout isLoading={isLoading}>
      {(userCampaigns?.length && (
        <UserCampaignsListState
          userCampaigns={userCampaigns}
          selectedCampaignId={selectedCampaignId}
          onCampaignSelect={handleCampaignSelect}
          onEnterCampaign={handleEnterCampaign}
          isLoading={isEntering}
        />
      )) || <UserCampaignEmptyState isLoading={isLoading} />}
    </DashboardLayout>
  )
}

export default SelectCampaignPage
