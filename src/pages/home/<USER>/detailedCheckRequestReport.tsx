import { CircularProgress, Paper, SxProps, Typography, Checkbox } from "@mui/material"
import {
  GridColDef,
  GridPaginationModel,
  GridRowParams,
  GridRenderCellParams,
} from "@mui/x-data-grid"
import { toast } from "react-toastify"
import { useCheckRequestFilterForm } from "../model/useCheckRequestFilter.form"

import { useGetCheckRequestTotalAmount } from "@/entities/campaign"
import { CampaignFundStatus } from "@/entities/campaignFund"
import { getLocalDateString } from "@/shared/lib/date"
import { formatCurrency } from "@/shared/lib/formatCurrency"
import { Button, Select, Table, ConfirmDialog } from "@/shared/ui"
import { DatePickerField } from "@/shared/ui/DatePickerField"
import { useState, useMemo, useCallback } from "react"
import { CheckRequestFilterForm } from "../model/checkRequestFilter.schema"
import { useClearCheckRequests } from "../model/detailedCheckRequestReport.hooks"

interface DetailedCheckRequestReportProps {
  columns: GridColDef[]
  rows: any[]
  totalPages: number
  isFetching: boolean
  currentPage: number
  onPaginationModelChange: (params: GridPaginationModel) => void
  pageSize: number
  tableSx: SxProps
  setCheckRequestFilter: (filter: CheckRequestFilterForm) => void
  checkRequestFilter: CheckRequestFilterForm
  checkRequestFilterOptions: { label: string; value: string }[]
  onRowClick: (params: GridRowParams) => void
  onResetFilter: () => void
}

export const DetailedCheckRequestReport = ({
  columns,
  rows,
  totalPages,
  isFetching,
  currentPage,
  onPaginationModelChange,
  pageSize,
  tableSx,
  setCheckRequestFilter,
  checkRequestFilter,
  checkRequestFilterOptions,
  onRowClick,
  onResetFilter,
}: DetailedCheckRequestReportProps) => {
  const { control, handleSubmit, reset, watch, setValue } =
    useCheckRequestFilterForm({
      start_date: checkRequestFilter.start_date,
      end_date: checkRequestFilter.end_date,
      check_request_status: checkRequestFilter.check_request_status,
    })

  const { mutate: clearCheckRequests } = useClearCheckRequests({
    onSuccess: () => {
      toast.success("Check requests cleared successfully")
    },
  })
  
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)

  const processedRows = useMemo(() => {
    return rows?.filter(row => row.request_status === CampaignFundStatus.processed) || []
  }, [rows])

  const isAllSelected = processedRows.length > 0 && selectedRows.size === processedRows.length

  const isIndeterminate = selectedRows.size > 0 && selectedRows.size < processedRows.length

  const handleRowSelect = useCallback((rowId: string) => {
    setSelectedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(rowId)) {
        newSet.delete(rowId)
      } else {
        newSet.add(rowId)
      }
      return newSet
    })
  }, [])

  const handleSelectAll = useCallback(() => {
    if (isAllSelected) {
      setSelectedRows(new Set())
    } else {
      setSelectedRows(new Set(processedRows.map(row => row.id)))
    }
  }, [isAllSelected, processedRows])

  const handleClearSelected = useCallback(() => {
    setConfirmDialogOpen(true)
  }, [])

  const handleConfirmClear = useCallback(() => {
    const selectedRowsData = rows?.filter(row => selectedRows.has(row.id)) || []
    clearCheckRequests({
        campaign_check_request_ids: selectedRowsData.map(row => row.id),
    })
    setConfirmDialogOpen(false)
    setSelectedRows(new Set())
  }, [rows, selectedRows, clearCheckRequests])

  const {
    data: checkRequestTotalAmount,
    isLoading: isLoadingCheckRequestTotalAmount,
  } = useGetCheckRequestTotalAmount(
    checkRequestFilter.start_date
      ? getLocalDateString(checkRequestFilter.start_date)
      : "",
    checkRequestFilter.end_date
      ? getLocalDateString(checkRequestFilter.end_date)
      : ""
  )

  const onSubmit = (data: CheckRequestFilterForm) => {
    setCheckRequestFilter({
      start_date: data.start_date,
      end_date: data.end_date,
      check_request_status: data.check_request_status,
    })
     if (checkRequestStatus === "pending") {
      setValue("start_date", null)
      setValue("end_date", null)
    }
    setSelectedRows(new Set())
  }

  const onReset = () => {
    reset()
    onResetFilter()
  }

  const checkRequestStatus = watch("check_request_status")

  const isDisabled = checkRequestStatus === "pending"

  const enhancedColumns = useMemo(() => {
    const checkboxColumn: GridColDef = {
      field: 'checkbox',
      headerName: '',
      width: 50,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderHeader: () => (
        <Checkbox
          checked={isAllSelected}
          indeterminate={isIndeterminate}
          onChange={handleSelectAll}
          disabled={processedRows.length === 0}
        />
      ),
      renderCell: (params: GridRenderCellParams) => {
        const isProcessed = params.row.request_status === CampaignFundStatus.processed
        if (!isProcessed) return null

        return (
          <Checkbox
            checked={selectedRows.has(params.row.id)}
            onClick={(e) => {
              e.stopPropagation()
            }}
            onChange={() => handleRowSelect(params.row.id)}
          />
        )
      },
    }

    return [checkboxColumn, ...columns]
  }, [columns, isAllSelected, isIndeterminate, selectedRows, processedRows.length, handleSelectAll, handleRowSelect])

  return (
    <>
      <Paper className="p-4 mb-4 w-1/3">
        <Typography className="!text-lg">
          {isLoadingCheckRequestTotalAmount ? (
            <CircularProgress size={20} className="ml-2" />
          ) : (
            <>
              <span className="block">
                <span>Total requested amount: </span>
                <span className="font-bold">
                  $
                  {formatCurrency(checkRequestTotalAmount?.requested ?? 0, {
                    maximumFractionDigits: 2,
                  })}
                </span>
              </span>
              <span className="block">
                <span>Total processed amount: </span>
                <span className="font-bold">
                  $
                  {formatCurrency(checkRequestTotalAmount?.processed ?? 0, {
                    maximumFractionDigits: 2,
                  })}
                </span>
              </span>
              <span className="block">
                <span>Total cleared amount: </span>
                <span className="font-bold">
                  $
                  {formatCurrency(checkRequestTotalAmount?.cleared ?? 0, {
                    maximumFractionDigits: 2,
                  })}
                </span>
              </span>
              <span className="block text-xs">
                * The total amount is calculated based on the selected date
                range.
              </span>
            </>
          )}
        </Typography>
      </Paper>
      <form
        className="flex w-full flex-wrap gap-4 mb-12 md:mb-4 justify-between"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex w-full flex-wrap gap-4">
          <div className="w-full mb-4 md:mb-0 md:w-48">
            <DatePickerField
              name="start_date"
              label="Date From"
              control={control}
              fieldVariant="standard"
              disabled={isDisabled}
            />
          </div>
          <div className="w-full mb-4 md:mb-0 md:w-48">
            <DatePickerField
              name="end_date"
              label="Date To"
              control={control}
              fieldVariant="standard"
              disabled={isDisabled}
            />
          </div>
          <div className="w-full md:w-48">
            <Select
              name="check_request_status"
              label="Check Request Status"
              options={checkRequestFilterOptions}
              control={control}
              fieldVariant="standard"
            />
          </div>

          <div className="flex flex-col md:flex-row w-full md:w-auto gap-4 py-5">
            <Button variant="contained" color="primary" type="submit">
              Apply
            </Button>
            <Button variant="contained" color="primary" onClick={onReset}>
              Reset
            </Button>
            {selectedRows.size > 0 && (
              <Button
                variant="contained"
                color="secondary"
                onClick={handleClearSelected}
              >
                Clear
              </Button>
            )}
          </div>
        </div>
      </form>
      <Table
        columns={enhancedColumns}
        rows={rows || []}
        rowCount={totalPages}
        loading={isFetching}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
        boxClassName=""
        sx={tableSx}
        onRowClick={onRowClick}
      />
      <ConfirmDialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={handleConfirmClear}
        title="Clear Selected Requests"
        content={`Are you sure you want to clear ${selectedRows.size} selected check request${selectedRows.size === 1 ? '' : 's'}?`}
        confirmText="Clear"
        cancelText="Cancel"
      />
    </>
  )
}
