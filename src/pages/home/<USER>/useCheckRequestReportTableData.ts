import { useGetCheckRequests } from "@/entities/campaign"
import { usePagination } from "@/shared/model"
import { format } from "date-fns"
import { useMemo } from "react"
import { getColumns } from "./checkRequest.columns"
import { CheckRequestFilterForm } from "./checkRequestFilter.schema"

interface UseCheckRequestReportTableDataProps {
  isDetailedView: boolean
  checkRequestFilter: CheckRequestFilterForm
}

export const useCheckRequestReportTableData = ({
  isDetailedView,
  checkRequestFilter,
}: UseCheckRequestReportTableDataProps) => {
  const { pageSize, page, onPaginationModelChange } = usePagination()

  const { data: checkRequests, isLoading: isLoadingCheckRequests } =
    useGetCheckRequests({
      page: isDetailedView ? page : 1,
      per_page: isDetailedView ? pageSize : 10,
      start_date: checkRequestFilter.start_date
        ? format(checkRequestFilter.start_date, "yyyy-MM-dd")
        : undefined,
      end_date: checkRequestFilter.end_date
        ? format(checkRequestFilter.end_date, "yyyy-MM-dd")
        : undefined,
      check_request_status: checkRequestFilter.check_request_status,
    })

  const currentPage = checkRequests?.current_page
    ? checkRequests.current_page - 1
    : 0

  const totalPages = checkRequests?.total ? checkRequests.total : 0

  const rows = useMemo(
    () => checkRequests?.data?.map((item) => ({ ...item })),
    [checkRequests]
  )

  const status = checkRequestFilter.check_request_status

  const columns = useMemo(
    () => getColumns(isDetailedView, status),
    [isDetailedView, status]
  )

  return {
    checkRequests,
    isLoadingCheckRequests,
    columns,
    rows,
    currentPage,
    totalPages,
    pageSize,
    onPaginationModelChange,
  }
}
