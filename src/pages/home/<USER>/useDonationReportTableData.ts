import {
  DonationReportFilter,
  useGetCampaignDonationsReport,
} from "@/entities/campaign"
import { usePagination } from "@/shared/model"
import { format } from "date-fns"
import { useMemo } from "react"
import { v4 as uuidv4 } from "uuid"
import { getColumns } from "./donationReport.columns"

interface UseDonationReportTableDataProps {
  isDetailedView: boolean
  donationReportFilter: DonationReportFilter
}

export const useDonationReportTableData = ({
  isDetailedView,
  donationReportFilter,
}: UseDonationReportTableDataProps) => {
  const { pageSize, page, onPaginationModelChange } = usePagination()

  const { data: donationsReport, isLoading: isLoadingDonationsReport } =
    useGetCampaignDonationsReport({
      page: isDetailedView ? page : 1,
      per_page: isDetailedView ? pageSize : 10,
      start_date: format(donationReportFilter.date_from, "yyyy-MM-dd"),
      end_date: format(donationReportFilter.date_to, "yyyy-MM-dd"),
    })

  const currentPage = donationsReport?.current_page
    ? donationsReport.current_page - 1
    : 0

  const totalPages = donationsReport?.total ? donationsReport.total : 0

  const rows = useMemo(
    () => donationsReport?.data?.map((item) => ({ ...item, id: uuidv4() })),
    [donationsReport]
  )

  const columns = useMemo(() => getColumns(isDetailedView), [isDetailedView])

  return {
    donationsReport,
    isLoadingDonationsReport,
    columns,
    rows,
    currentPage,
    totalPages,
    pageSize,
    onPaginationModelChange,
  }
}
