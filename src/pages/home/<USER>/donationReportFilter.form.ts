import { DonationReportFilter } from "@/entities/campaign"
import { yupResolver } from "@hookform/resolvers/yup"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { donationReportFilterSchema } from "./donationReportFilter.schema"

export const useDonationReportFilterForm = (
  defaultValues: DonationReportFilter
) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    getValues,
    setValue,
  } = useForm<DonationReportFilter>({
    resolver: yupResolver(donationReportFilterSchema),
    defaultValues,
  })

  useEffect(() => {
    const { date_from, date_to } = getValues()

    if (!date_from && !date_to) {
      setValue("date_from", defaultValues?.date_from)
      setValue("date_to", defaultValues?.date_to)
    }
  }, [defaultValues, reset, getValues])

  return {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    getValues,
  }
}
