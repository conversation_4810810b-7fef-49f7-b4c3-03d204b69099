import { useGetCheckRequestFilters } from "@/entities/campaign"
import { PATH } from "@/shared/config"
import { hexToRgba } from "@/shared/lib/hexToRgba"
import { Button, Dialog, Table } from "@/shared/ui"
import { Paper, Typography } from "@mui/material"
import { GridRowParams } from "@mui/x-data-grid"
import { useCallback, useState } from "react"
import { useNavigate } from "react-router-dom"
import { CheckRequestFilterForm } from "../model/checkRequestFilter.schema"
import { useCheckRequestReportTableData } from "../model/useCheckRequestReportTableData"
import { DetailedCheckRequestReport } from "./detailedCheckRequestReport"

export const CheckRequestReport = () => {
  const navigate = useNavigate()
  const today = new Date()
  const thirtyDaysAgo = new Date(new Date().setDate(today.getDate() - 30))
  const [isDetailedView, setIsDetailedView] = useState(false)
  const [checkRequestFilter, setCheckRequestFilter] =
    useState<CheckRequestFilterForm>({
      start_date: thirtyDaysAgo,
      end_date: today,
      check_request_status: "requested",
    })

  const { data: checkRequestFilters } = useGetCheckRequestFilters()

  const checkRequestStatusOptions = checkRequestFilters?.statuses.map(
    (status) => ({
      label: status,
      value: status,
    })
  )

  const {
    isLoadingCheckRequests,
    columns,
    rows,
    currentPage,
    totalPages,
    pageSize,
    onPaginationModelChange,
  } = useCheckRequestReportTableData({ isDetailedView, checkRequestFilter })

  const colors = { primary: "#ec7b1a", secondary: "#ad4120" }

  const tableSx = {
    "& .MuiDataGrid-columnHeader": {
      backgroundColor: hexToRgba(colors.primary ?? "", 0.5),
    },
  }

  const handleOpenDetailedView = () => {
    setIsDetailedView(true)
  }

  const handleRowClick = (params: GridRowParams) => {
    navigate(PATH.withAuth.campaign.fund.list.url(params.row.campaign_id))
  }

  const handleReset = useCallback(() => {
    setCheckRequestFilter({
      start_date: thirtyDaysAgo,
      end_date: today,
      check_request_status: "requested",
    })
  }, [])

  const handleCloseDetailedView = () => {
    handleReset()
    setIsDetailedView(false)
  }

  return (
    <Paper className="p-4">
      {!isDetailedView ? (
        <>
          <div className="flex items-center justify-between mb-4">
            <Typography className="!text-xl !font-bold ">
              Check Requests
            </Typography>
            <Button
              className="!w-30"
              variant="contained"
              onClick={handleOpenDetailedView}
            >
              Detailed View
            </Button>
          </div>
          <Table
            columns={columns}
            rows={rows || []}
            rowCount={totalPages}
            loading={isLoadingCheckRequests}
            page={currentPage}
            onPaginationModelChange={onPaginationModelChange}
            pageSize={pageSize}
            sx={tableSx}
            hideFooter
            onRowClick={handleRowClick}
            boxClassName="!min-h-[110px]"
          />
        </>
      ) : (
        ""
      )}

      <Dialog
        fullScreen
        title="Detailed Check Request View"
        open={isDetailedView}
        onClose={handleCloseDetailedView}
      >
        <DetailedCheckRequestReport
          columns={columns}
          rows={rows || []}
          totalPages={totalPages}
          isFetching={isLoadingCheckRequests}
          currentPage={currentPage}
          onPaginationModelChange={onPaginationModelChange}
          pageSize={pageSize}
          tableSx={tableSx}
          setCheckRequestFilter={setCheckRequestFilter}
          checkRequestFilter={checkRequestFilter}
          checkRequestFilterOptions={checkRequestStatusOptions || []}
          onRowClick={handleRowClick}
          onResetFilter={handleReset}
        />
      </Dialog>
    </Paper>
  )
}
