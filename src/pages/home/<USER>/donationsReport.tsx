import { DonationReportFilter } from "@/entities/campaign"
import { hexToRgba } from "@/shared/lib/hexToRgba"
import { Button, Dialog, Table } from "@/shared/ui"
import { Paper, Typography } from "@mui/material"
import { useState } from "react"
import { useDonationReportTableData } from "../model/useDonationReportTableData"
import { DetailedDonationsReport } from "./detailedDonationsReport"

export const DonationsReport = () => {
  const today = new Date()
  const thirtyDaysAgo = new Date(new Date().setDate(today.getDate() - 30))
  const [isDetailedView, setIsDetailedView] = useState(false)
  const [donationReportFilter, setDonationReportFilter] =
    useState<DonationReportFilter>({
      date_from: thirtyDaysAgo,
      date_to: today,
    })

  const {
    columns,
    rows,
    isLoadingDonationsReport,
    currentPage,
    totalPages,
    pageSize,
    onPaginationModelChange,
  } = useDonationReportTableData({ isDetailedView, donationReportFilter })

  const handleOpenDetailedView = () => {
    setIsDetailedView(true)
  }

  const handleCloseDetailedView = () => {
    setIsDetailedView(false)
    setDonationReportFilter({
      date_from: thirtyDaysAgo,
      date_to: today,
    })
  }

  const colors = { primary: "#ec7b1a", secondary: "#ad4120" }

  const tableSx = {
    "& .MuiDataGrid-columnHeader": {
      backgroundColor: hexToRgba(colors.primary ?? "", 0.5),
    },
  }

  return (
    <Paper className="p-4 md:min-h-[657px]">
      <div className="flex items-center justify-between mb-4">
        <Typography className="!text-xl !font-bold ">Donations</Typography>
        <Button
          className="!w-30"
          variant="contained"
          onClick={handleOpenDetailedView}
        >
          Detailed View
        </Button>
      </div>
      <Table
        columns={columns}
        rows={rows || []}
        rowCount={totalPages}
        loading={isLoadingDonationsReport}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
        sx={tableSx}
        hideFooter
        boxClassName="h-[578px]"
      />
      <Dialog
        fullScreen
        title="Detailed Donations View"
        open={isDetailedView}
        onClose={handleCloseDetailedView}
      >
        <DetailedDonationsReport
          columns={columns}
          rows={rows || []}
          totalPages={totalPages}
          isFetching={isLoadingDonationsReport}
          currentPage={currentPage}
          onPaginationModelChange={onPaginationModelChange}
          pageSize={pageSize}
          tableSx={tableSx}
          setDonationReportFilter={setDonationReportFilter}
          donationReportFilter={donationReportFilter}
        />
      </Dialog>
    </Paper>
  )
}
