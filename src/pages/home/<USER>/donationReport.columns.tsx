import { formatCurrency } from "@/shared/lib/formatCurrency"
import { GridRenderCellParams } from "@mui/x-data-grid"
import { format } from "date-fns"

export const getColumns = (isDetailedView: boolean) => {
  const columns = [
    {
      field: "campaign_display_name",
      headerName: "Campaign",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
    },
    {
      field: "base_donation_amount",
      headerName: "Base Amount",
      flex: 1,
      minWidth: 200,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div className="h-full">
            $
            {formatCurrency(params.value, {
              maximumFractionDigits: 2,
            })}
          </div>
        )
      },
    },
    {
      field: "tip_amount",
      headerName: "Tip",
      minWidth: 100,
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div className="h-full">
            $
            {formatCurrency(params.value, {
              maximumFractionDigits: 2,
            })}
          </div>
        )
      },
    },
    {
      field: "total_donation_amount",
      headerName: "Total",
      minWidth: 150,
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div className="h-full">
            $
            {formatCurrency(params.value, {
              maximumFractionDigits: 2,
            })}
          </div>
        )
      },
    },
    {
      field: "created_at",
      headerName: "Date",
      minWidth: 250,
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return params.row.created_at
          ? format(new Date(params.row.created_at), "MM/dd/yyyy hh:mm a")
          : ""
      },
    },
  ]

  const detailedColumns = [
    {
      field: "campaign_id",
      headerName: "Campaign Id",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
    },
    {
      field: "campaign_display_name",
      headerName: "Campaign Display Name",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
    },
    {
      field: "base_donation_amount",
      headerName: "Base Amount",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div className="h-full">
            $
            {formatCurrency(params.value, {
              maximumFractionDigits: 2,
            })}
          </div>
        )
      },
    },
    {
      field: "tip_amount",
      headerName: "Tip",
      flex: 1,
      minWidth: 100,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div className="h-full">
            $
            {formatCurrency(params.value, {
              maximumFractionDigits: 2,
            })}
          </div>
        )
      },
    },
    {
      field: "total_donation_amount",
      headerName: "Total",
      flex: 1,
      minWidth: 150,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div className="h-full">
            $
            {formatCurrency(params.value, {
              maximumFractionDigits: 2,
            })}
          </div>
        )
      },
    },
    {
      field: "donor_full_name",
      headerName: "Payment Full Name",
      flex: 1,
      minWidth: 200,
      sortable: false,
      filterable: false,
    },
    {
      field: "donor_email",
      headerName: "Payment Email",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
    },
    {
      field: "donor_phone_number",
      headerName: "Payment Phone",
      flex: 1,
      minWidth: 150,
      sortable: false,
      filterable: false,
    },
    {
      field: "created_at",
      headerName: "Date",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return params.row.created_at
          ? format(new Date(params.row.created_at), "MM/dd/yyyy hh:mm a")
          : ""
      },
    },
  ]

  if (isDetailedView) {
    return detailedColumns
  }

  return columns
}
