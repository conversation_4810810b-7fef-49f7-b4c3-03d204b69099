import { CampaignFundStatus } from "@/entities/campaignFund"
import { GridRenderCellParams } from "@mui/x-data-grid"
import { format } from "date-fns"

export const getColumns = (isDetailedView: boolean, status: string) => {
  const processedDateColumn = {
    field: "processed_date",
    headerName: "Processed Date",
    flex: 1,
    minWidth: 130,
    sortable: false,
    filterable: false,
    renderCell: (params: GridRenderCellParams) => {
      return <div>{params.value ? format(params.value, "MM/dd/yyyy") : ""}</div>
    },
  }

  const clearedDateColumn = {
    field: "cleared_date",
    headerName: "Cleared Date",
    flex: 1,
    minWidth: 130,
    sortable: false,
    filterable: false,
    renderCell: (params: GridRenderCellParams) => {
      return <div>{params.value ? format(params.value, "MM/dd/yyyy") : ""}</div>
    },
  }
  const columns = [
    {
      field: "school_org_name",
      headerName: "Name of School/Org name",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
    },
    {
      field: "campaign_display_name",
      headerName: "Campaign/Team Display Name",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
    },
    {
      field: "check_amount",
      headerName: "Check Amount $",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
    },
    {
      field: "request_date",
      headerName: "Request Date",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div>{params.value ? format(params.value, "MM/dd/yyyy") : ""}</div>
        )
      },
    },
    ...(status === CampaignFundStatus.processed ? [processedDateColumn] : []),
    ...(status === CampaignFundStatus.cleared
      ? [processedDateColumn, clearedDateColumn]
      : []),

    {
      field: "recipient_name",
      headerName: "Receipt Name",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
    },
  ]

  const detailedColumns = [
    {
      field: "request_status",
      headerName: "Status",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
    },
    ...(status === CampaignFundStatus.cleared ||
    status === CampaignFundStatus.processed
      ? [
          {
            field: "check_number",
            headerName: "Check Number",
            flex: 1,
            minWidth: 120,
            sortable: false,
            filterable: false,
          },
        ]
      : []),
    {
      field: "check_address",
      headerName: "Check Address",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
    },
    {
      field: "memo",
      headerName: "Check Memo",
      flex: 1,
      minWidth: 250,
      sortable: false,
      filterable: false,
    },
    {
      field: "org_contact_email",
      headerName: "Org Contact - Email",
      flex: 1,
      minWidth: 200,
      sortable: false,
      filterable: false,
    },
    {
      field: "org_contact_phone",
      headerName: "Org Contact - Phone",
      flex: 1,
      minWidth: 180,
      sortable: false,
      filterable: false,
    },
  ]

  return isDetailedView ? [...columns, ...detailedColumns] : columns
}
