import { DonationReportFilter } from "@/entities/campaign"
import { Button, Table } from "@/shared/ui"
import { DatePickerField } from "@/shared/ui/DatePickerField"
import { SxProps } from "@mui/material"
import { GridColDef, GridPaginationModel } from "@mui/x-data-grid"
import { useDonationReportFilterForm } from "../model/donationReportFilter.form"

interface DetailedDonationsReportProps {
  columns: GridColDef[]
  rows: any[]
  totalPages: number
  isFetching: boolean
  currentPage: number
  onPaginationModelChange: (params: GridPaginationModel) => void
  pageSize: number
  tableSx: SxProps
  setDonationReportFilter: (filter: { date_from: Date; date_to: Date }) => void
  donationReportFilter: DonationReportFilter
}

export const DetailedDonationsReport = ({
  columns,
  rows,
  totalPages,
  isFetching,
  currentPage,
  onPaginationModelChange,
  pageSize,
  tableSx,
  setDonationReportFilter,
  donationReportFilter,
}: DetailedDonationsReportProps) => {
  const { control, handleSubmit } = useDonationReportFilterForm({
    date_from: donationReportFilter.date_from,
    date_to: donationReportFilter.date_to,
  })

  const onSubmit = (data: any) => {
    setDonationReportFilter({
      date_from: data.date_from,
      date_to: data.date_to,
    })
  }

  return (
    <>
      <form
        className="flex w-full flex-wrap gap-4 mb-12 md:mb-4 justify-between"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex w-full flex-wrap gap-4">
          <div className="w-full mb-4 md:mb-0 md:w-48">
            <DatePickerField
              name="date_from"
              label="Date From"
              control={control}
              fieldVariant="standard"
            />
          </div>
          <div className="w-full mb-4 md:mb-0 md:w-48">
            <DatePickerField
              name="date_to"
              label="Date To"
              control={control}
              fieldVariant="standard"
            />
          </div>

          <div className="flex flex-col md:flex-row w-full md:w-auto gap-4 py-5">
            <div className="h-full w-full md:w-24 flex items-end">
              <Button variant="contained" color="primary" type="submit">
                Apply
              </Button>
            </div>
          </div>
        </div>
      </form>
      <Table
        columns={columns}
        rows={rows || []}
        rowCount={totalPages}
        loading={isFetching}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
        boxClassName=""
        sx={tableSx}
      />
    </>
  )
}
