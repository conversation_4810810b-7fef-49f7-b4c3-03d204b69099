import { hexToRgba } from "@/shared/lib/hexToRgba"
import { Table } from "@/shared/ui"
import {
  MessageLogsFilterComponent,
  MessageLogsReportForm,
} from "@/widgets/messageLogsFilters"
import { Paper, Typography } from "@mui/material"
import { useState } from "react"
import { useMessageLogsReportTableData } from "../model/useMessageLogsReportTableData"

export const MessageLogsReport = () => {
  const today = new Date()
  const thirtyDaysAgo = new Date(new Date().setDate(today.getDate() - 30))

  const [messageLogsFilter, setMessageLogsFilter] =
    useState<MessageLogsReportForm>({
      start_date: thirtyDaysAgo,
      end_date: today,
      message_status: {
        label: "sent",
        value: "sent",
      },
    })

  const {
    isLoadingMessageLogs,
    columns,
    rows,
    currentPage,
    totalPages,
    pageSize,
    onPaginationModelChange,
  } = useMessageLogsReportTableData({ messageLogsFilter })

  const onSubmit = (data: MessageLogsReportForm) => {
    setMessageLogsFilter({
      start_date: data.start_date,
      end_date: data.end_date,
      message_status: data.message_status,
    })
  }

  const onReset = () => {
    setMessageLogsFilter({
      start_date: thirtyDaysAgo,
      end_date: today,
      message_status: {
        label: "sent",
        value: "sent",
      },
    })
  }

  const colors = { primary: "#ec7b1a", secondary: "#ad4120" }

  const tableSx = {
    "& .MuiDataGrid-columnHeader": {
      backgroundColor: hexToRgba(colors.primary ?? "", 0.5),
    },
  }

  return (
    <Paper className="p-4 w-full md:min-h-[657px]">
      <Typography className="!text-xl !font-bold !mb-4">
        Message Logs
      </Typography>
      <MessageLogsFilterComponent
        messageLogsFilter={messageLogsFilter}
        onSubmit={onSubmit}
        onReset={onReset}
      />
      <Table
        columns={columns}
        rows={rows || []}
        rowCount={totalPages}
        loading={isLoadingMessageLogs}
        page={currentPage}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
        sx={tableSx}
        boxClassName="h-[578px]"
      />
    </Paper>
  )
}
