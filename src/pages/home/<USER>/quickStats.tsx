import { QuickStatsDto } from "@/entities/campaign"
import { formatCurrency } from "@/shared/lib/formatCurrency"
import { CircularProgress, Paper, Typography } from "@mui/material"

interface QuickStatsProps {
  quickStats?: QuickStatsDto
  isLoading?: boolean
}

const quickStatsKeys = {
  today: "Today",
  yesterday: "Yesterday",
  last_7_days: "Last 7 Days",
  year_to_date: "Year to Date",
}

export const QuickStats = ({ quickStats, isLoading }: QuickStatsProps) => {
  return (
    <Paper className="p-4">
      <Typography className="!text-xl !font-bold !mb-4">Stats</Typography>
      <div>
        {isLoading ? (
          <div className="flex justify-center items-center h-[585px]">
            <CircularProgress />
          </div>
        ) : (
          Object.entries(quickStats || {}).map(([key, value]) => {
            const keyName = quickStatsKeys[key as keyof typeof quickStatsKeys]
            return (
              <div key={key} className="flex flex-col gap-2 !mb-6">
                <Typography className="text-sm !font-bold">
                  {keyName}
                </Typography>
                <Typography className="flex justify-between border-b border-dotted border-gray-300">
                  <span className="">Donations: </span>
                  <span className="font-semibold">
                    $
                    {formatCurrency(value.donations, {
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </Typography>
                <Typography className="flex justify-between border-b border-dotted border-gray-300">
                  <span className="">Tips: </span>
                  <span className="font-semibold">
                    $
                    {formatCurrency(value.tips, {
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </Typography>
                <Typography className="flex justify-between border-b border-dotted border-gray-300">
                  <span className="">Total Donations: </span>
                  <span className="font-semibold">
                    $
                    {formatCurrency(value.total_donations, {
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </Typography>
              </div>
            )
          })
        )}
      </div>
    </Paper>
  )
}
