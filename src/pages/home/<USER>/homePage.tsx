import { useGetQuickStats } from "@/entities/campaign"
import { CheckRequestReport } from "@/pages/home/<USER>/checkRequestReport"
import { DonationsReport } from "@/pages/home/<USER>/donationsReport"
import { MessageLogsReport } from "@/pages/home/<USER>/messageLogsReport"
import { QuickStats } from "@/pages/home/<USER>/quickStats"
import { DashboardLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib"
import { Typography } from "@mui/material"

const HomePage = () => {
  usePageTitle("Home")

  const { data: quickStats, isLoading: isLoadingQuickStats } =
    useGetQuickStats()

  return (
    <DashboardLayout title="Home">
      <div className="flex flex-col items-center justify-center p-8">
        <Typography variant="h4" className="mb-4">
          Welcome to Aktivate
        </Typography>
        <Typography variant="body1" className="text-center text-gray-600">
          Your platform for managing campaigns and donations
        </Typography>
      </div>
      <div className="p-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <QuickStats quickStats={quickStats} isLoading={isLoadingQuickStats} />
        </div>
        <div className="lg:col-span-2">
          <DonationsReport />
        </div>
        <div className="lg:col-span-3">
          <CheckRequestReport />
        </div>
        <div className="lg:col-span-3">
          <MessageLogsReport />
        </div>
      </div>
    </DashboardLayout>
  )
}

export default HomePage
