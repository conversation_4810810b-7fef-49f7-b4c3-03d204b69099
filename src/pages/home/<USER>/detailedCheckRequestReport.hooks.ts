import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query"
import { ClearDto } from "../api/dto/campaign.dto"
import { homeApi } from "../api/home.queries"
import { queryKeys } from "@/shared/model"

export const useClearCheckRequests = (
  options?: UseMutationOptions<unknown, Error, ClearDto>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (body: ClearDto) => {
      return homeApi.clear(body)
    },
    ...options,
    onSuccess: (...args) => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.campaign.dashboard.checkRequests] })
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
  })
}
