import { useGetMessageLogs } from "@/entities/campaign"
import { getAllErrorMessages } from "@/shared/lib/getAllErrorMessages"
import { usePagination } from "@/shared/model"
import { MessageLogsReportForm } from "@/widgets/messageLogsFilters"
import { format } from "date-fns"
import { useEffect, useMemo } from "react"
import { toast } from "react-toastify"
import { v4 as uuidv4 } from "uuid"
import { getColumns } from "./messageLogsReport.columns"

interface UseMessageLogsReportTableDataProps {
  messageLogsFilter: MessageLogsReportForm
}

export const useMessageLogsReportTableData = ({
  messageLogsFilter,
}: UseMessageLogsReportTableDataProps) => {
  const { pageSize, page, onPaginationModelChange } = usePagination()

  const {
    data: messageLogs,
    isLoading: isLoadingMessageLogs,
    error,
  } = useGetMessageLogs({
    page: page,
    per_page: pageSize,
    start_date: format(messageLogsFilter.start_date, "yyyy-MM-dd"),
    end_date: format(messageLogsFilter.end_date, "yyyy-MM-dd"),
    message_status: messageLogsFilter.message_status?.value ?? "",
  })

  useEffect(() => {
    if (error) {
      const errorMessage = getAllErrorMessages(error)
      toast.error(errorMessage)
    }
  }, [error])

  const currentPage = messageLogs?.meta?.current_page
    ? messageLogs.meta.current_page - 1
    : 0

  const totalPages = messageLogs?.meta?.total ? messageLogs.meta.total : 0

  const rows = useMemo(
    () => messageLogs?.data?.map((item) => ({ ...item, id: uuidv4() })),
    [messageLogs]
  )

  const columns = useMemo(() => getColumns(), [])

  return {
    messageLogs,
    isLoadingMessageLogs,
    columns,
    rows,
    currentPage,
    totalPages,
    pageSize,
    onPaginationModelChange,
  }
}
