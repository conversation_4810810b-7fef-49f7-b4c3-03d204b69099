import { yupResolver } from "@hookform/resolvers/yup"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import {
  CheckRequestFilterForm,
  checkRequestFilterSchema,
} from "./checkRequestFilter.schema"

export const useCheckRequestFilterForm = (
  defaultValues: CheckRequestFilterForm
) => {
  const form = useForm<CheckRequestFilterForm>({
    resolver: yup<PERSON><PERSON><PERSON><PERSON>(checkRequestFilterSchema),
    defaultValues,
  })

  useEffect(() => {
    const { start_date, end_date, check_request_status } = form.getValues()

    if (!start_date && !end_date && !check_request_status) {
      form.setValue("start_date", defaultValues?.start_date)
      form.setValue("end_date", defaultValues?.end_date)
      form.setValue("check_request_status", defaultValues?.check_request_status)
    }
  }, [defaultValues, form])

  return form
}
