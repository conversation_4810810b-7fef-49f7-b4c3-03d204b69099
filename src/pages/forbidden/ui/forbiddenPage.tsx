import { useNavigate } from "react-router-dom"

import { PATH } from "@/shared/config"
import { But<PERSON> } from "@/shared/ui/"
import { usePageTitle } from "@/shared/lib"

const ForbiddenPage = () => {
  usePageTitle("Forbidden")
  const navigate = useNavigate()

  const handleGoHome = () => {
    navigate(PATH.withAuth.home)
  }

  return (
    <div className="flex flex-col items-center justify-center h-screen min-w-screen bg-white text-black">
      <h1 className="text-4xl font-bold mb-4 text-center">
        403 - Access Forbidden
      </h1>
      <p className="text-lg mb-4 text-center">
        You don't have permission to access this page.
      </p>
      <p className="text-md mb-6 text-center text-gray-600">
        Please contact your administrator for access.
      </p>
      <Button onClick={handleGoHome} fullWidth={false} size="medium">
        Go to home
      </Button>
    </div>
  )
}

export default ForbiddenPage
