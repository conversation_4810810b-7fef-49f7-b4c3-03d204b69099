import { copyToClipboard } from "@/shared/lib"
import { Dialog, IconButton } from "@/shared/ui"
import { <PERSON><PERSON> } from "@/shared/ui/Button"
import ContentCopyIcon from "@mui/icons-material/ContentCopy"
import FacebookIcon from "@mui/icons-material/Facebook"
import InstagramIcon from "@mui/icons-material/Instagram"
import LinkIcon from "@mui/icons-material/Link"
import XIcon from "@mui/icons-material/X"
import { Divider, Typography } from "@mui/material"
import { useState } from "react"
import { FacebookShareButton } from "react-share"
import { useShareForm } from "../../model/share.form"
import { ShareFormData } from "../../model/share.schema"

interface ShareButtonsProps {
  url: string
  teamDisplayName: string
  colors: {
    primary: string
    secondary: string
  }
}

export const ShareButtons = ({
  url,
  teamDisplayName,
  colors,
}: ShareButtonsProps) => {
  const [shareState, setShareState] = useState<"email" | "text">("email")
  const [isInstaDialogOpen, setIsInstaDialogOpen] = useState(false)
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false)
  const isInstagram = !!isInstaDialogOpen
  const isDialogOpen = isInstaDialogOpen || isShareDialogOpen

  // Feature is disabled until post-mvp
  const hideShareForm = true

  const { register, handleSubmit, errors, setValue } = useShareForm()

  const copyToClipboardHandler = async (text: string) => {
    await copyToClipboard(text)
  }

  const handleCloseDialog = () => {
    setIsInstaDialogOpen(false)
    setIsShareDialogOpen(false)
  }

  const handleChangeShareState = (state: string) => {
    setShareState(state as "email" | "text")
    // if email was selected, we need to clear the phone field. and vice versa
    if (state === "email") {
      setValue("phone", "")
    } else {
      setValue("email", "")
    }
  }

  const onSubmit = (data: ShareFormData) => {
    console.log(data)
  }

  const shareText = `I'm raising funds for ${teamDisplayName} and would love your support. Thank you!`

  return (
    <div className="flex flex-col justify-between items-center">
      <div className="flex flex-row w-full justify-between">
        <FacebookShareButton
          className="p-2 hover:opacity-80 cursor-pointer"
          url={url}
          title={shareText}
          hashtag={shareText}
        >
          <FacebookIcon className="h-6 w-6" />
        </FacebookShareButton>

        <a
          href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(url)}`}
          target="_blank"
          rel="noopener noreferrer"
          className="p-2 hover:opacity-80 cursor-pointer text-gray-700!"
        >
          <XIcon className="h-6 w-6" />
        </a>

        <div
          className="p-2 hover:opacity-80 cursor-pointer"
          onClick={() => setIsInstaDialogOpen(true)}
        >
          <InstagramIcon className="h-6 w-6" />
        </div>

        <div
          className="p-2 hover:opacity-80 cursor-pointer"
          onClick={() => copyToClipboardHandler(`${shareText} ${url}`)}
        >
          <LinkIcon className="h-6 w-6" />
        </div>
      </div>

      {!hideShareForm ? (
        <>
          <div className="w-full mb-5">
            <Divider component="div" />
          </div>
          <div className="inline-flex rounded-full border-2 w-full border-gray-200 mb-5">
            <button
              className={`px-5 py-2 w-[50%] text-base font-medium transition-colors !rounded-full tracking-tight ${
                shareState === "email"
                  ? "text-white hover:opacity-80"
                  : "text-gray"
              }`}
              style={{
                backgroundColor:
                  shareState === "email" ? colors?.primary : "white",
              }}
              onClick={() => handleChangeShareState("email")}
            >
              Email
            </button>
            <button
              className={`px-5 py-2 w-[50%] text-base font-medium transition-colors !rounded-full tracking-tight ${
                shareState === "text"
                  ? "text-white hover:opacity-80"
                  : "text-gray"
              }`}
              style={{
                backgroundColor:
                  shareState === "text" ? colors?.primary : "white",
              }}
              onClick={() => handleChangeShareState("text")}
            >
              Text
            </button>
          </div>

          <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-5">
              <div className="w-full">
                {shareState === "email" ? (
                  <div>
                    <input
                      {...register("email")}
                      placeholder="Email"
                      className="w-full border-solid border-2 border-gray-200 rounded-full p-2 outline-none"
                    />
                    {errors.email && (
                      <p className="text-red-500 px-5 text-sm">
                        {errors.email.message}
                      </p>
                    )}
                  </div>
                ) : (
                  <div>
                    <input
                      {...register("phone")}
                      placeholder="Phone Number"
                      className="w-full border-solid border-2 border-gray-200 rounded-full p-2 outline-none"
                    />
                    {errors.phone && (
                      <p className="text-red-500 px-5 text-sm">
                        {errors.phone.message}
                      </p>
                    )}
                  </div>
                )}
              </div>
              <Button
                size="large"
                type="submit"
                className="text-white px-5 py-2 !rounded-full"
                style={{
                  backgroundColor: colors?.primary,
                }}
              >
                {shareState === "email" ? "Send Email" : "Send Text"}
              </Button>
            </div>
          </form>
        </>
      ) : (
        ""
      )}

      <Dialog
        open={isDialogOpen}
        onClose={handleCloseDialog}
        title={`Share manually${isInstagram ? " on Instagram" : ""}`}
      >
        <Typography className="!mb-4">
          Copy the text below to your clipboard:
        </Typography>
        <div className="flex flex-col md:flex-row items-start gap-2">
          <Typography className="!w-full!font-bold !mb-4">
            {shareText} {url}
          </Typography>
          <IconButton
            title="Copy"
            onClick={() => copyToClipboardHandler(`${shareText} ${url}`)}
          >
            <ContentCopyIcon />
          </IconButton>
        </div>
        {isInstagram ? (
          <Typography>
            Go to the{" "}
            <a
              href="https://www.instagram.com"
              target="_blank"
              rel="noreferrer"
            >
              Instagram
            </a>{" "}
            app to post your donation link.
          </Typography>
        ) : (
          ""
        )}
      </Dialog>
    </div>
  )
}
