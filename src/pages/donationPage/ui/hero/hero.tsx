import { DonationProgress } from "@/shared/ui/donationProgress"
import { Typography, useMediaQuery } from "@mui/material"
import { ShareButtons } from "../shareComponent/shareComponent"
import { theme } from "@/shared/config"
const LOGO_PLACEHOLDER = "/Profile_avatar_placeholder_large.png"

interface HeroProps {
  teamDisplayName?: string
  progressInfo?: {
    raised: number
    goal: number
    daysLeft: number
    progress?: number
  }
  colors?: {
    primary: string
    secondary: string
  }
  title?: string
  city?: string
  state?: string
  logo?: string
  avatarUrl?: string
  rightSlot?: React.ReactNode
  name?: string
}

export default function Hero({
  teamDisplayName,
  colors,
  title,
  city,
  state,
  progressInfo = { raised: 0, goal: 0, daysLeft: 0, progress: 0 },
  logo,
  avatarUrl,
  rightSlot,
  name,
}: HeroProps) {
  const figureClass = avatarUrl
    ? "left-[calc(50%-58px)] md:left-[calc(50%-116px)]"
    : "left-1/2"

  const isMobile = useMediaQuery(theme.breakpoints.down("md"))

  return (
    <section className="font-inter mb-5" aria-label="Team information">
      <div className="container relative">
        {/* Team logo positioned over the header */}
        <figure
          className={`flex absolute ${figureClass} scale-50 top-[0px] md:top-[40px] md:scale-100 transform -translate-x-1/2 -translate-y-1/2 z-10`}
        >
          <div className="w-[298px] h-[298px] relative rounded-full overflow-hidden border-4 border-white bg-primary">
            <img
              src={logo ?? LOGO_PLACEHOLDER}
              alt={`${teamDisplayName} team logo`}
              className="w-full h-full object-cover"
              loading="eager"
              width={298}
              height={298}
              decoding="async"
              fetchPriority="high"
            />
          </div>
          {avatarUrl && (
            <div className="w-[298px] h-[298px] absolute transform translate-x-[80%] rounded-full overflow-hidden border-4 border-white bg-primary">
              <img
                src={avatarUrl}
                alt="Avatar"
                className="w-full h-full object-cover"
                loading="eager"
                width={298}
                height={298}
                decoding="async"
                fetchPriority="high"
              />
            </div>
          )}
        </figure>

        <div className="bg-white pt-[80px] pb-[30px] rounded-t-[14px] rounded-b-[14px]">
          <header className="flex flex-col md:flex-row items-center justify-between px-[30px]">
            <div className="flex flex-col mb-5 md:mb-0 items-center md:items-start">
              <Typography
                component="h1"
                className="!text-3xl !font-bold !mb-1 !tracking-tighter"
              >
                {isMobile && name ? name : title}
              </Typography>
              <Typography
                component="p"
                className="!text-gray-500 !tracking-tighter"
              >
                {isMobile && name ? title : `${city}, ${state}`}
              </Typography>
            </div>
            <div className="hidden md:flex flex-col items-center md:items-end">
              {rightSlot}
            </div>
          </header>
          <div
            className="md:hidden bg-white/95 px-8"
            role="complementary"
            aria-label="Share campaign"
          >
            <ShareButtons
              url={window.location.href}
              teamDisplayName={teamDisplayName ?? ""}
              colors={{
                primary: colors?.primary ?? "",
                secondary: colors?.secondary ?? "",
              }}
            />
          </div>
          <DonationProgress progressInfo={progressInfo} colors={colors} />
        </div>
      </div>
    </section>
  )
}
