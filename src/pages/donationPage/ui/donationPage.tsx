import { config } from "@/shared/config"
import { calculateDaysLeft, calculateProgress } from "@/shared/lib/"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/shared/ui"
import { LeaderBoard, LeaderboardTab } from "@/widgets/leaderboard"
import { CircularProgress, Typography } from "@mui/material"
import { useState } from "react"
import { useCampaignImages } from "../model/useCampaignImages"
import { useCampaignMetaTags } from "../model/useCampaignMetaTags"
import { useDonationAmounts } from "../model/useDonationAmounts"
import { useDonationPageData } from "../model/useDonationPageData"
import { CoachMessage } from "./coachMessage/coachMessage"
import { DonationWizardComponent } from "./donationWizard/donationWizardComponent"
import Hero from "./hero/hero"
import { Moments } from "./moments/moments"
import { ShareButtons } from "./shareComponent/shareComponent"
import { formatDateStrToUs } from "../model/formatDate"

const HERO_PLACEHOLDER = "./Hero-Banner-Placeholder-Dark-1024x480.png"

const CalendarSlot = ({
  startDate,
  endDate,
}: {
  startDate: string
  endDate: string
}) => {
  return (
    <>
      <Typography
        component="h2"
        className="!text-3xl !font-bold !tracking-tighter"
      >
        Campaign Calendar
      </Typography>
      <Typography component="p" className="!text-gray-500 !tracking-tighter">
        {startDate && endDate
          ? `${formatDateStrToUs(startDate)} - ${formatDateStrToUs(endDate)}`
          : ""}
      </Typography>
    </>
  )
}

const NameSlot = ({ name }: { name: string }) => {
  return (
    <>
      <Typography
        component="h2"
        className="!text-3xl !font-bold !tracking-tighter"
      >
        {name}
      </Typography>
      <Typography
        component="p"
        className="!text-gray-500 !tracking-tighter h-6"
      >
        {" "}
      </Typography>
    </>
  )
}

const DonationPage = () => {
  const [isMobileDonationWizardOpen, setIsMobileDonationWizardOpen] =
    useState(false)

  const [leaderboardPage, setLeaderboardPage] = useState(1)
  const [leaderboardTab, setLeaderboardTab] = useState<LeaderboardTab>("donors")

  const {
    campaignData: data,
    isLoading,
    guid,
    avatarUrl,
  } = useDonationPageData()
  const isPersonalPage = data && "campaign_user_info" in data!

  const { tipAmount, denominations, otherTipPercentage } =
    useDonationAmounts(data)
  useCampaignMetaTags(data)

  const { logo, actionShot, hero } = useCampaignImages(data?.campaign_id!)

  const campaignEndDate = data?.campaign_end_date
    ? new Date(data?.campaign_end_date)
    : null
  const daysLeft = calculateDaysLeft(campaignEndDate)

  const totalDonations = data?.donations_raised ?? 0

  const handleGoToAktivate = () => {
    window.open(config.aktivateUrl, "_self")
  }

  const donationButtonClassName = "w-full !rounded-full !bg-green-500 !text-white"

  return (
    <>
      {isLoading && (
        <div
          className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50"
          role="alert"
          aria-busy="true"
          aria-label="Loading campaign data"
        >
          <CircularProgress className="h-screen" aria-hidden="true" />
        </div>
      )}

      {data?.status === "Suspended" ? (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex flex-col items-center justify-center z-50">
          <Typography variant="h4" className="!text-white !mb-10">
            This campaign is closed and no longer taking donations. Thank you
            for your support.
          </Typography>
          <Button
            variant="contained"
            size="large"
            className="!rounded-full"
            onClick={handleGoToAktivate}
            fullWidth={false}
          >
            Go to Aktivate
          </Button>
        </div>
      ) : (
        <div className={`w-screen h-screen bg-cover bg-center `} role="main">
          <img
            src={hero?.[0]?.image?.s3_url ?? HERO_PLACEHOLDER}
            alt="Hero"
            className="absolute top-0 left-0 w-full h-full object-cover"
          />
          <div className="min-h-screen bg-background pt-32  backdrop-blur-xl bg-white/30">
            <div className="relative z-20 px-5 mx-auto container mb-20">
              <Hero
                teamDisplayName={data?.team_display_name}
                rightSlot={
                  isPersonalPage ? (
                    <NameSlot
                      name={data?.campaign_user_info?.user_name ?? ""}
                    />
                  ) : (
                    <CalendarSlot
                      startDate={data?.campaign_start_date ?? ""}
                      endDate={data?.campaign_end_date ?? ""}
                    />
                  )
                }
                title={data?.donation_page_title}
                city={data?.city}
                state={data?.state}
                logo={logo?.[0]?.image?.s3_url}
                name={isPersonalPage ? data?.campaign_user_info?.user_name : ""}
                avatarUrl={avatarUrl}
                colors={{
                  primary: data?.colors?.primary ?? "",
                  secondary: data?.colors?.secondary ?? "",
                }}
                progressInfo={{
                  raised: totalDonations ?? 0,
                  goal: Number(data?.fundraising_goal || 0),
                  progress: calculateProgress(
                    totalDonations,
                    Number(data?.fundraising_goal || 0)
                  ),
                  daysLeft: daysLeft,
                }}
              />

              <div className="grid grid-cols-1 lg:grid-cols-12 gap-5">
                <div className="lg:col-span-8 flex flex-col gap-5">
                  <CoachMessage message={data?.our_message_text} />
                  {data?.show_leaderboard && (
                    <LeaderBoard
                      campaignId={data?.campaign_id}
                      setLeaderboardPage={setLeaderboardPage}
                      setLeaderboardTab={setLeaderboardTab}
                    />
                  )}
                  <Moments images={actionShot ?? []} />
                </div>
                <div className="lg:col-span-4 relative">
                  <div className="hidden lg:block h-full">
                    <div className="sticky top-8 self-start space-y-6">
                      <div
                        className="bg-white/95 backdrop-blur rounded-[14px] p-[30px]"
                        role="complementary"
                        aria-label="Donation form"
                      >
                        <DonationWizardComponent
                          tipAmount={tipAmount ?? []}
                          otherTipPercentage={otherTipPercentage ?? ""}
                          denominations={denominations ?? []}
                          campaignId={data?.campaign_id}
                          setIsMobileDonationWizardOpen={
                            setIsMobileDonationWizardOpen
                          }
                          colors={{
                            primary: data?.colors?.primary ?? "#000000",
                            secondary: data?.colors?.secondary ?? "#ffffff",
                          }}
                          tippingEnabled={data?.tipping_enabled ?? false}
                          guid={guid as string}
                          leaderboardPage={leaderboardPage}
                          leaderboardTab={leaderboardTab}
                          buttonClassName={donationButtonClassName}
                          teamDisplayName={data?.team_display_name ?? ""}
                        />
                      </div>
                      <div
                        className="bg-white/95 backdrop-blur rounded-[14px] p-[30px]"
                        role="complementary"
                        aria-label="Share campaign"
                      >
                        <ShareButtons
                          url={window.location.href}
                          teamDisplayName={data?.team_display_name ?? ""}
                          colors={{
                            primary: data?.colors?.primary ?? "",
                            secondary: data?.colors?.secondary ?? "",
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* mobile donation wizard */}
                <div
                  className="w-full lg:hidden sticky bottom-0 left-0 p-4 bg-white rounded-t-[14px] shadow-lg relative z-10"
                  role="complementary"
                  aria-label="Mobile donation form"
                >
                  {isMobileDonationWizardOpen ? (
                    <div className="w-full">
                      <DonationWizardComponent
                        tipAmount={tipAmount ?? []}
                        otherTipPercentage={otherTipPercentage ?? ""}
                        setIsMobileDonationWizardOpen={
                          setIsMobileDonationWizardOpen
                        }
                        denominations={denominations ?? []}
                        campaignId={data?.campaign_id}
                        colors={{
                          primary: data?.colors?.primary ?? "#000000",
                          secondary: data?.colors?.secondary ?? "#ffffff",
                        }}
                        tippingEnabled={data?.tipping_enabled ?? false}
                        guid={guid as string}
                        leaderboardPage={leaderboardPage}
                        leaderboardTab={leaderboardTab}
                        buttonClassName={donationButtonClassName}
                        teamDisplayName={data?.team_display_name ?? ""}
                      />
                    </div>
                  ) : (
                    <Button
                      size="large"
                      type="submit"
                      variant="contained"
                      className={donationButtonClassName}
                      style={{ backgroundColor: data?.colors?.primary ?? "" }}
                      onClick={() => setIsMobileDonationWizardOpen(true)}
                      aria-label="Open donation form"
                    >
                      Donate
                    </Button>
                  )}
                </div>
              </div>
            </div>
            <Footer />
          </div>
        </div>
      )}
    </>
  )
}

export default DonationPage
