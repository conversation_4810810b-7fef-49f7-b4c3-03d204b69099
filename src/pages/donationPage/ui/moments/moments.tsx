import { CampaignImageDto } from "@/entities/campaignImage"
import { Carousel } from "@/shared/ui/carousel"

export const Moments = ({ images }: { images: CampaignImageDto[] }) => {
  const slides = images.map((image) => ({
    id: image.id,
    image: image.image?.s3_url,
    title: `Moment ${image.id}`,
    description: `Moment ${image.id}`,
  }))

  const options = {
    loop: true,
  }

  return (
    <div className="bg-white p-6 rounded-lg container relative">
      <div className="text-3xl font-bold tracking-tight mb-10">Moments</div>
      <Carousel slides={slides} options={options} />
    </div>
  )
}
