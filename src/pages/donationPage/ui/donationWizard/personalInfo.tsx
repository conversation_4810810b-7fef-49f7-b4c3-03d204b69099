import { Button, Checkbox, Input } from "@/shared/ui"
import { usePersonalInfoForm } from "../../model/personalInfo.form"
import { PersonalInfoFormData } from "../../model/personalInfo.schema"
import { PaymentNavigation } from "./paymentNavigation"

interface PersonalInfoProps {
  onSubmit: (data: PersonalInfoFormData) => void
  personalInfo: PersonalInfoFormData
  buttonClassName: string
  onBack: () => void
  onAbort: () => void
}

export const PersonalInfo = ({
  onSubmit,
  onBack,
  onAbort,
  personalInfo,
  buttonClassName,
}: PersonalInfoProps) => {
  const { register, handleSubmit, errors, control } =
    usePersonalInfoForm(personalInfo)

  const messagePlaceholder =
    "Leave a comment. (optional) All comments are visible on the public campaign site. Allowed max length is 250 characters."

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <PaymentNavigation
        title="Personal Information"
        onBack={onBack}
        onClose={onAbort}
      />
      <div className="space-y-4">
        <Input
          {...register("name")}
          label="First Name"
          variant="outlined"
          error={!!errors.name}
          helperText={errors.name?.message}
          required
        />
        <Input
          {...register("lastName")}
          label="Last Name"
          variant="outlined"
          error={!!errors.lastName}
          helperText={errors.lastName?.message}
          required
        />
        <Input
          {...register("phone")}
          label="Phone"
          type="tel"
          variant="outlined"
          error={!!errors.phone}
          helperText={errors.phone?.message}
          required
        />
        <Input
          {...register("email")}
          label="Email"
          variant="outlined"
          error={!!errors.email}
          helperText={errors.email?.message}
          required
        />
        <Input
          {...register("message")}
          variant="outlined"
          error={!!errors.message}
          multiline
          rows={4}
          label={"Message"}
          placeholder={messagePlaceholder}
          helperText={errors.message?.message}
        />
        <Checkbox
          label="Make my donation anonymous"
          defaultValue={false}
          name="isAnonymous"
          control={control}
        />
      </div>
      <Button
        size="large"
        type="submit"
        variant="contained"
        className={buttonClassName}
      >
        Next
      </Button>
    </form>
  )
}
