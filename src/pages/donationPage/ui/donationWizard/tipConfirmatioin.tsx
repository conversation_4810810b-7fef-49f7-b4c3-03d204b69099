import { formatCurrency } from "@/shared/lib/formatCurrency"
import { But<PERSON> } from "@/shared/ui/Button"
import { Typography } from "@mui/material"
import { PaymentNavigation } from "./paymentNavigation"

interface TipConfirmationProps {
  onNext: () => void
  onChange: (value: number) => void
  calculatedOtherTipAmount: number
  otherTipPercentage: string
  onBack: () => void
  onAbort: () => void
  buttonClassName: string
  teamDisplayName: string
}

export const TipConfirmation = ({
  onNext,
  onChange,
  calculatedOtherTipAmount,
  otherTipPercentage,
  teamDisplayName,
  onBack,
  onAbort,
}: TipConfirmationProps) => {
  const handleNextClick = (value: boolean) => {
    if (value) {
      onChange(Number(otherTipPercentage))
    }
    onNext()
  }

  return (
    <>
      <PaymentNavigation title="You’re already doing something amazing — thank you!" onBack={onBack} onClose={onAbort} />

      <Typography
        variant="body1"
        className="!text-xl  p-4 !bg-gray-200 rounded-lg"
      >
        Your generosity helps keep fundraising simple and affordable for {teamDisplayName}.
        Even a small contribution of ${formatCurrency(calculatedOtherTipAmount, {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2,
        })} goes a long way toward making that possible. 
      </Typography>
      <div className="flex justify-center gap-4">
        <Button
          size="large"
          className='w-full !rounded-full !bg-green-500 !text-white'
          onClick={() => handleNextClick(true)}
        >
          Yes
        </Button>
        <Button
          size="large"
          variant="outlined"
          className='w-full !rounded-full !bg-white-50 !border-gray-400 !text-gray-400 hover:!bg-gray-100'
          onClick={() => handleNextClick(false)}
        >
          No
        </Button>
      </div>
    </>
  )
}
