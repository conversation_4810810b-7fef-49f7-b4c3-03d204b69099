import { TippingPercentageDto } from "@/entities/campaign"
import { But<PERSON> } from "@/shared/ui/Button"
import { PaymentNavigation } from "./paymentNavigation"

interface TipSelectorProps {
  colors: {
    primary: string
    secondary: string
  }
  value: number
  onChange: (amount: number) => void
  otherTipPercentage: string
  onNext: () => void
  tipAmount: TippingPercentageDto[]
  onTipOther: () => void
  totalDonationAmount: number
  onBack: () => void
  onAbort: () => void
  buttonClassName: string
}

export const TipSelector = ({
  colors,
  value,
  onChange,
  onNext,
  tipAmount,
  otherTipPercentage,
  onTipOther,
  totalDonationAmount,
  onBack,
  onAbort,
  buttonClassName,
}: TipSelectorProps) => {
  const handleTipOtherClick = () => {
    onChange(Number(otherTipPercentage))
    onTipOther()
  }

  return (
    <div className="space-y-4">
      <PaymentNavigation title="Add a tip" onBack={onBack} onClose={onAbort} />
      <div className="grid grid-cols-3 gap-2 mb-6">
        {tipAmount.map((amount) => (
          <Button
            key={amount.id}
            variant={value === Number(amount.value) ? "contained" : "outlined"}
            onClick={() => onChange(Number(amount.value))}
            className="h-14 !text-base !rounded-full"
            sx={{
              "&.MuiButton-contained": {
                backgroundColor: `${colors?.primary} !important`,
                color: "#fff",
              },
              "&.MuiButton-outlined": {
                borderColor: `${colors?.primary} !important`,
                color: "#000",
              },
            }}
          >
            {amount.value}%
          </Button>
        ))}
      </div>
      <Button
        variant="outlined"
        color="primary"
        onClick={handleTipOtherClick}
        sx={{
          "&.MuiButton-outlined": {
            borderColor: `${colors?.primary} !important`,
            color: "#000",
          },
        }}
        className="w-full h-14 !text-base !rounded-full !mb-6"
      >
        Other
      </Button>

      <div className="flex justify-center">
        <Button size="large" className={buttonClassName} onClick={onNext}>
          Donate ${totalDonationAmount}
        </Button>
      </div>
    </div>
  )
}
