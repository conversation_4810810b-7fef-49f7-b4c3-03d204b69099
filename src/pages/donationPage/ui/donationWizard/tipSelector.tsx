import { Button } from "@/shared/ui/Button"
import { PaymentNavigation } from "./paymentNavigation"
import { TipAmount } from "../../model/useDonationAmounts"
import { formatCentsToDollarStr } from "@/shared/lib"
import { TooltipIcon } from "@/shared/ui/tooltipIcon"

interface TipSelectorProps {
  colors: {
    primary: string
    secondary: string
  }
  value: number
  onChange: (amount: number) => void
  onNext: () => void
  tipPercentages: TipAmount[]
  onTipOther: () => void
  totalDonationAmountCents: number
  onBack: () => void
  onAbort: () => void
  buttonClassName: string
  teamDisplayName: string
}

export const TipSelector = ({
  colors,
  value,
  onChange,
  onNext,
  tipPercentages,
  onTipOther,
  totalDonationAmountCents,
  onBack,
  onAbort,
  teamDisplayName,
}: TipSelectorProps) => {
  const handleTipOtherClick = () => {
    onChange(0)
    onTipOther()
  }

  return (
    <div className="space-y-4">
      <PaymentNavigation onBack={onBack} onClose={onAbort}>
        <div className="flex justify-center gap-1 items-center">
          <p className="text-center !text-base md:!text-lg !font-bold">
            Power future fundraisers
          </p>
          <TooltipIcon
            placement="top"
            text={
              <>
                <p className="!text-white text-center !text-xl !font-bold">
                  About your contribution
                </p>
                <p className="!text-white">
                  Your optional support helps cover processing fees and
                  technology costs that power this fundraising platform —
                  allowing us to keep fees low and maximize the impact of every
                  donation for {teamDisplayName}.
                </p>
              </>
            }
            tooltipProps={{
              enterTouchDelay: 0,
            }}
          />
        </div>
      </PaymentNavigation>

      <div className="flex justify-center">
        <Button
          size="large"
          className="w-full h-14 !rounded-full !bg-green-500 !text-white"
          onClick={onNext}
        >
          Donate ${formatCentsToDollarStr(totalDonationAmountCents)}
        </Button>
      </div>

      <div className="grid grid-cols-3 gap-2 mb-6">
        {tipPercentages.map((percentage) => (
          <Button
            key={percentage.id}
            variant={
              value === Number(percentage.value) ? "contained" : "outlined"
            }
            onClick={() => onChange(Number(percentage.value))}
            className="h-11 !text-base !rounded-full"
            sx={{
              "&.MuiButton-contained": {
                backgroundColor: `${colors?.primary} !important`,
                color: "#fff",
              },
              "&.MuiButton-outlined": {
                borderColor: `${colors?.primary} !important`,
                color: "#000",
              },
            }}
          >
            {percentage.value}%
          </Button>
        ))}
      </div>
      <Button
        variant="outlined"
        color="primary"
        onClick={handleTipOtherClick}
        sx={{
          "&.MuiButton-outlined": {
            borderColor: `${colors?.primary} !important`,
            color: "#000",
          },
        }}
        className="w-full h-11 !text-base !rounded-full !mb-6"
      >
        Other
      </Button>
    </div>
  )
}
