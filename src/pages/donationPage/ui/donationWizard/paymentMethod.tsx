import { Button } from "@/shared/ui/Button"
import { PaymentNavigation } from "./paymentNavigation"
interface PaymentMethodProps {
  onChange: (method: string) => void
  onBack: () => void
  onAbort: () => void
}

const PAYMENT_METHODS = [
  {
    id: 1,
    name: "Credit Card",
    color: "#FF0000", // red
    icon: null,
  },
]

export const PaymentMethod = ({
  onChange,
  onBack,
  onAbort,
}: PaymentMethodProps) => {
  return (
    <div className="space-y-4">
      <PaymentNavigation
        title="Payment Method"
        onBack={onBack}
        onClose={onAbort}
      />
      <div className="grid grid-cols-1 gap-4">
        {PAYMENT_METHODS.map((method) => (
          <Button
            key={method.id}
            variant="contained"
            size="large"
            className="w-full !rounded-full !text-white"
            style={{ backgroundColor: method.color }}
            onClick={() => onChange(method.name)}
          >
            {method.name}
            {method.icon}
          </Button>
        ))}
      </div>
    </div>
  )
}
