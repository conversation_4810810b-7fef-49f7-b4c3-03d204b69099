import { parseSquareErrors } from "@/shared/lib/parseSquareErrors"
import { Button } from "@/shared/ui"
import { Typography } from "@mui/material"

interface ErrorStateProps {
  onDonateAgain: () => void
  errors: string[]
  buttonClassName: string
}

export const ErrorState = ({
  onDonateAgain,
  errors,
  buttonClassName,
}: ErrorStateProps) => {
  const readableErrors = parseSquareErrors(errors)

  return (
    <div>
      <div className="flex flex-col">
        <Typography className="!text-xl !font-bold text-center">
          Oops!
        </Typography>
        {readableErrors?.map((error) => (
          <Typography
            key={error}
            className="!text-xl !font-bold text-center !mb-4"
          >
            {error}
          </Typography>
        ))}
      </div>
      <Button
        size="large"
        type="submit"
        variant="contained"
        className={buttonClassName}
        onClick={onDonateAgain}
      >
        Donate again
      </Button>
    </div>
  )
}
