import { Button } from "@/shared/ui/Button"
import { Input } from "@/shared/ui/Input"
import { InputAdornment } from "@mui/material"
import { PaymentNavigation } from "./paymentNavigation"

interface TipOtherProps {
  onNext: (value: number) => void
  onChange: (value: number) => void
  tip: number
  onBack: () => void
  onAbort: () => void
  calculatedTip: number
  buttonClassName: string
}

export const TipOther = ({
  onNext,
  onChange,
  tip,
  onBack,
  onAbort,
  calculatedTip,
  buttonClassName,
}: TipOtherProps) => {
  const handleNextClick = () => {
    onNext(tip ? tip : 0)
  }

  return (
    <>
      <PaymentNavigation title="Add a tip" onBack={onBack} onClose={onAbort} />

      <div>
        <Input
          label="Custom amount %"
          variant="outlined"
          type="number"
          onChange={(e) => onChange(Number(e.target.value))}
          value={tip.toString()}
          inputProps={{
            min: 0,
            max: 100,
          }}
          slotProps={{
            input: {
              endAdornment: <InputAdornment position="end">%</InputAdornment>,
            },
          }}
        />
      </div>
      <div className="flex justify-center">
        <Button
          size="large"
          className={buttonClassName}
          onClick={handleNextClick}
        >
          Tip ${calculatedTip}
        </Button>
      </div>
    </>
  )
}
