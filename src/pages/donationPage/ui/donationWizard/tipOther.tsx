import { Button } from "@/shared/ui/Button"
import { Input } from "@/shared/ui/Input"
import { InputAdornment, Typography } from "@mui/material"
import { PaymentNavigation } from "./paymentNavigation"
import { useEffect, useState } from "react"
import { onKeyDownFilter } from "../../lib/onKeyDownFilter"
import { formatCentsToDollarStr, formatDollarStrToCents } from "@/shared/lib"

interface TipOtherProps {
  onNext: (value: number) => void
  onChange: (value: number) => void
  otherTipCents: number
  onBack: () => void
  onAbort: () => void
  buttonClassName: string
  teamDisplayName: string
}

export const TipOther = ({
  onNext,
  onChange,
  otherTipCents,
  onBack,
  onAbort,
  buttonClassName,
  teamDisplayName,
}: TipOtherProps) => {
  const handleNextClick = () => {
    onNext(otherTipCents ? otherTipCents : 0)
  }
  const [stringifiedValue, setStringifiedValue] = useState<string>(
    formatCentsToDollarStr(otherTipCents)
  )

  useEffect(() => {
    setStringifiedValue(formatCentsToDollarStr(otherTipCents))
  }, [otherTipCents])

  const onBlurHandler = () => {
    if (!stringifiedValue) {
      onChange(0)
      setStringifiedValue("0.00")
      return
    }
    onChange(formatDollarStrToCents(stringifiedValue))
    setStringifiedValue(parseFloat(stringifiedValue).toFixed(2))
  }

  return (
    <>
      <PaymentNavigation onBack={onBack} onClose={onAbort}>
        <Typography variant="h6" className="!text-lg !font-bold text-center">
          Enter a custom contribution
        </Typography>
      </PaymentNavigation>

      <Typography
        variant="h6"
        className="!text-base !font-semibold text-center"
      >
        Want to choose your own amount?
      </Typography>

      <Typography variant="h6" className="!text-sm !mb-6">
        Even a small contribution — like $
        {formatCentsToDollarStr(otherTipCents)} — helps offset credit card and
        platform costs so more of your donation goes directly to
        {teamDisplayName}.
      </Typography>

      <div>
        <Input
          label="Custom amount $"
          variant="outlined"
          type="text"
          placeholder="0.00"
          onChange={(e) => {
            setStringifiedValue(e.target.value ?? "0.00")
          }}
          onKeyDown={onKeyDownFilter(stringifiedValue)}
          onBlur={onBlurHandler}
          value={stringifiedValue}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">$</InputAdornment>
              ),
              lang: "en-US",
            },
          }}
          sx={{
            "& .MuiInputBase-input": {
              textAlign: "center",
              marginLeft: "-1em",
            },
          }}
        />
      </div>
      <div className="flex justify-center">
        <Button
          size="large"
          className={buttonClassName}
          onClick={handleNextClick}
        >
          Confirm
        </Button>
      </div>
    </>
  )
}
