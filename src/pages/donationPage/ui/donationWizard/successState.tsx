import { Button } from "@/shared/ui"
import { Typography } from "@mui/material"
import { formatCentsToDollarStr } from "@/shared/lib"

interface SuccessStateProps {
  totalDonationAmountCents: number
  onDonateAgain: () => void
  buttonClassName: string
}

export const SuccessState = ({
  totalDonationAmountCents,
  onDonateAgain,
  buttonClassName,
}: SuccessStateProps) => {
  return (
    <div>
      <div className="flex flex-col">
        <Typography className="!text-xl !font-bold text-center">
          Thank you!
        </Typography>
        <Typography className="!text-xl !font-bold text-center !mb-4">
          Your payment is confirmed.
        </Typography>
        <Typography className="!text-5xl !font-bold text-center !mb-5">
          ${formatCentsToDollarStr(totalDonationAmountCents)}
        </Typography>
      </div>
      <Button
        size="large"
        type="submit"
        variant="contained"
        className={buttonClassName}
        onClick={onDonateAgain}
      >
        Donate again
      </Button>
    </div>
  )
}
