import { Button } from "@/shared/ui"
import { Typography } from "@mui/material"

interface SuccessStateProps {
  totalDonationAmount: number
  onDonateAgain: () => void
  buttonClassName: string
}

export const SuccessState = ({
  totalDonationAmount,
  onDonateAgain,
  buttonClassName,
}: SuccessStateProps) => {
  return (
    <div>
      <div className="flex flex-col">
        <Typography className="!text-xl !font-bold text-center">
          Thank you!
        </Typography>
        <Typography className="!text-xl !font-bold text-center !mb-4">
          Your payment is confirmed.
        </Typography>
        <Typography className="!text-5xl !font-bold text-center !mb-5">
          ${totalDonationAmount}
        </Typography>
      </div>
      <Button
        size="large"
        type="submit"
        variant="contained"
        className={buttonClassName}
        onClick={onDonateAgain}
      >
        Donate again
      </Button>
    </div>
  )
}
