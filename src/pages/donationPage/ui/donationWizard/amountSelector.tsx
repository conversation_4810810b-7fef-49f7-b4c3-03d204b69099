import { DonationDenominationDto } from "@/entities/campaign"
import { But<PERSON> } from "@/shared/ui/Button"
import { Typography } from "@mui/material"
import { useEffect, useRef } from "react"
import { useAmountSelectorForm } from "../../model/amountSelector.form"
import { AmountSelectorData } from "../../model/amountSelector.schema"

interface AmountSelectorProps {
  value: number
  onChange: (amount: number) => void
  colors: {
    primary: string
    secondary: string
  }
  onNext: () => void
  denominations: DonationDenominationDto[]
  buttonClassName: string
}

export const AmountSelector = ({
  value,
  onChange,
  colors,
  onNext,
  denominations,
  buttonClassName,
}: AmountSelectorProps) => {
  const { handleSubmit, setValue, errors } =
    useAmountSelectorForm(value)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setValue("amount", value)
  }, [value, setValue])

  const onSubmit = (data: AmountSelectorData) => {
    onChange(data.amount)
    onNext()
  }

  const handlePresetAmount = (amount: number) => {
    setValue("amount", amount)
    onChange(amount)
  }

  const formatAmountValue = (amount: number) => {
    return amount.toFixed(2)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <Typography
        variant="h6"
        className="!text-xl text-center !font-bold !mb-4"
      >
        Donation Amount
      </Typography>
      <div className="grid grid-cols-4 gap-2">
        {denominations.map((denomination) => (
          <Button
            key={denomination.amount}
            variant={
              value === Number(denomination.amount) ? "contained" : "outlined"
            }
            onClick={() => handlePresetAmount(Number(denomination.amount))}
            type="button"
            className="h-14 !text-base !rounded-full"
            sx={{
              "&.MuiButton-contained": {
                backgroundColor: `${colors?.primary} !important`,
                color: "#fff",
              },
              "&.MuiButton-outlined": {
                borderColor: `${colors?.primary} !important`,
                color: "#000",
              },
            }}
          >
            ${denomination.amount}
          </Button>
        ))}
      </div>

      <div>
        <div className="relative w-full">
          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-600 font-medium text-lg pointer-events-none">
            $
          </span>
          <input
            ref={inputRef}
            placeholder="0.00"
            type="text"
            value={formatAmountValue(value)}
            className="w-full border-solid border-2 border-gray-200 rounded-full p-2 pl-8 pr-4 outline-none text-center text-lg font-medium"
            onChange={(e) => {
              const cursorPosition = e.target.selectionStart
              const inputValue = e.target.value.replace(/[^0-9.]/g, '')
              const numValue = parseFloat(inputValue) || 0
              handlePresetAmount(numValue)

              // Restore cursor position after state update
              setTimeout(() => {
                if (inputRef.current && cursorPosition !== null) {
                  inputRef.current.setSelectionRange(cursorPosition, cursorPosition)
                }
              }, 0)
            }}
          />
        </div>
        {errors.amount && (
          <p className="text-red-500 text-sm mt-1 px-2">
            {errors.amount.message}
          </p>
        )}
      </div>

      <div className="flex justify-center">
        <Button type="submit" size="large" className={buttonClassName}>
          {`Donate $${value}`}
        </Button>
      </div>
    </form>
  )
}
