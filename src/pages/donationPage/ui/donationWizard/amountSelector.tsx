import { Button } from "@/shared/ui/Button"
import { InputAdornment, Typography } from "@mui/material"
import { Input } from "@/shared/ui/Input"
import { ChangeEventHandler, useEffect } from "react"
import { useAmountSelectorForm } from "../../model/amountSelector.form"
import { AmountSelectorData } from "../../model/amountSelector.schema"
import { DonationDenomination } from "../../model/useDonationAmounts"
import { onKeyDownFilter } from "../../lib/onKeyDownFilter"
import { formatCentsToDollarStr, formatDollarStrToCents } from "@/shared/lib/"

interface AmountSelectorProps {
  value: number
  onChange: (amount: number) => void
  colors: {
    primary: string
    secondary: string
  }
  onNext: () => void
  denominations: DonationDenomination[]
  buttonClassName: string
}

export const AmountSelector = ({
  value,
  onChange,
  colors,
  onNext,
  denominations,
  buttonClassName,
}: AmountSelectorProps) => {
  const { handleSubmit, setValue, errors, watch } = useAmountSelectorForm(
    formatCentsToDollarStr(value)
  )

  const stringifiedValue = watch("amount")

  useEffect(() => {
    setValue("amount", formatCentsToDollarStr(value))
  }, [value, setValue])

  const onSubmit = (data: AmountSelectorData) => {
    onChange(formatDollarStrToCents(data.amount))
    onNext()
  }

  const onChangeHandler: ChangeEventHandler<HTMLInputElement> = (e) => {
    setValue("amount", e.target.value)
  }

  const onBlurHandler = () => {
    if (!stringifiedValue) {
      setValue("amount", formatCentsToDollarStr(0))
      onChange(0)
      return
    }
    onChange(formatDollarStrToCents(stringifiedValue))
    setValue("amount", parseFloat(stringifiedValue).toFixed(2))
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <Typography
        variant="h6"
        className="!text-xl text-center !font-bold !mb-4"
      >
        Donation Amount
      </Typography>
      <div className="grid grid-cols-4 gap-2">
        {denominations.map((denomination) => (
          <Button
            key={denomination.amount}
            variant={
              Number(value) === Number(denomination.amount)
                ? "contained"
                : "outlined"
            }
            onClick={() => {
              setValue("amount", formatCentsToDollarStr(denomination.amount))
              onChange(denomination.amount)
            }}
            type="button"
            className="h-14 !text-base !rounded-full"
            sx={{
              "&.MuiButton-contained": {
                backgroundColor: `${colors?.primary} !important`,
                color: "#fff",
              },
              "&.MuiButton-outlined": {
                borderColor: `${colors?.primary} !important`,
                color: "#000",
              },
            }}
          >
            ${denomination.amount / 100}
          </Button>
        ))}
      </div>

      <div>
        <div className="relative w-full">
          <Input
            inputMode="decimal"
            onKeyDown={onKeyDownFilter(stringifiedValue)}
            placeholder="0.00"
            type="text"
            value={stringifiedValue}
            className="w-full border-solid border-2 border-gray-200 rounded-full p-2 pl-8 pr-4 outline-none text-center text-lg font-medium"
            onChange={onChangeHandler}
            onBlur={onBlurHandler}
            lang="en-US"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="end">$</InputAdornment>
                ),
                lang: "en-US",
              },
            }}
            sx={{
              "& .MuiInputBase-input": {
                textAlign: "center",
                marginLeft: "-1em",
              },
            }}
          />
        </div>
        {errors.amount && (
          <p className="text-red-500 text-sm mt-1 px-2">
            {errors.amount.message}
          </p>
        )}
      </div>

      <div className="flex justify-center">
        <Button type="submit" size="large" className={buttonClassName}>
          {`Donate $${stringifiedValue}`}
        </Button>
      </div>
    </form>
  )
}
