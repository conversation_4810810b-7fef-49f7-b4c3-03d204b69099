import { DonationDenominationDto } from "@/entities/campaign"
import { But<PERSON> } from "@/shared/ui/Button"
import { Typography } from "@mui/material"
import { useEffect } from "react"
import { useAmountSelectorForm } from "../../model/amountSelector.form"
import { AmountSelectorData } from "../../model/amountSelector.schema"

interface AmountSelectorProps {
  value: number
  onChange: (amount: number) => void
  colors: {
    primary: string
    secondary: string
  }
  onNext: () => void
  denominations: DonationDenominationDto[]
  buttonClassName: string
}

export const AmountSelector = ({
  value,
  onChange,
  colors,
  onNext,
  denominations,
  buttonClassName,
}: AmountSelectorProps) => {
  const { register, handleSubmit, setValue, errors } =
    useAmountSelectorForm(value)

  useEffect(() => {
    setValue("amount", value)
  }, [value, setValue])

  const onSubmit = (data: AmountSelectorData) => {
    onChange(data.amount)
    onNext()
  }

  const handlePresetAmount = (amount: number) => {
    setValue("amount", amount)
    onChange(amount)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <Typography
        variant="h6"
        className="!text-xl text-center !font-bold !mb-4"
      >
        Donation Amount
      </Typography>
      <div className="grid grid-cols-4 gap-2">
        {denominations.map((denomination) => (
          <Button
            key={denomination.amount}
            variant={
              value === Number(denomination.amount) ? "contained" : "outlined"
            }
            onClick={() => handlePresetAmount(Number(denomination.amount))}
            type="button"
            className="h-14 !text-base !rounded-full"
            sx={{
              "&.MuiButton-contained": {
                backgroundColor: `${colors?.primary} !important`,
                color: "#fff",
              },
              "&.MuiButton-outlined": {
                borderColor: `${colors?.primary} !important`,
                color: "#000",
              },
            }}
          >
            ${denomination.amount}
          </Button>
        ))}
      </div>

      <div>
        <input
          {...register("amount")}
          placeholder="Custom amount"
          type="number"
          className="w-full border-solid border-2 border-gray-200 rounded-full p-2 outline-none"
          onChange={(e) => handlePresetAmount(Number(e.target.value))}
        />
        {errors.amount && (
          <p className="text-red-500 text-sm mt-1 px-2">
            {errors.amount.message}
          </p>
        )}
      </div>

      <div className="flex justify-center">
        <Button type="submit" size="large" className={buttonClassName}>
          {`Donate $${value}`}
        </Button>
      </div>
    </form>
  )
}
