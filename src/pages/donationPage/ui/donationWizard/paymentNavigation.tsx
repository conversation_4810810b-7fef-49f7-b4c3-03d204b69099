import { Button, Dialog, IconButton } from "@/shared/ui"
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew"
import CloseIcon from "@mui/icons-material/Close"
import { Typography } from "@mui/material"
import { useState } from "react"

interface PaymentNavigationProps {
  onBack: () => void
  onClose: () => void
  title: string
}

export const PaymentNavigation = ({
  onBack,
  onClose,
  title,
}: PaymentNavigationProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleBack = () => {
    onBack()
  }

  const handleCloseClick = () => {
    setIsOpen(true)
  }
  const handleAbort = () => {
    onClose()
    setIsOpen(false)
  }

  return (
    <div className="flex items-center justify-between gap-2 mb-4 pb-2 border-b-neutral-300 border-b-1">
      <IconButton onClick={handleBack} title="Back">
        <ArrowBackIosNewIcon />
      </IconButton>
      <Typography variant="h6" className="!text-xl !font-bold text-center">
        {title}
      </Typography>
      <IconButton onClick={handleCloseClick} title="Close">
        <CloseIcon />
      </IconButton>
      <Dialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        title="Cancel donation?"
        actions={
          <>
            <Button
              onClick={() => setIsOpen(false)}
              variant="contained"
              fullWidth={false}
            >
              Not now
            </Button>
            <Button onClick={handleAbort} variant="contained" fullWidth={false}>
              Cancel donation
            </Button>
          </>
        }
      >
        <Typography className="!mb-4">
          Cancel donation? All information will be lost.
        </Typography>
      </Dialog>
    </div>
  )
}
