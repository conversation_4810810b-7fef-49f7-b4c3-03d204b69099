import { queryKeys } from "@/shared/model"
import { CircularProgress } from "@mui/material"
import { useQueryClient } from "@tanstack/react-query"
import { toast } from "react-toastify"
import { STEPS } from "../../model/donationSteps.const"
import { PersonalInfoFormData } from "../../model/personalInfo.schema"
import { useDonationForm } from "../../model/useDonationForm"
import { useDonationSearchParams } from "../../model/useDonationSearchParams"
import { useDonationSteps } from "../../model/useDonationSteps"
import { DonationInfo, useDonationSubmit } from "../../model/useDonationSubmit"
import { AmountSelector } from "./amountSelector"
import {
  SquareBillingInfoStep,
  SquareErrorStep,
  SquareWrapper,
  type BillingInfoFormData,
} from "@/features/paymentProviders/square"

import { PaymentMethod } from "./paymentMethod"
import { PersonalInfo } from "./personalInfo"
import { SuccessState } from "./successState"
import { TipConfirmation } from "./tipConfirmatioin"
import { TipOther } from "./tipOther"
import { TipSelector } from "./tipSelector"
import { PaymentNavigation } from "./paymentNavigation"
import { useDonationPageData } from "../../model/useDonationPageData"
import { useDonationAmounts } from "../../model/useDonationAmounts"

interface DonationWizardComponentProps {
  setIsMobileDonationWizardOpen: (isOpen: boolean) => void
  leaderboardPage: number
  leaderboardTab: "donors" | "players" | "groups"
  buttonClassName: string
}

export const DonationWizardComponent = ({
  setIsMobileDonationWizardOpen,
  leaderboardPage,
  leaderboardTab,
  buttonClassName,
}: DonationWizardComponentProps) => {
  const queryClient = useQueryClient()

  const { campaignData: data, guid } = useDonationPageData()
  const {
    tipPercentages,
    denominationsCents,
    defaultOtherTipCents,
    defaultDenominationAmountCents,
    defaultTipPercentage,
  } = useDonationAmounts(data)

  const colors = {
    primary: data?.colors?.primary ?? "#000000",
    secondary: data?.colors?.secondary ?? "#ffffff",
  }

  const { donationInviteId, campaignUserId } = useDonationSearchParams()

  const {
    amountCents,
    setAmountCents,
    tipPercentage,
    setTipPercentage,
    setPaymentMethod,
    personalInfo,
    setPersonalInfo,
    calculatedTipAmountCents,
    totalDonationAmountCents,
    billingInfo,
    setBillingInfo,
    setOtherTipCents,
    otherTipCents,
  } = useDonationForm(defaultDenominationAmountCents)

  const {
    step,
    setStep,
    errors,
    handleAmountNext,
    handlePaymentMethodChange,
    handlePersonalInfoSubmit,
    handleDonateAgain,
    handleError,
    handleTipSubmit,
    handleTipOther,
    handleTipLastChance,
    onBack,
    handleTipOtherBack,
    handleTipBack,
  } = useDonationSteps({
    tippingEnabled: data?.tipping_enabled ?? false,
    setOtherTipCents,
    setTipPercentage,
    defaultTipPercentage,
    defaultOtherTipCents,
  })

  const onAbort = () => {
    onPersonalInfoSubmit({
      name: "",
      lastName: "",
      email: "",
      phone: "",
    })
    setBillingInfo({
      nameOnCard: "",
      address: "",
      country: "",
      state: "",
      city: "",
    })
    setTipPercentage(defaultTipPercentage)
    setAmountCents(defaultDenominationAmountCents)
    setStep(STEPS.amount)
    setIsMobileDonationWizardOpen(false)
  }

  const { handleDonate, isDonating } = useDonationSubmit({
    campaignId: data?.campaign_id,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.campaign.byGuid, guid],
      })
      if (leaderboardTab === "donors") {
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.campaign.donationLeaderboard.donors,
            data?.campaign_id,
            leaderboardPage,
          ],
        })
      } else if (leaderboardTab === "players") {
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.campaign.donationLeaderboard.players,
            data?.campaign_id,
            leaderboardPage,
          ],
        })
      } else if (leaderboardTab === "groups") {
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.campaign.donationLeaderboard.groups,
            data?.campaign_id,
            leaderboardPage,
          ],
        })
      }
      setStep(STEPS.success)
    },
    onError: handleError,
  })

  const onPaymentMethodChange = (method: string) => {
    // TODO: remove this once the other payment method is implemented
    if (method === "Other") {
      toast.error("Will be implemented soon")
      return
    }
    setPaymentMethod(method)
    handlePaymentMethodChange()
  }

  const onPersonalInfoSubmit = (data: PersonalInfoFormData) => {
    setPersonalInfo(data)
    handlePersonalInfoSubmit()
  }

  const handleSubmitDonation = (data: {
    paymentMethodId: string
    billingInfo: BillingInfoFormData
    zip: string
    paymentType: string
    get_way: string
  }) => {
    setBillingInfo(data.billingInfo)

    const payload: DonationInfo = {
      paymentMethodId: data.paymentMethodId,
      amountCents,
      calculatedTipAmountCents,
      totalDonationAmountCents,
      donation_method: data.paymentType,
      zip: data.zip,
      message: personalInfo.message || "",
      isAnonymous: !!personalInfo.isAnonymous,
      name: personalInfo.name,
      lastName: personalInfo.lastName,
      email: personalInfo.email,
      phone: personalInfo.phone,
      address: data.billingInfo.address,
      state: data.billingInfo.state,
      country: data.billingInfo.country,
      city: data.billingInfo.city,
      nameOnCard: data.billingInfo.nameOnCard,
      get_way: data.get_way,
    }

    if (donationInviteId) {
      payload.donationInviteId = Number(donationInviteId)
    } else if (campaignUserId) {
      payload.campaignUserId = Number(campaignUserId)
    }

    handleDonate(payload)
  }

  return (
    <div className="flex flex-col gap-4">
      {isDonating && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}
      <SquareWrapper>
        {step === STEPS.amount && (
          <AmountSelector
            value={amountCents}
            denominations={denominationsCents ?? []}
            onChange={setAmountCents}
            colors={colors}
            onNext={handleAmountNext}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.paymentMethod && (
          <PaymentMethod
            onChange={onPaymentMethodChange}
            onBack={() => onBack(STEPS.amount)}
            onAbort={onAbort}
          />
        )}
        {step === STEPS.personalInfo && (
          <PersonalInfo
            personalInfo={personalInfo}
            onSubmit={onPersonalInfoSubmit}
            onBack={() => onBack(STEPS.paymentMethod)}
            onAbort={onAbort}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.tip && (
          <TipSelector
            tipPercentages={tipPercentages ?? []}
            value={tipPercentage}
            onChange={setTipPercentage}
            colors={colors}
            onNext={handleTipSubmit}
            onTipOther={handleTipOther}
            totalDonationAmountCents={totalDonationAmountCents}
            onBack={handleTipBack}
            onAbort={onAbort}
            buttonClassName={buttonClassName}
            teamDisplayName={data?.team_display_name ?? ""}
          />
        )}
        {step === STEPS.tipOther && (
          <TipOther
            onChange={setOtherTipCents}
            onNext={handleTipLastChance}
            otherTipCents={otherTipCents}
            onBack={handleTipOtherBack}
            onAbort={onAbort}
            buttonClassName={buttonClassName}
            teamDisplayName={data?.team_display_name ?? ""}
          />
        )}
        {step === STEPS.tipConfirmation && (
          <TipConfirmation
            otherTipCents={otherTipCents}
            onNext={handleTipSubmit}
            onChange={setOtherTipCents}
            onBack={() => onBack(STEPS.tipOther)}
            onAbort={onAbort}
            buttonClassName={buttonClassName}
            teamDisplayName={data?.team_display_name ?? ""}
          />
        )}
        {step === STEPS.billingInfo && (
          <SquareBillingInfoStep
            totalDonationAmountCents={totalDonationAmountCents}
            onSubmit={handleSubmitDonation}
            billingInfo={billingInfo}
            buttonClassName={buttonClassName}
            navigationSlot={
              <PaymentNavigation
                title="Billing Info"
                onBack={() =>
                  onBack(data?.tipping_enabled ? STEPS.tip : STEPS.personalInfo)
                }
                onClose={onAbort}
              />
            }
          />
        )}
        {step === STEPS.success && (
          <SuccessState
            totalDonationAmountCents={totalDonationAmountCents}
            onDonateAgain={handleDonateAgain}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.error && (
          <SquareErrorStep
            onDonateAgain={handleDonateAgain}
            errors={errors}
            buttonClassName={buttonClassName}
          />
        )}
      </SquareWrapper>
    </div>
  )
}
