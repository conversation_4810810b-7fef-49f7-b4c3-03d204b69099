import {
  DonationDenominationDto,
  TippingPercentageDto,
} from "@/entities/campaign"
import { queryKeys } from "@/shared/model"
import { CircularProgress } from "@mui/material"
import { Elements } from "@stripe/react-stripe-js"
import { useQueryClient } from "@tanstack/react-query"
import { toast } from "react-toastify"
import { BillingInfoFormData } from "../../model/billingInfo.schema"
import { STEPS } from "../../model/donationSteps.const"
import { PersonalInfoFormData } from "../../model/personalInfo.schema"
import { stripePromise } from "../../model/stripe.const"
import { useDonationForm } from "../../model/useDonationForm"
import { useDonationSearchParams } from "../../model/useDonationSearchParams"
import { useDonationSteps } from "../../model/useDonationSteps"
import { DonationInfo, useDonationSubmit } from "../../model/useDonationSubmit"
import { AmountSelector } from "./amountSelector"
import { BillingInfo } from "./billingInfo"
import { ErrorState } from "./errorState"
import { PaymentMethod } from "./paymentMethod"
import { PersonalInfo } from "./personalInfo"
import { SuccessState } from "./successState"
import { TipConfirmation } from "./tipConfirmatioin"
import { TipOther } from "./tipOther"
import { TipSelector } from "./tipSelector"

interface DonationWizardComponentProps {
  colors: {
    primary: string
    secondary: string
  }
  tipAmount: TippingPercentageDto[]
  otherTipPercentage: string
  denominations: DonationDenominationDto[]
  campaignId?: number
  tippingEnabled: boolean
  guid: string
  setIsMobileDonationWizardOpen: (isOpen: boolean) => void
  leaderboardPage: number
  leaderboardTab: "donors" | "players" | "groups"
  buttonClassName: string
  teamDisplayName: string
}

export const DonationWizardComponent = ({
  colors,
  tipAmount,
  otherTipPercentage,
  denominations,
  campaignId,
  tippingEnabled,
  guid,
  setIsMobileDonationWizardOpen,
  leaderboardPage,
  leaderboardTab,
  buttonClassName,
  teamDisplayName,
}: DonationWizardComponentProps) => {
  const queryClient = useQueryClient()

  const { donationInviteId, campaignUserId } = useDonationSearchParams()

  const lowestDenominationAmount = denominations?.[0]?.amount ?? "0"
  const midTipAmount = tippingEnabled ? tipAmount?.[1]?.value : "0"

  const {
    amount,
    setAmount,
    tip,
    setTip,
    setPaymentMethod,
    personalInfo,
    setPersonalInfo,
    calculatedTip,
    totalDonationAmount,
    calculatedOtherTipAmount,
    billingInfo,
    setBillingInfo,
  } = useDonationForm(
    lowestDenominationAmount,
    midTipAmount ?? "0",
    otherTipPercentage
  )

  const {
    step,
    setStep,
    errors,
    handleAmountNext,
    handlePaymentMethodChange,
    handlePersonalInfoSubmit,
    handleDonateAgain,
    handleError,
    handleTipSubmit,
    handleTipOther,
    handleTipLastChance,
    onBack,
  } = useDonationSteps(tippingEnabled)

  const onAbort = () => {
    onPersonalInfoSubmit({
      name: "",
      lastName: "",
      email: "",
      phone: "",
    })
    setBillingInfo({
      nameOnCard: "",
      address: "",
      country: "",
      state: "",
      city: "",
    })
    setTip(Number(midTipAmount))
    setAmount(Number(lowestDenominationAmount))
    setStep(STEPS.amount)
    setIsMobileDonationWizardOpen(false)
  }

  const { handleDonate, isDonating } = useDonationSubmit({
    campaignId,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.campaign.byGuid, guid],
      })
      if (leaderboardTab === "donors") {
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.campaign.donationLeaderboard.donors,
            campaignId,
            leaderboardPage,
          ],
        })
      } else if (leaderboardTab === "players") {
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.campaign.donationLeaderboard.players,
            campaignId,
            leaderboardPage,
          ],
        })
      } else if (leaderboardTab === "groups") {
        queryClient.invalidateQueries({
          queryKey: [
            queryKeys.campaign.donationLeaderboard.groups,
            campaignId,
            leaderboardPage,
          ],
        })
      }
      setStep(STEPS.success)
    },
    onError: handleError,
  })

  const onPaymentMethodChange = (method: string) => {
    // TODO: remove this once the other payment method is implemented
    if (method === "Other") {
      toast.error("Will be implemented soon")
      return
    }
    setPaymentMethod(method)
    handlePaymentMethodChange()
  }

  const onPersonalInfoSubmit = (data: PersonalInfoFormData) => {
    setPersonalInfo(data)
    handlePersonalInfoSubmit()
  }

  const handleSubmitDonation = (data: {
    paymentMethodId: string
    billingInfo: BillingInfoFormData
    zip: string
    paymentType: string
    get_way: string
  }) => {
    setBillingInfo(data.billingInfo)

    const payload: DonationInfo = {
      paymentMethodId: data.paymentMethodId,
      amount,
      calculatedTip,
      totalDonationAmount,
      donation_method: data.paymentType,
      zip: data.zip,
      message: personalInfo.message || "",
      isAnonymous: !!personalInfo.isAnonymous,
      name: personalInfo.name,
      lastName: personalInfo.lastName,
      email: personalInfo.email,
      phone: personalInfo.phone,
      address: data.billingInfo.address,
      state: data.billingInfo.state,
      country: data.billingInfo.country,
      city: data.billingInfo.city,
      nameOnCard: data.billingInfo.nameOnCard,
      get_way: data.get_way,
    }

    if (donationInviteId) {
      payload.donationInviteId = Number(donationInviteId)
    } else if (campaignUserId) {
      payload.campaignUserId = Number(campaignUserId)
    }

    handleDonate(payload)
  }

  return (
    <div className="flex flex-col gap-4">
      {isDonating && (
        <div className="fixed inset-0 bg-gray-600/50 backdrop-blur-sm flex items-center justify-center z-50">
          <CircularProgress size={48} />
        </div>
      )}
      <Elements stripe={stripePromise}>
        {step === STEPS.amount && (
          <AmountSelector
            value={amount}
            denominations={denominations}
            onChange={setAmount}
            colors={colors}
            onNext={handleAmountNext}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.paymentMethod && (
          <PaymentMethod
            onChange={onPaymentMethodChange}
            onBack={() => onBack(STEPS.amount)}
            onAbort={onAbort}
          />
        )}
        {step === STEPS.personalInfo && (
          <PersonalInfo
            personalInfo={personalInfo}
            onSubmit={onPersonalInfoSubmit}
            onBack={() => onBack(STEPS.paymentMethod)}
            onAbort={onAbort}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.tip && (
          <TipSelector
            tipAmount={tipAmount}
            value={tip}
            onChange={setTip}
            otherTipPercentage={otherTipPercentage}
            colors={colors}
            onNext={handleTipSubmit}
            onTipOther={handleTipOther}
            totalDonationAmount={totalDonationAmount}
            onBack={() => onBack(STEPS.personalInfo)}
            onAbort={onAbort}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.tipOther && (
          <TipOther
            onChange={setTip}
            calculatedTip={calculatedTip}
            onNext={handleTipLastChance}
            tip={tip}
            onBack={() => onBack(STEPS.tip)}
            onAbort={onAbort}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.tipConfirmation && (
          <TipConfirmation
            calculatedOtherTipAmount={calculatedOtherTipAmount}
            otherTipPercentage={otherTipPercentage}
            onNext={handleTipSubmit}
            onChange={setTip}
            onBack={() => onBack(STEPS.tipOther)}
            onAbort={onAbort}
            buttonClassName={buttonClassName}
            teamDisplayName={teamDisplayName}
          />
        )}
        {step === STEPS.billingInfo && (
          <BillingInfo
            totalDonationAmount={totalDonationAmount}
            onSubmit={handleSubmitDonation}
            billingInfo={billingInfo}
            onBack={() =>
              onBack(tippingEnabled ? STEPS.tip : STEPS.personalInfo)
            }
            onAbort={onAbort}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.success && (
          <SuccessState
            totalDonationAmount={totalDonationAmount}
            onDonateAgain={handleDonateAgain}
            buttonClassName={buttonClassName}
          />
        )}
        {step === STEPS.error && (
          <ErrorState
            onDonateAgain={handleDonateAgain}
            errors={errors}
            buttonClassName={buttonClassName}
          />
        )}
      </Elements>
    </div>
  )
}
