export const onKeyDownFilter =
  (stringifiedValue: string) => (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      e.key === "Backspace" ||
      e.key === "Delete" ||
      e.key === "ArrowLeft" ||
      e.key === "ArrowRight" ||
      e.key === "Tab"
    ) {
      return
    }
    // Allow only digits and one dot
    if (
      !/[0-9.]/.test(e.key) ||
      (e.key === "." && stringifiedValue.includes("."))
    ) {
      e.preventDefault()
    }
  }
