import * as yup from "yup"

export const billingInfoSchema = yup.object().shape({
  nameOnCard: yup
    .string()
    .transform((value) => value?.trim())
    .required("Name on Card is required"),
  address: yup
    .string()
    .transform((value) => value?.trim())
    .required("Address is required"),
  country: yup
    .string()
    .transform((value) => value?.trim())
    .required("Country is required"),
  state: yup
    .string()
    .transform((value) => value?.trim())
    .required("State is required"),
  city: yup
    .string()
    .transform((value) => value?.trim())
    .required("City is required"),
})

export type BillingInfoFormData = yup.InferType<typeof billingInfoSchema>
