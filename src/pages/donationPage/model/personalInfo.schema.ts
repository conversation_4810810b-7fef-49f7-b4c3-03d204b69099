import * as yup from "yup"

export const personalInfoSchema = yup.object().shape({
  name: yup
    .string()
    .transform((value) => value?.trim())
    .required("Name is required"),
  lastName: yup
    .string()
    .transform((value) => value?.trim())
    .required("Last Name is required"),
  email: yup
    .string()
    .transform((value) => value?.trim())
    .email("Please enter a valid email")
    .required("Email is required"),
  phone: yup
    .string()
    .transform((value) => value?.trim())
    .required("Phone is required")
    .matches(
      /^(\+?1[-\s]?)?\(?([0-9]{3})\)?[-\s]?([0-9]{3})[-\s]?([0-9]{4})$/,
      "Please enter a valid phone number"
    ),
  message: yup
    .string()
    .transform((value) => value?.trim())
    .max(250, "Message must be less than 250 characters"),
  isAnonymous: yup.boolean(),
})

export type PersonalInfoFormData = yup.InferType<typeof personalInfoSchema>
