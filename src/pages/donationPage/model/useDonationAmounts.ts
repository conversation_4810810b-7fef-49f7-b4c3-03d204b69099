import { CampaignDto, DonationDenominationDto } from "@/entities/campaign"
import { useMemo } from "react"

interface TipAmount {
  id: number
  campaign_id: number
  option_name: string
  value: string
  created_at: string
  updated_at: string
}

export const useDonationAmounts = (data: CampaignDto | undefined) => {
  const tipAmount = useMemo(() => {
    const filteredTipAmount = data?.tippingPercentages?.filter(
      (amount) => amount.option_name !== "other_percentage"
    )
    const sortedTipAmount = filteredTipAmount?.sort(
      (a: TipAmount, b: TipAmount) => Number(a.value) - Number(b.value)
    )

    return sortedTipAmount?.map((amount) => ({
      ...amount,
      value: amount.value.split(".")[0],
    }))
  }, [data?.tippingPercentages, data?.campaign_id])

  const otherTipPercentage = useMemo(() => {
    const filteredTipAmount = data?.tippingPercentages?.filter(
      (amount) => amount.option_name === "other_percentage"
    )
    return filteredTipAmount?.[0]?.value
  }, [data?.tippingPercentages])

  const denominations = useMemo(() => {
    const sortedDenominations = data?.donationDenominations?.sort(
      (a: DonationDenominationDto, b: DonationDenominationDto) =>
        Number(a.amount) - Number(b.amount)
    )
    return sortedDenominations?.map((denomination) => ({
      ...denomination,
      amount: denomination.amount.split(".")[0],
    }))
  }, [data?.donationDenominations])

  return {
    tipAmount,
    denominations,
    otherTipPercentage,
  }
}
