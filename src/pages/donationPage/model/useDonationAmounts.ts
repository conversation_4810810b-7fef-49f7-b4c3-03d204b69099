import { CampaignDto, TippingPercentageDto } from "@/entities/campaign"
import { useMemo } from "react"
import { formatDollarStrToCents } from "@/shared/lib"

export interface TipAmount {
  id: number
  campaign_id: number
  option_name: string
  value: number
  created_at: string
  updated_at: string
}

export interface DonationDenomination {
  id: number
  campaign_id: number
  amount: number
  default: boolean
  created_at: string
  updated_at: string
}

export const useDonationAmounts = (data: CampaignDto | undefined) => {
  const tipPercentages = useMemo(() => {
    const filteredTipAmount = data?.tippingPercentages?.filter(
      (amount) => amount.option_name !== "other"
    )
    const sortedTipAmount = filteredTipAmount?.sort(
      (a: TippingPercentageDto, b: TippingPercentageDto) =>
        parseInt(a.value) - parseInt(b.value)
    )

    return sortedTipAmount?.map((amount) => ({
      ...amount,
      value: parseInt(amount.value),
    })) as TipAmount[]
  }, [data?.tippingPercentages])

  const defaultOtherTipCents = useMemo(() => {
    const filteredTipAmount = data?.tippingPercentages?.filter(
      (amount) => amount.option_name === "other"
    )

    if (!filteredTipAmount) {
      console.error("Other tip amount not found")
      return 0
    }

    return formatDollarStrToCents(filteredTipAmount[0].value)
  }, [data?.tippingPercentages])

  const denominationsCents = useMemo(() => {
    return data?.donationDenominations?.map((denomination) => ({
      ...denomination,
      amount: formatDollarStrToCents(denomination.amount),
    }))
  }, [data?.donationDenominations])

  const defaultDenominationAmountCents =
    denominationsCents?.find((denomination) => denomination.default)?.amount ??
    denominationsCents?.[0].amount ??
    0
  const defaultTipPercentage = data?.tipping_enabled
    ? tipPercentages?.[1]?.value
    : 0

  return {
    tipPercentages,
    denominationsCents,
    defaultOtherTipCents,
    defaultDenominationAmountCents,
    defaultTipPercentage,
  }
}
