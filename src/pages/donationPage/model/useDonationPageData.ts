import {
  CampaignDto,
  DonationUserDto,
  useGetCampaignByGuid,
  useGetDonationUser,
  useGetDonationUserByInviteId,
} from "@/entities/campaign"
import { useParams } from "react-router-dom"
import { useDonationSearchParams } from "./useDonationSearchParams"

export const useDonationPageData = () => {
  const { guid } = useParams()
  const { donationInviteId, campaignUserId } = useDonationSearchParams()

  const { data: campaign, isLoading: isLoadingCampaign } = useGetCampaignByGuid(
    guid as string,
    !donationInviteId && !campaignUserId
  )

  const { data: donationUserByInviteId, isLoading: isLoadingByInviteId } =
    useGetDonationUserByInviteId(guid as string, Number(donationInviteId))

  const { data: donationUserByUserId, isLoading: isLoadingByUserId } =
    useGetDonationUser(guid as string, Number(campaignUserId))

  const isLoading =
    isLoadingCampaign || isLoadingByInviteId || isLoadingByUserId

  const campaignData: DonationUserDto | CampaignDto | undefined =
    donationUserByInviteId || donationUserByUserId || campaign

  let avatarUrl = undefined
  if (donationInviteId || campaignUserId) {
    const data = campaignData as DonationUserDto
    avatarUrl = data?.campaign_user_info?.images[0]?.image_url
  }

  return {
    campaignData: campaignData as DonationUserDto | CampaignDto | undefined,
    isLoading,
    guid,
    avatarUrl,
    donationInviteId,
    campaignUserId,
  }
}
