import * as yup from "yup"

export const shareSchema = yup
  .object()
  .shape({
    email: yup
      .string()
      .transform((value) => value?.trim())
      .email("Please enter a valid email")
      .nullable(),
    phone: yup
      .string()
      .transform((value) => value?.trim())
      .nullable(),
  })
  .test("at-least-one", "Either email or phone is required", function (value) {
    return !!(value.email || value.phone)
  })

export type ShareFormData = yup.InferType<typeof shareSchema>
