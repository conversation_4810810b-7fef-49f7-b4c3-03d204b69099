import { useDonate } from "@/entities/campaignDenomination"
import { ApiError } from "@/shared/model"

interface UseDonationSubmitProps {
  campaignId?: number
  onSuccess: () => void
  onError: (errorMessages: string[]) => void
}

export interface DonationInfo {
  paymentMethodId: string
  donation_method: string
  amount: number
  calculatedTip: number
  totalDonationAmount: number
  name: string
  lastName: string
  email: string
  phone: string
  message: string
  isAnonymous: boolean
  address: string
  state: string
  country: string
  city: string
  nameOnCard: string
  zip: string
  donationInviteId?: number
  campaignUserId?: number
  get_way: string
}

export const useDonationSubmit = ({
  campaignId,
  onSuccess,
  onError,
}: UseDonationSubmitProps) => {
  const { mutate: donate, isPending: isDonating } = useDonate({
    onSuccess: () => {
      onSuccess()
    },
    onError: (error: ApiError) => {
      const errorMessages = error.response?.data?.errors ?? [
        "An error occurred",
      ]
      onError(
        // @ts-expect-error fix ApiError type on Backend
        Array.isArray(errorMessages)
          ? errorMessages
          : Object.values(errorMessages)
      )
    },
  })

  const handleDonate = (donationInfo: DonationInfo) => {
    if (!campaignId) {
      onError(["Campaign ID is required"])
      return
    }

    donate({
      campaign_id: campaignId,
      donation_method: donationInfo.donation_method,
      base_donation_amount: donationInfo.amount,
      tip_amount: donationInfo.calculatedTip,
      total_donation_amount: donationInfo.totalDonationAmount,
      payment_method_id: donationInfo.paymentMethodId,
      donor_first_name: donationInfo.name,
      donor_last_name: donationInfo.lastName,
      donor_email: donationInfo.email,
      donor_phone_number: donationInfo.phone,
      message: donationInfo.message,
      is_anonymous: donationInfo.isAnonymous,
      address: donationInfo.address,
      state: donationInfo.state,
      city: donationInfo.city,
      country: donationInfo.country,
      name_on_card: donationInfo.nameOnCard,
      zip: donationInfo.zip,
      campaign_donation_invite_id: donationInfo.donationInviteId,
      campaign_user_id: donationInfo.campaignUserId,
      get_way: donationInfo.get_way,
    })
  }

  return {
    handleDonate,
    isDonating,
  }
}
