import { useEffect, useState } from "react"
import { PersonalInfoFormData } from "./personalInfo.schema"
import { BillingInfoFormData } from "@/features/paymentProviders/square"

export const useDonationForm = (defaultDenominationAmountCents: number) => {
  // NOTE: Amount is in cents
  const [amountCents, setAmountCents] = useState(defaultDenominationAmountCents)
  // NOTE: Tip is in percentage
  const [tipPercentage, setTipPercentage] = useState(0)
  // NOTE Other tip is in cents
  const [otherTipCents, setOtherTipCents] = useState(0)
  const [paymentMethod, setPaymentMethod] = useState("Credit Card")
  const [personalInfo, setPersonalInfo] = useState<PersonalInfoFormData>({
    name: "",
    lastName: "",
    email: "",
    phone: "",
    isAnonymous: false,
  })
  const [billingInfo, setBillingInfo] = useState<BillingInfoFormData>({
    nameOnCard: "",
    address: "",
    country: "",
    state: "",
    city: "",
  })

  useEffect(() => {
    setAmountCents(defaultDenominationAmountCents)
  }, [defaultDenominationAmountCents])

  const calculatedTipAmountCents = (amountCents * tipPercentage) / 100
  const calculatedOtherTipAmountCents = amountCents + otherTipCents

  const totalDonationAmountCents =
    amountCents + calculatedTipAmountCents + otherTipCents

  return {
    amountCents,
    setAmountCents,
    tipPercentage,
    setTipPercentage,
    setOtherTipCents,
    otherTipCents,
    paymentMethod,
    setPaymentMethod,
    personalInfo,
    setPersonalInfo,
    calculatedTipAmountCents,
    totalDonationAmountCents,
    calculatedOtherTipAmountCents,
    billingInfo,
    setBillingInfo,
  }
}
