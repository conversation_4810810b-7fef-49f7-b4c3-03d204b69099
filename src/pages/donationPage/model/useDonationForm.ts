import { useEffect, useState } from "react"
import { BillingInfoFormData } from "./billingInfo.schema"
import { PersonalInfoFormData } from "./personalInfo.schema"

export const useDonationForm = (
  initialAmount: string,
  initialTip: string,
  initialTipOther: string
) => {
  const [amount, setAmount] = useState(Number(initialAmount))
  const [tip, setTip] = useState(Number(initialTip))
  const [paymentMethod, setPaymentMethod] = useState("Credit Card")
  const [personalInfo, setPersonalInfo] = useState<PersonalInfoFormData>({
    name: "",
    lastName: "",
    email: "",
    phone: "",
    isAnonymous: false,
  })
  const [billingInfo, setBillingInfo] = useState<BillingInfoFormData>({
    nameOnCard: "",
    address: "",
    country: "",
    state: "",
    city: "",
  })

  useEffect(() => {
    setAmount(Number(initialAmount))
    setTip(Number(initialTip))
  }, [initialAmount, initialTip])

  const calculatedTip = (amount * tip) / 100
  const totalDonationAmount = amount + calculatedTip
  const calculatedOtherTipAmount = (amount * Number(initialTipOther)) / 100

  return {
    amount,
    setAmount,
    tip,
    setTip,
    paymentMethod,
    setPaymentMethod,
    personalInfo,
    setPersonalInfo,
    calculatedTip,
    totalDonationAmount,
    calculatedOtherTipAmount,
    billingInfo,
    setBillingInfo,
  }
}
