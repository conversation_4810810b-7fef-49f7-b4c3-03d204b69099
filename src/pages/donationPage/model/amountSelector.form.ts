import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  AmountSelectorData,
  amountSelectorSchema,
} from "./amountSelector.schema"

export const useAmountSelectorForm = (defaultAmount: string = "25.00") => {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    ...form
  } = useForm<AmountSelectorData>({
    resolver: yupResolver(amountSelectorSchema),
    defaultValues: {
      amount: defaultAmount,
    },
  })

  return {
    register,
    handleSubmit,
    setValue,
    errors,
    ...form,
  }
}
