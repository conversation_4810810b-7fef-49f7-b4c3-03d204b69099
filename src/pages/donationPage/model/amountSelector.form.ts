import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  AmountSelectorData,
  amountSelectorSchema,
} from "./amountSelector.schema"

export const useAmountSelectorForm = (defaultAmount: number = 25) => {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<AmountSelectorData>({
    resolver: yupResolver(amountSelectorSchema),
    defaultValues: {
      amount: defaultAmount,
    },
  })

  return {
    register,
    handleSubmit,
    setValue,
    errors,
  }
}
