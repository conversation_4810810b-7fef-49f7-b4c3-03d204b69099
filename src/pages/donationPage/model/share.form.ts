import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { ShareFormData, shareSchema } from "./share.schema"

export const useShareForm = () => {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<ShareFormData>({
    resolver: yupResolver(shareSchema),
  })

  return {
    register,
    handleSubmit,
    errors,
    setValue,
  }
}
