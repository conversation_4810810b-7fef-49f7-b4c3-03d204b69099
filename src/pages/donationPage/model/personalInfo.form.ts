import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import { PersonalInfoFormData, personalInfoSchema } from "./personalInfo.schema"

export const usePersonalInfoForm = (personalInfo: PersonalInfoFormData) => {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
  } = useForm<PersonalInfoFormData>({
    resolver: yupResolver(personalInfoSchema),
    defaultValues: personalInfo,
  })

  return {
    register,
    handleSubmit,
    errors,
    control,
    getValues,
  }
}
