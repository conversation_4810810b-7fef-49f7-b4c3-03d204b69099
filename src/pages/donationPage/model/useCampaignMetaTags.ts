import { CampaignDto } from "@/entities/campaign"
import { useMetaTags } from "@/shared/model"
import { useEffect } from "react"

export const useCampaignMetaTags = (data: CampaignDto | undefined) => {
  const { setMetaTags } = useMetaTags()

  useEffect(() => {
    if (data) {
      const metaDescription = `Join us in making a difference! Support our fundraising campaign for ${data?.team_display_name} and help them reach their goal. Donate today and make a positive impact!`

      setMetaTags({
        title: data?.name || "Support Our Fundraiser!",
        description: data?.team_display_name
          ? metaDescription
          : "Join us in making a difference!",
        image:
          "https://fundraising.aktivate.com/hubfs/Aktivate_Horz_RGB_Color-2.svg",
        url: window.location.href,
      })
    }
    return () => {
      setMetaTags({
        title: "",
        description: "",
        image: "",
        url: "",
      })
    }
  }, [data, setMetaTags])
}
