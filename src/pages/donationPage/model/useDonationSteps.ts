import { useState } from "react"
import { STEPS, Step } from "./donationSteps.const"

export const useDonationSteps = ({
  tippingEnabled,
  setOtherTipCents,
  setTipPercentage,
  defaultTipPercentage,
  defaultOtherTipCents,
}: {
  tippingEnabled: boolean
  setOtherTipCents: (value: number) => void
  setTipPercentage: (value: number) => void
  defaultTipPercentage: number
  defaultOtherTipCents: number
}) => {
  const [step, setStep] = useState<Step>(STEPS.amount)
  const [errors, setErrors] = useState<string[]>([])

  const handleAmountNext = () => {
    setStep(STEPS.paymentMethod)
  }

  const handlePaymentMethodChange = () => {
    setStep(STEPS.personalInfo)
  }

  const handlePersonalInfoSubmit = () => {
    if (tippingEnabled) {
      setTipPercentage(defaultTipPercentage)
      setOtherTipCents(0)
      setStep(STEPS.tip)
    } else {
      setStep(STEPS.billingInfo)
    }
  }

  const handleTipSubmit = () => {
    setStep(STEPS.billingInfo)
  }

  const handleDonateAgain = () => {
    setStep(STEPS.amount)
  }

  const handleError = (errorMessages: string[]) => {
    setErrors(errorMessages)
    setStep(STEPS.error)
  }

  const handleTipOther = () => {
    setOtherTipCents(defaultOtherTipCents)
    setTipPercentage(0)
    setStep(STEPS.tipOther)
  }

  const handleTipOtherBack = () => {
    setTipPercentage(defaultTipPercentage)
    setOtherTipCents(0)
    setStep(STEPS.tip)
  }

  const handleTipBack = () => {
    setTipPercentage(0)
    setStep(STEPS.personalInfo)
  }

  const handleTipLastChance = (value: number) => {
    if (value >= 1) {
      setStep(STEPS.billingInfo)
    } else {
      setOtherTipCents(defaultOtherTipCents)
      setStep(STEPS.tipConfirmation)
    }
  }

  const onBack = (step: Step) => {
    setStep(step)
  }

  return {
    step,
    setStep,
    errors,
    handleAmountNext,
    handlePaymentMethodChange,
    handlePersonalInfoSubmit,
    handleTipSubmit,
    handleDonateAgain,
    handleError,
    handleTipOther,
    handleTipLastChance,
    onBack,
    handleTipOtherBack,
    handleTipBack,
  }
}
