import { useState } from "react"
import { STEPS, Step } from "./donationSteps.const"

export const useDonationSteps = (tippingEnabled: boolean) => {
  const [step, setStep] = useState<Step>(STEPS.amount)
  const [errors, setErrors] = useState<string[]>([])

  const handleAmountNext = () => {
    setStep(STEPS.paymentMethod)
  }

  const handlePaymentMethodChange = () => {
    setStep(STEPS.personalInfo)
  }

  const handlePersonalInfoSubmit = () => {
    if (tippingEnabled) {
      setStep(STEPS.tip)
    } else {
      setStep(STEPS.billingInfo)
    }
  }

  const handleTipSubmit = () => {
    setStep(STEPS.billingInfo)
  }

  const handleDonateAgain = () => {
    setStep(STEPS.amount)
  }

  const handleError = (errorMessages: string[]) => {
    setErrors(errorMessages)
    setStep(STEPS.error)
  }

  const handleTipOther = () => {
    setStep(STEPS.tipOther)
  }

  const handleTipLastChance = (value: number) => {
    if (value >= 1) {
      setStep(STEPS.billingInfo)
    } else {
      setStep(STEPS.tipConfirmation)
    }
  }

  const onBack = (step: Step) => {
    setStep(step)
  }

  return {
    step,
    setStep,
    errors,
    handleAmountNext,
    handlePaymentMethodChange,
    handlePersonalInfoSubmit,
    handleTipSubmit,
    handleDonateAgain,
    handleError,
    handleTipOther,
    handleTipLastChance,
    onBack,
  }
}
