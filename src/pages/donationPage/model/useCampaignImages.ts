import {
  useGetCampaignActionShot,
  useGetCampaignHero,
  useGetCampaignLogo,
} from "@/entities/campaignImage"

export const useCampaignImages = (campaignId: number) => {
  const { data: logo } = useGetCampaignLogo(campaignId)
  const { data: actionShot } = useGetCampaignActionShot(campaignId)
  const { data: hero } = useGetCampaignHero(campaignId)

  return {
    logo,
    actionShot,
    hero: hero,
  }
}
