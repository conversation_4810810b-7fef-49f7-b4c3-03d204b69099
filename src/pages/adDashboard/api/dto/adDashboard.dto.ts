export interface CampaignSummaryAdDto {
  total_raised: number
  total_fees: number
  total_profit: number
}

export interface CampaignDonationAdDto {
  id: number | null
  created_at: string
  campaign_id: number
  payment_status: string | null
  base_donation_amount: string
  donor_first_name: string
  donor_last_name: string
  donor_email: string
  donor_phone_number: string
  campaign_donation_invite_id: number | null
  campaign_user_id: number | null
  message: string | null
  is_anonymous: boolean | null
  name_on_card: string | null
  country: string | null
  state: string | null
  city: string | null
  zip: string | null
  address: string | null
  donation_method: string
  invite_path: string
  who_invited_first_name: string | null
  who_invited_last_name: string | null
  who_invited_full_name: string | null
  who_invited_role: string | null
  coaches: string[]
}

export interface CampaignTotalsAdDto {
  id: number
  name: string
  team_display_name: string
  total_raised: number
  status: string
}

export interface CampaignDonationFilterAdDto {
  donation_methods: string[]
  members: string[]
  date_range: {
    min_date: string
    max_date: string
  }
}
