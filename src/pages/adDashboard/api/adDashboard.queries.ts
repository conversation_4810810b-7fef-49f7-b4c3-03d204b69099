import { API, apiUrls } from "@/shared/api"
import { PaginatedResponse, PaginationParams } from "@/shared/model"
import {
  CampaignSummaryAdDto,
  CampaignDonationAdDto,
  CampaignTotalsAdDto,
  CampaignDonationFilterAdDto,
} from "./dto/adDashboard.dto"
import { CampaignDonationsParams } from "../model/adDashboard.types"

export const adApi = {
  getSummary: async (): Promise<CampaignSummaryAdDto> => {
    const response = await API.get(apiUrls.ad.summary())
    return response.data
  },

  getDonations: async (
    params?: PaginationParams
  ): Promise<PaginatedResponse<CampaignDonationAdDto>> => {
    const response = await API.get(apiUrls.ad.donations.list(), {
      params,
    })
    return response.data
  },

  exportDonations: async (params: CampaignDonationsParams): Promise<Blob> => {
    const response = await API.get(apiUrls.ad.donations.export(), {
      params,
    })

    const blob = new Blob([response.data], { type: "text/csv" })

    return blob
  },

  getFilters: async (): Promise<CampaignDonationFilterAdDto> => {
    const response = await API.get(apiUrls.ad.donations.filter())

    return {
      ...response.data,
      data: response.data.data,
    }
  },

  getTotals: async (
    params: PaginationParams
  ): Promise<PaginatedResponse<CampaignTotalsAdDto>> => {
    const response = await API.get(apiUrls.ad.totals(), {
      params,
    })
    return response.data
  },
}
