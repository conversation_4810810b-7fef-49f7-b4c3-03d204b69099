import { PaginationParams, queryKeys } from "@/shared/model"
import { useMutation, useQuery } from "@tanstack/react-query"
import { adApi } from "../api/adDashboard.queries"
import { CampaignDonationsParams } from "./adDashboard.types"
import { toast } from "react-toastify"

export const useGetAdSummary = () => {
  return useQuery({
    queryKey: [queryKeys.athleticDirectorDashboard.summary],
    queryFn: () => adApi.getSummary(),
    staleTime: 0,
    refetchOnMount: true,
  })
}

export const useGetAdDonations = (
  params?: CampaignDonationsParams & PaginationParams
) => {
  return useQuery({
    queryKey: [queryKeys.athleticDirectorDashboard.donations, params],
    queryFn: () => adApi.getDonations(params),
  })
}

export const useExportAdDonations = () => {
  return useMutation({
    mutationFn: (params: CampaignDonationsParams) =>
      adApi.exportDonations(params),
    onSuccess: (...args) => {
      // Create download link
      const url = window.URL.createObjectURL(args[0] as Blob)
      const link = document.createElement("a")
      link.href = url
      link.download = `ad-donations-export-${new Date().toISOString().split("T")[0]}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success("Donations exported successfully")
    },
  })
}

export const useGetAdTotals = (params: PaginationParams) => {
  return useQuery({
    queryKey: [queryKeys.athleticDirectorDashboard.totals],
    queryFn: () => adApi.getTotals(params),
    staleTime: 0,
    refetchOnMount: true,
  })
}

export const useGetAdDonationFilters = () => {
  return useQuery({
    queryKey: [queryKeys.athleticDirectorDashboard.donationFilters],
    queryFn: adApi.getFilters,
    staleTime: 0,
    refetchOnMount: true,
  })
}
