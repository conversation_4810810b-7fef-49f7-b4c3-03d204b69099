import { GridRenderCellParams } from "@mui/x-data-grid"

export const getColumns = () => {
  return [
    {
      field: "team_display_name",
      headerName: "Team Display Name",
      flex: 2,
      sortable: false,
      filterable: false,
    },
    {
      field: "total_raised",
      headerName: "Total Raised",
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) =>
        `$${params.row.total_raised?.toLocaleString() || "0"}`,
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            params.row.status === "active"
              ? "bg-green-100 text-green-800"
              : params.row.status === "completed"
                ? "bg-blue-100 text-blue-800"
                : "bg-gray-100 text-gray-800"
          }`}
        >
          {params.row.status}
        </span>
      ),
    },
  ]
}
