import { DashboardLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib/usePageTitle"

import { DonationsWidget } from "./donationsWidget"
import { CampaignsTotalsWidget } from "./campaignsTotalsWidget"
import { useGetAdSummary } from "../model/adDashboard.hooks"
import CampaignsSummaryWidget from "./campaignsSummaryWidget"

const AdDashboard = () => {
  usePageTitle("Coach Dashboard")

  const { data: campaignSummary, isLoading: isLoadingCampaignSummary } =
    useGetAdSummary()

  const { total_raised, total_fees, total_profit } = campaignSummary ?? {}

  const colors = { primary: "#ec7b1a", secondary: "#ad4120" }

  const isLoading = isLoadingCampaignSummary

  return (
    <DashboardLayout isLoading={isLoading}>
      <div className="grid grid-cols-1 md:grid-cols-2 mx-[30px] py-6 gap-4">
        <div>
          <CampaignsSummaryWidget
            total_raised={total_raised}
            total_fees={total_fees}
            total_profit={total_profit}
          />

          <CampaignsTotalsWidget />
        </div>

        <DonationsWidget colors={colors} />
      </div>
    </DashboardLayout>
  )
}

export default AdDashboard
