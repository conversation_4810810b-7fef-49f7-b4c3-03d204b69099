import { Table } from "@/shared/ui"
import { Typography } from "@mui/material"
import { useMemo } from "react"
import { v4 as uuidv4 } from "uuid"
import { useNavigate } from "react-router"
import { getColumns } from "../model/campaignTotalsWidget.columns"
import { useGetAdTotals } from "../model/adDashboard.hooks"
import { usePagination } from "@/shared/model"
import { PATH } from "@/shared/config"

export const CampaignsTotalsWidget = () => {
  const navigate = useNavigate()
  const { pageSize, page, onPaginationModelChange } = usePagination()
  const { data: campaignTotals, isLoading } = useGetAdTotals({
    page,
    per_page: pageSize,
  })

  const currentPage = campaignTotals?.meta?.current_page
    ? campaignTotals.meta.current_page - 1
    : 0

  const columns = useMemo(() => getColumns(), [])

  const rows = useMemo(
    () =>
      campaignTotals?.data?.map((row) => ({
        ...row,
        id: row.id || uuidv4(),
      })) || [],
    [campaignTotals]
  )

  const handleRowClick = (params: { row: { id: number } }) => {
    const campaignId = params.row.id
    if (campaignId) {
      navigate(PATH.withAuth.campaign.dashboard.url(campaignId))
    }
  }

  return (
    <div className="bg-white shadow-md p-6 rounded-lg container relative">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Typography
            className="!text-3xl !font-bold !tracking-tight"
            variant="h2"
          >
            Campaigns Totals
          </Typography>
        </div>
        <Table
          columns={columns}
          rows={rows}
          loading={isLoading}
          boxClassName="md:h-[590px]"
          hideFooter={false}
          disableColumnMenu
          disableRowSelectionOnClick
          onPaginationModelChange={onPaginationModelChange}
          pageSize={pageSize}
          page={currentPage}
          onRowClick={handleRowClick}
        />
      </div>
    </div>
  )
}
