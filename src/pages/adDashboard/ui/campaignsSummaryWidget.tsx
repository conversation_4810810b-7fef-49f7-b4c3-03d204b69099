import { Paper, Typography } from "@mui/material"

function campaignsSummaryWidget({
  total_raised,
  total_fees,
  total_profit,
}: {
  total_raised?: number
  total_fees?: number
  total_profit?: number
}) {
  return (
    <Paper className="p-6 mb-4 !shadow-md">
      <Typography
        className="!text-3xl !font-bold !tracking-tight !mb-4"
        variant="h2"
      >
        Campaigns Summary
      </Typography>
      <Typography className="!text-sm mb-3!" variant="body1">
        Total Raised:{" "}
        <span className="font-bold">
          {total_raised ? `$${total_raised}` : "N/A"}
        </span>
      </Typography>
      <Typography className="!text-sm mb-3!" variant="body1">
        Total Fees:{" "}
        <span className="font-bold">
          {total_fees ? `$${total_fees}` : "N/A"}
        </span>
      </Typography>
      <Typography className="!text-sm mb-3!" variant="body1">
        Total Profit:{" "}
        <span className="font-bold">
          {total_profit ? `$${total_profit}` : "N/A"}
        </span>
      </Typography>
    </Paper>
  )
}

export default campaignsSummaryWidget
