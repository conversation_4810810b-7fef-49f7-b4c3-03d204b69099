import { yupResolver } from "@hookform/resolvers/yup"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import {
  MessageLogsReportForm,
  messageLogsReportSchema,
} from "./messageLogsReport.schema"

export const useMessageLogsReportForm = (
  defaultValues: MessageLogsReportForm
) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    getValues,
    setValue,
  } = useForm<MessageLogsReportForm>({
    resolver: yupResolver(messageLogsReportSchema),
    defaultValues,
  })

  useEffect(() => {
    const { start_date, end_date, message_status } = getValues()

    if (!start_date && !end_date && !message_status) {
      setValue("start_date", defaultValues?.start_date)
      setValue("end_date", defaultValues?.end_date)
      setValue("message_status", defaultValues?.message_status)
    }
  }, [defaultValues, reset, getValues])

  return {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    getValues,
  }
}
