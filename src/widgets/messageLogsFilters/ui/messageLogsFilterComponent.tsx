import { useGetMessageLogFilters } from "@/entities/campaign"
import { <PERSON>complete, Button } from "@/shared/ui"
import { DatePickerField } from "@/shared/ui/DatePickerField"
import { useMessageLogsReportForm } from "../model/messageLogsReport.form"
import { MessageLogsReportForm } from "../model/messageLogsReport.schema"

interface MessageLogsFilterComponentProps {
  messageLogsFilter: MessageLogsReportForm
  onSubmit: (data: MessageLogsReportForm) => void
  onReset: () => void
}
export const MessageLogsFilterComponent = ({
  messageLogsFilter,
  onSubmit,
  onReset,
}: MessageLogsFilterComponentProps) => {
  const { control, handleSubmit, reset } =
    useMessageLogsReportForm(messageLogsFilter)

  const { data: messageLogsFilters, isLoading: isLoadingMessageLogsFilters } =
    useGetMessageLogFilters()

  const messageLogsStatusOptions = messageLogsFilters?.statuses.map(
    (status: string) => ({
      label: status,
      value: status,
    })
  )

  const handleReset = () => {
    reset()
    onReset()
  }

  return (
    <form
      className="flex w-full flex-wrap gap-4 mb-12 md:mb-4 justify-between"
      onSubmit={handleSubmit(onSubmit)}
    >
      <div className="flex w-full flex-wrap gap-4">
        <div className="w-full mb-4 md:mb-0 md:w-48">
          <DatePickerField
            name="start_date"
            label="Date From"
            control={control}
            fieldVariant="standard"
          />
        </div>
        <div className="w-full mb-4 md:mb-0 md:w-48">
          <DatePickerField
            name="end_date"
            label="Date To"
            control={control}
            fieldVariant="standard"
          />
        </div>
        <div className="w-full md:w-48">
          <Autocomplete
            name="message_status"
            label="Message Status"
            options={messageLogsStatusOptions || []}
            control={control}
            isLoading={isLoadingMessageLogsFilters}
            fieldVariant="standard"
            disableClearable
          />
        </div>

        <div className="flex flex-col md:flex-row w-full md:w-auto gap-4 py-5">
          <div className="h-full w-full md:w-24 gap-4 flex items-end">
            <Button variant="contained" color="primary" type="submit">
              Apply
            </Button>
            <Button variant="contained" color="primary" onClick={handleReset}>
              Reset
            </Button>
          </div>
        </div>
      </div>
    </form>
  )
}
