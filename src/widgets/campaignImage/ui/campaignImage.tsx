import {
  useDeleteCampaignImage,
  useDeleteCampaignUserImage,
  useGetCampaignImagesByType,
  useGetCampaignUserImagesByType,
  useUpdateCampaignImage,
  useUpdateCampaignUserImage,
} from "@/entities/campaignMoment"
import {
  CampaignImageType,
  CampaignImageTypeById,
  CampaignImageTypes,
  Helpers,
  useCampaignStore,
  useGetCampaignId,
} from "@/shared/model"
import { asyncConfirm, Button, UploadImage } from "@/shared/ui"
import { Typography } from "@mui/material"
import React, { useCallback } from "react"
import { toast } from "react-toastify"

interface Props {
  type: Exclude<CampaignImageTypes, typeof CampaignImageType.action_shot>
  useGetImage:
    | typeof useGetCampaignUserImagesByType
    | typeof useGetCampaignImagesByType
  useUpdateImage:
    | typeof useUpdateCampaignUserImage
    | typeof useUpdateCampaignImage
  useDeleteImage:
    | typeof useDeleteCampaignUserImage
    | typeof useDeleteCampaignImage
  campaignUserIdProp?: number
  circularCrop?: boolean
}

export const CampaignImage: React.FC<Props> = ({
  type = CampaignImageType.portrait,
  useGetImage,
  useUpdateImage,
  useDeleteImage,
  campaignUserIdProp,
  circularCrop = false,
}) => {
  const campaignId = useGetCampaignId()!
  const { selectedCampaign } = useCampaignStore()
  const campaignUserId =
    campaignUserIdProp ?? Number(selectedCampaign?.campaign_user_id)

  const { data, isLoading: isLoadingPortrait } = useGetImage(
    type,
    Number(campaignId),
    campaignUserId
  )

  const { mutate: updatePortrait, isPending: isUpdatingPortrait } =
    useUpdateImage({
      onSuccess: () => {
        toast.success(`${type} image updated successfully`)
      },
      onError: () => {
        toast.error(`Failed to update ${type} image`)
      },
    })

  const { mutate: deleteImage, isPending: isDeleting } = useDeleteImage({
    onSuccess: () => {
      toast.success("Image was deleted successfully")
    },
    onError: () => {
      toast.error("Failed to delete image")
    },
  })
  const onLoadPortrait = (image: Blob) => {
    const data = Helpers.objectToFormData({
      image,
      image_type_id: CampaignImageTypeById[type],
    })

    updatePortrait({
      campaignId: Number(campaignId),
      campaignUserId,
      data,
      type,
    })
  }

  const onDelete = useCallback(
    async (id: number) => {
      const confirm = await asyncConfirm({
        title: "Delete image",
        message: "Are you sure you want to delete this image?",
      })

      if (!confirm) return

      deleteImage({
        campaignId: Number(campaignId),
        campaignUserId,
        imageId: id,
        type,
      })
    },
    [campaignId, campaignUserId, type, deleteImage]
  )

  const item = data?.[0]
  const itemSrc = item ? item?.image?.s3_url : ""
  const itemId = item?.image_id

  return (
    <>
      <Typography variant="h5" className="first-letter:uppercase">
        {type}
      </Typography>
      <div className="max-w-[250px] inline-block">
        <UploadImage
          src={itemSrc}
          className="bordered mt-6"
          onSubmit={onLoadPortrait}
          loading={isLoadingPortrait}
          textButton={`Upload ${type} image`}
          isUploading={isUpdatingPortrait}
          circularCrop={circularCrop}
        />
        {itemId && (
          <div className="mt-4">
            <Button
              variant="outlined"
              loading={isDeleting}
              onClick={() => onDelete(itemId)}
            >
              Delete {type} image
            </Button>
          </div>
        )}
      </div>
    </>
  )
}
