import { Dialog, Table } from "@/shared/ui"
import { useCampaignGroupsTableData } from "../model/emailTemplates.hooks"
import { CampaignMessageTemplateDto } from "@/entities/campaignMessageTemplate"
import { useState } from "react"
import { CircularProgress } from "@mui/material"
import { toast } from "react-toastify"
import DOMPurify from "dompurify"

interface Props {
  data: CampaignMessageTemplateDto[]
  isLoading: boolean
  handleEdit: (id: number) => void
  getDetails: (id: number) => Promise<CampaignMessageTemplateDto>
}

export const MessageTemplates = ({
  data,
  isLoading,
  handleEdit,
  getDetails,
}: Props) => {
  const [open, setOpen] = useState(false)
  const [preview, setPreview] = useState<
    CampaignMessageTemplateDto | undefined
  >()
  const [loading, setLoading] = useState(false)

  const handleShow = async (id: number) => {
    try {
      setOpen(true)
      setLoading(true)
      const data = await getDetails(id)
      setPreview(data)
      setLoading(false)
    } catch (err) {
      setLoading(false)
      setOpen(false)
      toast.error("Failed to open preview")
      console.log(err)
    }
  }

  const { columns, rows } = useCampaignGroupsTableData({
    onEdit: handleEdit,
    onShow: handleShow,
    data: data ?? [],
  })

  return (
    <>
      <Table
        columns={columns}
        rows={rows}
        rowCount={rows?.length || 0}
        loading={isLoading}
        hideFooter
      />
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="md"
        title="Template details"
      >
        {(loading && (
          <div className="min-h-[100px] flex items-center justify-center">
            <CircularProgress />
          </div>
        )) || (
          <div
            className="whitespace-pre-wrap break-words max-w-full"
            dangerouslySetInnerHTML={{
              __html: preview?.body ? DOMPurify.sanitize(preview.body) : "",
            }}
          />
        )}
      </Dialog>
    </>
  )
}
