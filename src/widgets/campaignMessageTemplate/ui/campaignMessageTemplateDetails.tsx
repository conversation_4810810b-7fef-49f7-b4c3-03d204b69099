import { useEffect, useMemo } from "react"
import { RichTextEditor } from "@/shared/ui/richTextEditor"
import { CircularProgress, Box, Paper } from "@mui/material"
import { Input, <PERSON><PERSON>, Wrapper } from "@/shared/ui"
import { useTemplateForm } from "../model/emailTemplateDetails.form"
import { EmailTemplateFormData } from "../model/emailTemplateDetails.schema"
import {
  useGetCampaignMessageTemplateById,
  useUpdateCampaignMessageTemplate,
} from "@/entities/campaignMessageTemplate"
import { toast } from "react-toastify"
import { PATH } from "@/shared/config"
import { useNavigate } from "react-router-dom"

interface Props {
  campaignId: string
  templateId: string
  type: "email" | "sms"
  onBack?: () => void
  backTitle?: string
}

export const CampaignMessageTemplateDetails = ({
  campaignId,
  templateId,
  type,
  onBack,
  backTitle,
}: Props) => {
  const { data, isLoading } = useGetCampaignMessageTemplateById(
    Number(campaignId),
    Number(templateId)
  )

  const navigate = useNavigate()
  const initialContent = useMemo(() => data?.[0]?.body, [data])
  const initialValues = useMemo(
    () => ({
      subject: data?.[0]?.subject || "",
      body: data?.[0]?.body || "",
    }),
    [data]
  )

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors, isDirty },
  } = useTemplateForm(initialValues)

  useEffect(() => {
    if (initialContent) {
      setValue("body", initialContent)
    }
  }, [setValue, initialContent])

  const { mutate: updateTemplate, isPending } =
    useUpdateCampaignMessageTemplate({
      onSuccess: (_, values) => {
        toast.success("Template updated successfully")
        reset(values.body)

        if (type === "email") {
          navigate(PATH.withAuth.campaign.emailTemplates.list.url(campaignId!))
        } else {
          navigate(PATH.withAuth.campaign.smsTemplates.list.url(campaignId!))
        }
      },
      onError: () => {
        toast.error("Failed to update template")
      },
    })

  const onChange = (content: string) => {
    setValue("body", content, {
      shouldDirty: true,
      shouldValidate: true,
      shouldTouch: true,
    })
  }

  const onSubmit = (values: EmailTemplateFormData) => {
    updateTemplate({
      campaignId: Number(campaignId),
      templateId: Number(templateId),
      body: values,
    })
  }

  return (
    <Wrapper isForm onBack={onBack} backTitle={backTitle}>
      {(isLoading && (
        <div className="flex items-center justify-center">
          <CircularProgress size={48} />
        </div>
      )) || (
        <div>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="flex items-center gap-4">
              {type !== "sms" && (
                <div className="w-1/2">
                  <Input
                    {...register("subject")}
                    label="Subject"
                    error={!!errors.subject}
                    helperText={errors.subject?.message}
                    variant="outlined"
                    disabled={isLoading}
                  />
                </div>
              )}
            </div>
            {type === "email" && (
              <RichTextEditor
                initialContent={initialContent}
                onChange={onChange}
              />
            )}
            {type === "sms" && (
              <Paper variant="outlined">
                <Box className="min-h-[300px] p-2">
                  <textarea
                    className="min-h-[300px] min-w-full outline-none resize-none"
                    {...register("body")}
                  />
                </Box>
              </Paper>
            )}

            <div className="mt-4">
              <Button
                variant="contained"
                type="submit"
                disabled={isLoading || !isDirty || isPending}
                loading={isPending}
              >
                update
              </Button>
            </div>
          </form>
        </div>
      )}
    </Wrapper>
  )
}
