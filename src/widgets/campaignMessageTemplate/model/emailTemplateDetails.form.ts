import { yupResolver } from "@hookform/resolvers/yup"
import { useForm } from "react-hook-form"
import {
  EmailTemplateFormData,
  emailTemplateSchema,
} from "./emailTemplateDetails.schema"
import { useEffect } from "react"

export const useTemplateForm = (initialData?: EmailTemplateFormData) => {
  const form = useForm<EmailTemplateFormData>({
    resolver: yupResolver(emailTemplateSchema),
    defaultValues: initialData,
  })

  useEffect(() => {
    if (!form.formState.isDirty && initialData) {
      form.reset(initialData)
    }
  }, [form, initialData])

  return form
}
