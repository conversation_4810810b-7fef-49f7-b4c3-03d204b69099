import { useMemo } from "react"
import { getColumns } from "./emailTemplates.columns"
import { CampaignMessageTemplateDto } from "@/entities/campaignMessageTemplate"
import { useLocation } from "react-router-dom"

interface Props {
  onEdit: (id: number) => void
  onShow: (id: number) => void
  data: CampaignMessageTemplateDto[]
}

export const useCampaignGroupsTableData = ({ onEdit, onShow, data }: Props) => {
  const location = useLocation()

  const templateType: "email-templates" | "sms-templates" =
    location.pathname.includes("sms-templates") ? "sms-templates" : "email-templates"
  const columns = useMemo(
    () => getColumns({ onEdit, onShow, templateType }),
    [onEdit, onShow, templateType]
  )

  const rows =
    useMemo(() => data?.map((row) => ({ ...row, id: row.id })), [data]) ?? []

  return {
    columns,
    rows,
  }
}
