import { IconButton } from "@/shared/ui"
import EditIcon from "@mui/icons-material/Edit"
import VisibilityIcon from "@mui/icons-material/Visibility"
import { GridRenderCellParams } from "@mui/x-data-grid"

interface GetColumnsProps {
  onEdit: (id: number) => void
  onShow: (id: number) => void
  templateType: "email-templates" | "sms-templates"
}

export const getColumns = ({
  onEdit,
  onShow,
  templateType,
}: GetColumnsProps) => {
  return [
    {
      field: "id",
      headerName: "ID",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    {
      field: "template_name",
      headerName: "Template name",
      flex: 1,
      sortable: false,
      filterable: false,
    },
    ...(templateType === "email-templates"
      ? [
          {
            field: "subject",
            headerName: "Subject",
            flex: 1,
            sortable: false,
            filterable: false,
          },
        ]
      : []),
    {
      field: "actions",
      headerName: "Actions",
      width: 140,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => (
        <>
          <IconButton
            onClick={() => onShow(params.row.template_id)}
            title="View"
          >
            <VisibilityIcon />
          </IconButton>
          <IconButton
            onClick={() => onEdit(params.row.template_id)}
            title="Edit"
          >
            <EditIcon />
          </IconButton>
        </>
      ),
    },
  ]
}
