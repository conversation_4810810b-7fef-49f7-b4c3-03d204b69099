import { CampaignDonationLeaderboardMeta } from "@/entities/campaignDonationLeaderboard"
import { IconButton } from "@/shared/ui"
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft"
import ChevronRightIcon from "@mui/icons-material/ChevronRight"
import ExpandMoreIcon from "@mui/icons-material/ExpandMore"
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  CircularProgress,
  Typography,
} from "@mui/material"
import { getTeamRankBadgeColor } from "./styles"

interface TeamData {
  id: string
  name: string
  amount: string
  players: {
    id: string
    name: string
    amount: string
  }[]
}

interface TeamsProps {
  teamData: TeamData[]
  currentPage: number
  setCurrentPage: (page: number) => void
  groupsMeta?: CampaignDonationLeaderboardMeta
  isTeamsLoading: boolean
  itemsPerPage: number
}

export const Teams = ({
  teamData,
  groupsMeta,
  currentPage,
  setCurrentPage,
  isTeamsLoading,
  itemsPerPage,
}: TeamsProps) => {
  const totalPages = groupsMeta?.last_page || 1

  return (
    <>
      <div className="space-y-6">
        {teamData?.map((data, index) => {
          const globalRank = (currentPage - 1) * itemsPerPage + index + 1

          return (
            <div key={data.id}>
              <Accordion>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls={`panel${globalRank}-content`}
                  id={`panel${globalRank}-header`}
                >
                  <div className="flex w-full items-center justify-between">
                    <div className="flex items-center">
                      <div
                        className={`flex items-center justify-center w-6 h-6 rounded-full ${globalRank <= 3 ? "text-white" : ""} font-bold text-sm mr-3 ${getTeamRankBadgeColor(
                          globalRank - 1
                        )}`}
                      >
                        {globalRank}
                      </div>
                      <div className="text-xl font-bold tracking-tight">
                        {data.name}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="text-xl font-bold tracking-tight">
                        ${data.amount}
                      </span>
                    </div>
                  </div>
                </AccordionSummary>
                <AccordionDetails>
                  <div className="ml-2">
                    {data.players
                      .sort((a, b) => Number(b.amount) - Number(a.amount))
                      .map((player, playerIndex) => (
                        <div
                          key={player.id}
                          className="flex items-center justify-between h-[36px]"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-5 text-gray-900 font-medium tracking-tight">
                              {playerIndex + 1}
                            </div>
                            <span className="font-medium tracking-tight">
                              {player.name}
                            </span>
                          </div>
                          <div>
                            <span className="font-bold tracking-tight">
                              ${player.amount}
                            </span>
                          </div>
                        </div>
                      ))}
                  </div>
                </AccordionDetails>
              </Accordion>
            </div>
          )
        })}
        {teamData?.length === 0 && !isTeamsLoading && (
          <div className="flex items-center justify-center h-[200px]">
            <Typography variant="h6">No donations yet</Typography>
          </div>
        )}
        {isTeamsLoading && (
          <div className="flex items-center justify-center h-[200px]">
            <CircularProgress />
          </div>
        )}
      </div>

      {totalPages > 1 && (
        <div className="flex justify-between items-center mt-10 pt-4">
          <IconButton
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-12 w-12 border rounded-md"
          >
            <ChevronLeftIcon className="h-6 w-6" />
          </IconButton>
          <div className="text-base font-medium tracking-tight">
            Page {currentPage} of {totalPages}
          </div>
          <IconButton
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="h-12 w-12 border rounded-md"
          >
            <ChevronRightIcon className="h-6 w-6" />
          </IconButton>
        </div>
      )}
    </>
  )
}
