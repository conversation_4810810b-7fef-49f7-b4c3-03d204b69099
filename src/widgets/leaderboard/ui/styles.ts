export const getRankBadgeColor = (rank: number) => {
  switch (rank) {
    case 1:
      return "bg-amber-500"
    case 2:
      return "bg-gray-400"
    case 3:
      return "bg-[#CD7F32]"
    default:
      return "bg-gray-200 text-gray-700"
  }
}

export const getTextStyles = (rank: number, isAmount = false) => {
  if (rank === 1) {
    return isAmount ? "text-xl" : "text-base"
  }
  if (rank > 3) {
    return "text-gray-700"
  }
  return ""
}

export const getContainerStyles = (rank: number) => {
  const baseStyles = "flex i flex-col relative py-3"
  const bgStyle = rank > 1 ? "bg-accent/50" : ""
  const marginStyle = rank === 3 ? "mb-6" : "mb-1"

  return `${baseStyles} ${bgStyle} ${marginStyle}`
}

export const getTeamRankBadgeColor = (index: number) => {
  switch (index) {
    case 0:
      return "bg-amber-500"
    case 1:
      return "bg-gray-400"
    case 2:
      return "bg-[#CD7F32]"
    default:
      return "bg-gray-200"
  }
}
