import { CampaignDonationLeaderboardMeta } from "@/entities/campaignDonationLeaderboard"
import { formatCurrency } from "@/shared/lib/formatCurrency"
import { IconButton } from "@/shared/ui"
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft"
import ChevronRightIcon from "@mui/icons-material/ChevronRight"
import { CircularProgress, Typography } from "@mui/material"
import { getRankBadgeColor, getTextStyles } from "./styles"

interface Props {
  donorsData: {
    id: string
    name: string
    amount: string
    message: string
  }[]
  donorsMeta?: CampaignDonationLeaderboardMeta
  currentPage: number
  setCurrentPage: (page: number) => void
  itemsPerPage: number
  isDonorsLoading: boolean
}

export const Donors = ({
  donorsData,
  donorsMeta,
  currentPage,
  setCurrentPage,
  itemsPerPage,
  isDonorsLoading,
}: Props) => {
  const totalPages = donorsMeta?.last_page || 1

  return (
    <>
      <div>
        {donorsData.map((donor, index) => {
          const globalRank = (currentPage - 1) * itemsPerPage + index + 1
          return (
            <div
              key={donor.id}
              className={`flex flex-col relative py-3 ${
                globalRank > 1 ? "bg-accent/50" : ""
              } ${globalRank === 3 ? "mb-6" : "mb-1"}`}
            >
              <div className="flex items-center justify-between w-full mb-1">
                <div className="flex items-center gap-2 pl-1">
                  <div
                    className={`flex items-center justify-center w-6 h-6 rounded-full ${getRankBadgeColor(
                      globalRank
                    )} font-bold text-sm ${globalRank <= 3 ? "text-white" : ""}`}
                  >
                    {globalRank}
                  </div>
                  <span
                    className={`${getTextStyles(globalRank)} font-medium tracking-tight`}
                  >
                    {donor.name}
                  </span>
                </div>
                <div className="px-4">
                  <span
                    className={`${getTextStyles(globalRank, true)} font-bold tracking-tight`}
                  >
                    ${formatCurrency(Number(donor.amount))}
                  </span>
                </div>
              </div>
              {donor.message ? (
                <div className="px-9">
                  <Typography className="text-sm w-fit text-gray-500 bg-gray-100 rounded-md p-2 border border-gray-200">
                    {donor.message}
                  </Typography>
                </div>
              ) : (
                ""
              )}
            </div>
          )
        })}
        {donorsData?.length === 0 && !isDonorsLoading && (
          <div className="flex items-center justify-center h-[200px]">
            <Typography variant="h6">No donations yet</Typography>
          </div>
        )}
        {isDonorsLoading && (
          <div className="flex items-center justify-center h-[200px]">
            <CircularProgress />
          </div>
        )}
      </div>
      {totalPages > 1 && (
        <div className="flex justify-between items-center mt-10 pt-4">
          <IconButton
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-12 w-12 border rounded-md"
          >
            <ChevronLeftIcon className="h-6 w-6" />
          </IconButton>
          <div className="text-base font-medium tracking-tight">
            Page {currentPage} of {totalPages}
          </div>
          <IconButton
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="h-12 w-12 border rounded-md"
          >
            <ChevronRightIcon className="h-6 w-6" />
          </IconButton>
        </div>
      )}
    </>
  )
}
