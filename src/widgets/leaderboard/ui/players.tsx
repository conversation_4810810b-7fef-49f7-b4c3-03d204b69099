import { CampaignDonationLeaderboardMeta } from "@/entities/campaignDonationLeaderboard"
import { formatCurrency } from "@/shared/lib/formatCurrency"
import { IconButton } from "@/shared/ui"
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft"
import ChevronRightIcon from "@mui/icons-material/ChevronRight"
import { CircularProgress, Typography } from "@mui/material"
import { getContainerStyles, getRankBadgeColor, getTextStyles } from "./styles"

interface Props {
  playersData: {
    id: string
    name: string
    amount: string
    donorsCount: number
  }[]
  playersMeta?: CampaignDonationLeaderboardMeta
  currentPage: number
  setCurrentPage: (page: number) => void
  isPlayersLoading: boolean
  itemsPerPage: number
}

export const Players = ({
  playersData,
  playersMeta,
  currentPage,
  setCurrentPage,
  isPlayersLoading,
  itemsPerPage,
}: Props) => {
  const totalPages = playersMeta?.last_page || 1

  return (
    <>
      <div>
        {playersData.map((player, index) => {
          const globalRank = (currentPage - 1) * itemsPerPage + index + 1
          return (
            <div
              key={player.id}
              className={`${getContainerStyles(globalRank)}`}
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2 pl-1">
                  <div
                    className={`flex items-center justify-center w-6 h-6 rounded-full ${getRankBadgeColor(
                      globalRank
                    )} font-bold text-sm ${globalRank <= 3 ? "text-white" : ""}`}
                  >
                    {globalRank}
                  </div>
                  <span
                    className={`${getTextStyles(globalRank)} font-medium tracking-tight`}
                  >
                    {player.name}
                  </span>
                </div>
                <div className="px-4">
                  <span
                    className={`${getTextStyles(globalRank, true)} font-bold tracking-tight`}
                  >
                    ${formatCurrency(Number(player.amount))}
                  </span>
                </div>
              </div>
              <div className="pl-9">
                <span className="text-sm text-gray-500">
                  {`${player.donorsCount} ${
                    player.donorsCount === 1 ? "donor" : "donors"
                  }`}
                </span>
              </div>
            </div>
          )
        })}
        {playersData?.length === 0 && !isPlayersLoading && (
          <div className="flex items-center justify-center h-[200px]">
            <Typography variant="h6">No donations yet</Typography>
          </div>
        )}
        {isPlayersLoading && (
          <div className="flex items-center justify-center h-[200px]">
            <CircularProgress />
          </div>
        )}
      </div>
      {totalPages > 1 && (
        <div className="flex justify-between items-center mt-10 pt-4">
          <IconButton
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-12 w-12 border rounded-md"
          >
            <ChevronLeftIcon className="h-6 w-6" />
          </IconButton>
          <div className="text-base font-medium tracking-tight">
            Page {currentPage} of {totalPages}
          </div>
          <IconButton
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="h-12 w-12 border rounded-md"
          >
            <ChevronRightIcon className="h-6 w-6" />
          </IconButton>
        </div>
      )}
    </>
  )
}
