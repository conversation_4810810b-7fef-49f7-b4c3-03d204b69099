import { useState } from "react"
import { useLeaderboardApi } from "../model/useLeaderboardApi"
import { Donors } from "./donors"
import { Players } from "./players"
import { Teams } from "./teams"

export type LeaderboardTab = "donors" | "players" | "groups"

interface Props {
  campaignId?: number
  setLeaderboardPage: (page: number) => void
  setLeaderboardTab: (tab: LeaderboardTab) => void
  isDashboard?: boolean
}

export const LeaderBoard = ({
  campaignId,
  setLeaderboardPage,
  setLeaderboardTab,
  isDashboard = false,
}: Props) => {
  const [currentTab, setCurrentTab] = useState<LeaderboardTab>("donors")

  const [currentPage, setCurrentPage] = useState({
    donors: 1,
    players: 1,
    groups: 1,
  })

  const {
    donorsList,
    donorsMeta,
    isDonorsLoading,
    groupsList,
    groupsMeta,
    isGroupsLoading,
    isPlayersLoading,
    playersList,
    playersMeta,
  } = useLeaderboardApi({
    campaignId: campaignId as number,
    currentTab,
    page: currentPage[currentTab],
  })

  const handlePageChange =
    (tab: keyof typeof currentPage) => (page: number) => {
      setCurrentPage((prev) => ({
        ...prev,
        [tab]: page,
      }))
      setLeaderboardPage(page)
      setLeaderboardTab(tab)
    }
  const handleTabChange = (tab: keyof typeof currentPage) => {
    setCurrentTab(tab)
    setLeaderboardTab(tab)
  }

  return (
    <div className="bg-white shadow-md p-6 rounded-lg container relative min-h-[350px]">
      <div className="tracking-tight">
        <div className="flex flex-col gap-4 md:flex-row justify-between items-center mb-8">
          <div className="text-3xl font-bold tracking-tight">Leaderboard</div>
          <div
            className={`inline-flex rounded-lg ${
              isDashboard ? "bg-[#ec7b1a]" : "bg-gray-100"
            } p-[1px]`}
          >
            <button
              onClick={() => handleTabChange("donors")}
              className={`px-5 py-2 text-base !text-black font-medium rounded-md ${
                currentTab === "donors" ? "!bg-white" : "!bg-transparent"
              }`}
            >
              Donors
            </button>
            <button
              onClick={() => handleTabChange("players")}
              className={`px-5 py-2 text-base !text-black font-medium rounded-md ${
                currentTab === "players" ? "!bg-white" : "!bg-transparent"
              }`}
            >
              Participants
            </button>
            <button
              onClick={() => handleTabChange("groups")}
              className={`px-5 py-2 text-base !text-black font-medium rounded-md ${
                currentTab === "groups" ? "!bg-white" : "!bg-transparent"
              }`}
            >
              Groups
            </button>
          </div>
        </div>

        {currentTab === "donors" && (
          <Donors
            donorsData={donorsList || []}
            donorsMeta={donorsMeta}
            currentPage={currentPage.donors}
            setCurrentPage={handlePageChange("donors")}
            itemsPerPage={donorsMeta?.per_page || 6}
            isDonorsLoading={isDonorsLoading}
          />
        )}
        {currentTab === "players" && (
          <Players
            playersData={playersList || []}
            playersMeta={playersMeta}
            currentPage={currentPage.players}
            setCurrentPage={handlePageChange("players")}
            isPlayersLoading={isPlayersLoading}
            itemsPerPage={playersMeta?.per_page || 6}
          />
        )}
        {currentTab === "groups" && (
          <Teams
            teamData={groupsList || []}
            groupsMeta={groupsMeta}
            currentPage={currentPage.groups}
            setCurrentPage={handlePageChange("groups")}
            isTeamsLoading={isGroupsLoading}
            itemsPerPage={groupsMeta?.per_page || 3}
          />
        )}
      </div>
    </div>
  )
}
