import {
  useGetDonorsLeaderboard,
  useGetGroupsLeaderboard,
  useGetPlayersLeaderboard,
} from "@/entities/campaignDonationLeaderboard"
import { v4 as uuidv4 } from "uuid"

export const useLeaderboardApi = ({
  campaignId,
  currentTab,
  page,
}: {
  campaignId: number
  currentTab: "donors" | "players" | "groups"
  page: number
}) => {
  const { data: donorsData, isLoading: isDonorsLoading } =
    useGetDonorsLeaderboard(
      campaignId,
      page,
      currentTab === "donors" && !!campaignId
    )
  const { data: playersData, isLoading: isPlayersLoading } =
    useGetPlayersLeaderboard(
      campaignId,
      page,
      currentTab === "players" && !!campaignId
    )
  const { data: groupsData, isLoading: isGroupsLoading } =
    useGetGroupsLeaderboard(
      campaignId,
      page,
      currentTab === "groups" && !!campaignId
    )

  // Donors
  const donorsList = donorsData?.data.map((donor) => {
    const donorName = donor.is_anonymous
      ? "Anonymous"
      : donor.donor_first_name + " " + donor.donor_last_name
    return {
      id: uuidv4(),
      name: donor<PERSON><PERSON>,
      amount: donor.base_donation_amount,
      message: donor.message,
    }
  })
  const donorsMeta = donorsData?.meta

  // Players
  const playersList = playersData?.data.map((player) => {
    return {
      id: uuidv4(),
      name: player.first_name + " " + player.last_name,
      amount: player.total_donation_amount,
      donorsCount: player.count_of_donors,
    }
  })
  const playersMeta = playersData?.meta

  // Groups
  const groupsList = groupsData?.data.map((group) => {
    return {
      id: uuidv4(),
      name: group.group_name,
      amount: group.amount_of_donations,
      players: group.players.map((player) => {
        return {
          id: uuidv4(),
          name: player.first_name + " " + player.last_name,
          amount: player.total_donation_amount,
        }
      }),
    }
  })

  const groupsMeta = groupsData?.meta

  return {
    donorsList,
    donorsMeta,
    isDonorsLoading,
    isPlayersLoading,
    isGroupsLoading,
    playersList,
    playersMeta,
    groupsList,
    groupsMeta,
  }
}
