import { ShareMeta } from "@/shared/ui/"
import { useEffect } from "react"
import AppProvider from "./providers/index"
import { AppRouter } from "./routing/router"
import "./styles/index.css"

function App() {
  useEffect(() => {
    const script = document.createElement("script")
    script.src = import.meta.env.VITE_SQUARE_CDN
    script.async = true
    script.onload = () => {
      console.log("Square SDK loaded")
    }
    document.body.appendChild(script)

    return () => {
      document.body.removeChild(script)
    }
  }, [])

  return (
    <AppProvider>
      <ShareMeta />
      <AppRouter />
    </AppProvider>
  )
}

export default App
