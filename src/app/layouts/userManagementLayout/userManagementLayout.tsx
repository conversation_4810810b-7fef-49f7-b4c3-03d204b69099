import { Tab, Tabs } from "@mui/material"
import { useEffect, useMemo } from "react"
import { Outlet, useLocation, useNavigate } from "react-router-dom"
import { DashboardLayout, ListLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib"
import { TABS } from "./userManagementLayout.settings"
import { PATH } from "@/shared/config"

export const CampaignPage = () => {
  usePageTitle("User Management")
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    if (location.pathname === PATH.withAuth.userManagement.home) {
      navigate(TABS?.[0]?.path)
    }
  }, [location.pathname, navigate])

  const currentTab = useMemo(
    () =>
      TABS.find((tab) => {
        return location.pathname.includes(tab.path)
      })?.path || TABS?.[0]?.path,
    [location.pathname]
  )

  const handleTabChange = (_: React.SyntheticEvent, newPath: string) => {
    navigate(newPath)
  }

  const onBackClick = () => {
    navigate(-1)
  }

  return (
    <DashboardLayout>
      <ListLayout
        title="User Management"
        hideBackButton={false}
        onBackClick={onBackClick}
      >
        <div className="bg-white border-b">
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
          >
            {TABS.map((tab) => (
              <Tab key={tab.path} label={tab.label} value={tab.path} />
            ))}
          </Tabs>
        </div>
        <div className="pt-6">
          <Outlet />
        </div>
      </ListLayout>
    </DashboardLayout>
  )
}

export default CampaignPage
