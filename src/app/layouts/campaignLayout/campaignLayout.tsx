import { But<PERSON> } from "@/shared/ui"
import { Box, Tab, Tabs, Typography } from "@mui/material"
import { useMemo } from "react"
import { Outlet, useLocation, useNavigate, useParams } from "react-router-dom"

import { useGetCampaignById } from "@/entities/campaign"
import { useGetUserCampaigns } from "@/entities/userCampaign"
import { PATH } from "@/shared/config"
import { DashboardLayout, ListLayout } from "@/shared/layouts"
import { usePageTitle } from "@/shared/lib"
import {
  getInitialCampaignsRouteByRole,
  Roles,
  usePermissions,
  useUserStore,
} from "@/shared/model"
import { BackButton } from "@/shared/ui"
import { getTabsByRole } from "./campaignLayout.settings"

export const CampaignPage = () => {
  usePageTitle("Campaign")
  const { userInfo } = useUserStore()
  const { campaignId } = useParams<{ campaignId: string }>()
  const { data: userCampaigns } = useGetUserCampaigns()
  const navigate = useNavigate()
  const permissions = usePermissions()
  const location = useLocation()

  const { data: campaign, isLoading } = useGetCampaignById(Number(campaignId))

  const currentCampaign = useMemo(() => {
    return userCampaigns?.find(
      (campaign) => campaign.campaign_id === Number(campaignId)
    )
  }, [userCampaigns, campaignId])

  const handlePreviewClick = () => {
    const guid = campaign?.guid

    const userId = currentCampaign?.campaign_user_id
    const queryParams = new URLSearchParams()
    if (userId) queryParams.set("campaign_user_id", userId.toString())

    if (guid) {
      window.open(
        PATH.withoutAuth.donation.url({
          guid,
          queryParams: userId ? queryParams : undefined,
        }),
        "_blank"
      )
    }
  }

  const mappedTabs = useMemo(
    () =>
      getTabsByRole(userInfo?.role)
        .map(({ includePath, path, ...rest }) => {
          const pathUrl = path(":campaignId")
          const includePathUrl = path(":campaignId")
          const permPath = permissions?.[pathUrl]
          const permIncludePath = permissions?.[includePathUrl]
          const pathAllowed =
            permPath ||
            (typeof permPath === "object" &&
              (permPath as { read: boolean }).read)
          const includePathAllowed =
            permIncludePath ||
            (typeof permIncludePath === "object" &&
              (permIncludePath as { read: boolean }).read)

          if (!pathAllowed && !includePathAllowed) {
            return
          }

          return {
            ...rest,
            path: path(campaignId!),
            ...(includePath && { includePath: includePath(campaignId!) }),
          }
        })
        .filter((item) => {
          if (!userInfo) return false
          if (!item) return false
          return true
        }) as {
        includePath?: string | undefined
        path: string
        label: string
      }[],
    [campaignId, userInfo, permissions]
  )

  const currentTab = useMemo(
    () =>
      mappedTabs.find((tab) => {
        const basePath = tab!.includePath ?? tab!.path
        return location.pathname.includes(basePath)
      })?.path || mappedTabs?.[0]?.path,
    [location.pathname, mappedTabs]
  )

  const handleTabChange = (_: React.SyntheticEvent, newPath: string) => {
    navigate(newPath)
  }

  const onBackClick = () => {
    navigate(getInitialCampaignsRouteByRole(userInfo?.role ?? Roles.User))
  }

  const hideBackButton =
    !(userInfo?.userRoleId === 1 || userInfo?.userRoleId === 2) &&
    Number(userCampaigns?.length) <= 1

  const hidePreviewButton = !(
    userInfo?.userRoleId === 1 || userInfo?.userRoleId === 2
  )

  return (
    <DashboardLayout isLoading={isLoading}>
      <ListLayout
        title="Campaign"
        hideBackButton={hideBackButton}
        onBackClick={onBackClick}
        // actions={actions}
      >
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center">
            <Typography variant="h6">{campaign?.name}</Typography>
            <Box className="ml-2">
              <BackButton />
            </Box>
          </div>
          <div>
            {hidePreviewButton && (
              <Button
                variant="contained"
                isLoading={isLoading}
                onClick={handlePreviewClick}
                className="!ml-2"
              >
                Preview
              </Button>
            )}
          </div>
        </div>
        <div className="bg-white border-b">
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
          >
            {mappedTabs.map((tab) => (
              <Tab key={tab.path} label={tab.label} value={tab.path} />
            ))}
          </Tabs>
        </div>
        <div className="pt-6">
          <Outlet />
        </div>
      </ListLayout>
    </DashboardLayout>
  )
}

export default CampaignPage
