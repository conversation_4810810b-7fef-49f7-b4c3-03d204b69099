import { PATH } from "@/shared/config"
import { Roles } from "@/shared/model"

export const TABS: Record<
  "admin" | "user",
  {
    label: string
    includePath?: (campaignId: string | number) => string
    path: (campaignId: string | number) => string
  }[]
> = {
  admin: [
    { label: "Details", path: PATH.withAuth.campaign.details.url },
    {
      label: "Moments",
      path: PATH.withAuth.campaign.moments.url,
    },
    { label: "Groups", path: PATH.withAuth.campaign.groups.url },
    { label: "Members", path: PATH.withAuth.campaign.members.list.url },
    {
      label: "Invited Donors",
      path: PATH.withAuth.campaign.donationInvite.list.url,
    },
    {
      label: "Message Log",
      path: PATH.withAuth.campaign.messageLog.list.url,
    },
    { label: "Donations", path: PATH.withAuth.campaign.donations.list.url },
    {
      label: "SMS Templates",
      path: PATH.withAuth.campaign.smsTemplates.list.url,
    },
    {
      label: "Email Templates",
      path: PATH.withAuth.campaign.emailTemplates.list.url,
    },
    { label: "Drip Schedules", path: PATH.withAuth.campaign.dripSchedules.url },
    {
      label: "Donations Settings",
      includePath: PATH.withAuth.campaign.donationsSettings.url,
      path: PATH.withAuth.campaign.donationsSettings.fee.url,
    },
    {
      label: "Funds",
      path: PATH.withAuth.campaign.fund.list.url,
    },
  ],
  user: [
    { label: "Details", path: PATH.withAuth.campaign.details.url },
    { label: "Groups", path: PATH.withAuth.campaign.groups.url },
    { label: "Members", path: PATH.withAuth.campaign.members.list.url },
    {
      label: "Invited Donors",
      path: PATH.withAuth.campaign.donationInvite.list.url,
    },
    { label: "Donations", path: PATH.withAuth.campaign.donations.list.url },
    // NOTE: temporary hidden. (Post MVP feature)
    // {
    //   label: "Donation Message",
    //   path: PATH.withAuth.campaign.donationMessage.url,
    // },
    {
      label: "Media",
      path: PATH.withAuth.campaign.moments.url,
    },
  ],
}

export const getTabsByRole = (role?: Roles) => {
  switch (role) {
    case Roles.Admin:
    case Roles.SuperAdmin:
      return TABS.admin
    case Roles.User:
      return TABS.user
    default:
      return []
  }
}
