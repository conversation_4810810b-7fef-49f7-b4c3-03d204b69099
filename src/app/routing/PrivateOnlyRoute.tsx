import { can, generatePatternFromPath, PATH } from "@/shared/config"
import { Roles, usePermissions, useUserStore } from "@/shared/model"
import { useMemo } from "react"
import {
  matchPath,
  Navigate,
  Outlet,
  useLocation,
  useParams,
} from "react-router-dom"

const excludePaths: string[] = [
  PATH.withAuth.campaign.home.path,
  PATH.withAuth.selectCampaign,
  PATH.withAuth.selectGroup,
  PATH.withAuth.forbidden,
  PATH.withAuth.changePassword,
]

export const PrivateOnlyRoute = () => {
  const { userInfo } = useUserStore()
  //TODO: location needs for re render this component whe pathname change
  const location = useLocation()
  const permissions = usePermissions()

  const params = useParams()

  const pathName = location.pathname

  const path = useMemo(
    () => generatePatternFromPath(pathName, params),
    [params, pathName]
  )

  const isMatched = matchPath(
    { path: PATH.withAuth.campaign.home.path, end: false },
    pathName
  )

  if (!userInfo) {
    return <Navigate to={PATH.withoutAuth.login} replace />
  }

  const role = userInfo.role

  if (!excludePaths.includes(pathName) && role === Roles.User && !isMatched) {
    return <Navigate to={PATH.withAuth.selectCampaign} replace />
  }

  if (!can(role, path, permissions)) {
    console.error(`${role} is not authorized to see this page`)
    return <Navigate to={PATH.withAuth.forbidden} replace />
  }

  return <Outlet />
}
