import { defineConfig } from "steiger"
import fsd from "@feature-sliced/steiger-plugin"

export default defineConfig([
  ...fsd.configs.recommended,
  {
    files: ["./src/app/routing/router.tsx"],
    rules: {
      "fsd/no-public-api-sidestep": "off",
    },
  },
  {
    files: ["./src/shared/config/authorization/can.ts"],
    rules: {
      "fsd/no-public-api-sidestep": "off",
      "fsd/forbidden-imports": "off",
    },
  },
  // Temporary remove checking of insignificant slices, 05.04.2025, in 1-2 months remove this rule
  {
    files: ["./src/entities/**/*"],
    rules: {
      "fsd/insignificant-slice": "off",
      "fsd/excessive-slicing": "off",
    },
  },
  // Will be removed in refactor pages PR
  {
    files: ["./src/pages/**/*"],
    rules: {
      "fsd/excessive-slicing": "off",
    },
  },
])
