# Aktivate Frontend Client

## Table of Contents

- [Getting Started](#getting-started)
- [Docker Setup](#docker-setup)
- [Architecture](#architecture)

## Getting Started

### Local Development

1. Clone the repository:

```bash
<NAME_EMAIL>:aktivate/aktivate-fr-client.git
cd aktivate-fr-client
```

2. Create environment file:

```bash
cp .env.example .env
```

3. Install dependencies:

```bash
npm install
```

4. Start the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:5173`

### Running Tests

```bash
npm run test
```

## Deployment process

Branches to envs:

- dev -> production
- main -> none (will be changed in near future)

Auto deployment uses volume to reduce downtime on production. So after push it only builds an app into `dist/` folder and updates volume.
To deploy Frontend app from the scratch you need:

```bash
docker compose up --build -d
```

then add it to aktivate network

```bash
docker network connect aktivate_network fe-web-1
```

## Architecture

### Feature-Sliced Design

This project follows the Feature-Sliced Design (FSD) methodology, which is an architectural methodology for building frontend applications. [More about FSD](https://feature-sliced.github.io/documentation/)

#### Layer Responsibilities

- **app/**: Application setup, providers, store initialization
- **pages/**: Page components
- **widgets/**: Large reused parts of pages
- **features/**: Independent features with their own business logic. Use cases of entities
- **entities/**: Business entities (models, basic CRUD)
- **shared/**: Shared utils, UI components, API clients

### Tech Stack

- **Framework**: React
- **Build Tool**: Vite
- **State Management**: React Query
- **Styling**: Tailwind CSS + Material-UI
- **Type Checking**: TypeScript
- **Testing**: Jest
- **API Client**: Axios
