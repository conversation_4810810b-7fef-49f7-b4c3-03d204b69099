services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=${NODE_ENV:-production}
    ports:
      - "1516:8081"
    env_file:
      - .env
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_USE_MOCK_API=${VITE_USE_MOCK_API:-false}
      - VITE_STRIPE_PUBLIC_KEY=${VITE_STRIPE_PUBLIC_KEY}
    volumes:
      - web-dist:/app/dist
    networks:
      - aktivate_network 

volumes:
  web-dist:

networks:
  aktivate_network:
    external: true